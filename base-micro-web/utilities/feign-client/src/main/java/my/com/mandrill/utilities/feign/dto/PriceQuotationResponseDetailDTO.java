package my.com.mandrill.utilities.feign.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PriceQuotationResponseDetailDTO implements Serializable {

	private String id;

	private String refCode;

	private String quotationNo;

	private String insurerCode;

	private String insurerSumInsuredValue;

	private String generalPrice;

	private String ncdDetails;

	private String extraCoverage;

	private Boolean coverNoteAllowed;

	private String roadTaxAvailability;

	private String roadTaxErrorDescription;

	private String vehicleNo;

}
