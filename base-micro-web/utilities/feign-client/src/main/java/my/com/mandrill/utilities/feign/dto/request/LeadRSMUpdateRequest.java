package my.com.mandrill.utilities.feign.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.RSMStatus;

import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeadRSMUpdateRequest {

	private String id;

	private Set<String> ids;

	@NotNull
	private RSMStatus status;

	private boolean commissionAttached;

}
