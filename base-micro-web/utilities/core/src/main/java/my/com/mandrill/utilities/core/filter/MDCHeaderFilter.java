package my.com.mandrill.utilities.core.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Map;

@Component
public class MDCHeaderFilter extends OncePerRequestFilter {

	private static final Map<String, String> HEADER_TO_MDC = Map.of(RequestUtil.CLIENT_PLATFORM,
			RequestUtil.CLIENT_PLATFORM);

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hai<PERSON> filter<PERSON>hain)
			throws ServletException, IOException {
		try {
			for (Map.Entry<String, String> entry : HEADER_TO_MDC.entrySet()) {
				MDC.put(entry.getValue(), StringUtils.defaultIfEmpty(request.getHeader(entry.getKey()), null));
			}

			filterChain.doFilter(request, response);

		}
		finally {
			for (String mdcKey : HEADER_TO_MDC.values()) {
				MDC.remove(mdcKey);
			}
		}
	}

}
