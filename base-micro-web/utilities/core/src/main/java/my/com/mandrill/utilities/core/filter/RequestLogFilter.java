package my.com.mandrill.utilities.core.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.util.TraceContextHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class RequestLogFilter extends OncePerRequestFilter {

	@Autowired
	private TraceContextHelper traceContextHelper;

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {

		ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
		ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);

		long startTime = System.currentTimeMillis();

		try {
			filterChain.doFilter(wrappedRequest, wrappedResponse);
		}
		finally {
			if (log.isInfoEnabled() && !shouldSkipLogging(request.getRequestURI())) {
				long duration = System.currentTimeMillis() - startTime;

				String requestBody = new String(wrappedRequest.getContentAsByteArray(), StandardCharsets.UTF_8);
				String responseBody = new String(wrappedResponse.getContentAsByteArray(), StandardCharsets.UTF_8);

				log.info("""
						[HTTP] %s %s | %d ms
						- RequestId: %s
						- Platform: %s
						- Headers: %s
						- Request Body: %s
						- Response Body: %s
						""".formatted(request.getMethod(), request.getRequestURI(), duration,
						traceContextHelper.getRequestId(), traceContextHelper.getPlatformClient(),
						request.getHeaderNames().asIterator().hasNext() ? request.getHeaderNames().toString() : "-",
						requestBody, responseBody));

			}
			wrappedResponse.copyBodyToResponse();
		}
	}

	private boolean shouldSkipLogging(String uri) {
		return uri.startsWith("/v3/api-docs") || uri.startsWith("/swagger-ui") || uri.startsWith("/swagger-resources");
	}

}
