package my.com.mandrill.utilities.general.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "google")
public class GoogleProperties {

	private final Recaptcha recaptcha = new Recaptcha();

	@Getter
	@Setter
	public static class Recaptcha {

		private String baseUrl;

		private String secretKey;

		private String apiKey;

		private String siteKeyWeb;

		private String siteKeyAndroid;

		private String siteKeyIos;

		private String projectId;

	}

}
