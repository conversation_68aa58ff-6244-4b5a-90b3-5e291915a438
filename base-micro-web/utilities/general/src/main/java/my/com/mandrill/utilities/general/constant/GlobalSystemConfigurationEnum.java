package my.com.mandrill.utilities.general.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum GlobalSystemConfigurationEnum {

	IOS_MINIMUM_VERSION("IOS_MINIMUM_VERSION", "IOS Minimum Version", "0.0.0"),
	ANDROID_MINIMUM_VERSION("ANDROID_MINIMUM_VERSION", "Android Minimum Version", "0.0.0"),
	HUAWEI_MINIMUM_VERSION("HUAWEI_MINIMUM_VERSION", "Huawei Minimum Version", "0.0.0"),
	GET_ACCOUNT_REQUEST_OTP_MINIMUM_VERSION("GET_ACCOUNT_REQUEST_OTP_MINIMUM_VERSION",
			"Minimum version of GET Account Request OTP API", "0.0.0"),
	GET_ACCOUNT_RESET_PASSWORD_EMAIL_MINIMUM_VERSION("GET_ACCOUNT_RESET_PASSWORD_EMAIL_MINIMUM_VERSION",
			"Minimum version of GET Account Reset Password Email API", "0.0.0"),
	NUMBER_OF_TARGETED_USER_REGISTERED_CHART("NUMBER_OF_TARGETED_USER_REGISTERED_CHART",
			"Number of Targeted User Registered Chart", null),
	ENABLED_DEVICE_BINDING("ENABLED_DEVICE_BINDING", "to enable/disable device binding", "false"),
	MAXIMUM_DEVICE_PER_USER("MAXIMUM_DEVICE_PER_USER", "Maximum number of device per user", "10"),
	MAXIMUM_USER_PER_DEVICE("MAXIMUM_USER_PER_DEVICE", "Maximum number of user per device", "10"),
	DEVICE_BINDING_DAY_DURATION("DEVICE_BINDING_DAY_DURATION", "Number of day the device is bound", "7"),
	ACCESS_TOKEN_VALIDITY_IN_SECOND("ACCESS_TOKEN_VALIDITY_IN_SECOND",
			"Maximum time validity of access token in seconds", "3600"),
	REFRESH_TOKEN_VALIDITY_IN_SECOND("REFRESH_TOKEN_VALIDITY_IN_SECOND",
			"Maximum time validity of refresh token in seconds", "604800"),
	REFRESH_TOKEN_VALIDITY_REMEMBER_ME_IN_SECOND("REFRESH_TOKEN_VALIDITY_REMEMBER_ME_IN_SECOND",
			"Maximum time validity of refresh token with remember me in seconds", "2592000"),
	SESSION_MANAGEMENT_ENABLED("SESSION_MANAGEMENT_ENABLED", "Enable session management", "false"),
	INFOBIP_SMS_ENABLED("INFOBIP_SMS_ENABLED", "Infobip SMS enabled", "false"),
	INFOBIP_WHATSAPP_FLOW_ENABLED("INFOBIP_WHATSAPP_FLOW_ENABLED", "Infobip WhatsApp flow enabled", "false"),
	INFOBIP_PUSH_NOTIFICATION_ENABLED("INFOBIP_PUSH_NOTIFICATION_ENABLED", "Check is push notification infobip enabled",
			"false"),
	INFOBIP_EMAIL_ENABLED("INFOBIP_EMAIL_ENABLED", "Email infobip enabled", "false"),
	REFERRAL_CODE_SIZE("REFERRAL_CODE_SIZE", "Size of referral code", "5"),
	RSM_WITHDRAWAL_ADMIN_FEE("RSM_WITHDRAWAL_ADMIN_FEE", "RSM Point Withdrawal Admin Fee", "500"),
	RSM_WITHDRAWAL_MIN_POINT("RSM_WITHDRAWAL_MIN_POINT", "RSM Point Withdrawal Minimum Point", "5000"),
	RSM_WITHDRAWAL_ADMIN_FEE_ENABLED("RSM_WITHDRAWAL_ADMIN_FEE_ENABLED", "RSM Point Withdrawal Admin Fee Enabled",
			"false"),
	INFOBIP_LIVECHAT_ENABLED("INFOBIP_LIVECHAT_ENABLED", "Live chat enabled", "false"),
	REFERRAL_WITHDRAWAL_ENABLED("REFERRAL_WITHDRAWAL_ENABLED", "Enable referral withdrawal", "false"),
	WHITELISTED_COUNTRY_CODE_FOR_REQUEST_OTP("WHITELISTED_COUNTRY_CODE_FOR_REQUEST_OTP",
			"Whitelist country code for request OTP, separated by comma", ""),
	RSM_B2B2C_LINKAGE_ENABLED("RSM_B2B2C_LINKAGE_ENABLED", "RSM B2B2C Relation Linkage Enabled", "false"),
	KEY_REQUEST_VALID_HOURS("KEY_REQUEST_VALID_HOURS", "Key Request Valid In Hours", "1"),
	NPS_DETRACTOR_UPPER_LIMIT("NPS_DETRACTOR_UPPER_LIMIT", "Detractor upper limit", "6"),
	NPS_PASSIVE_UPPER_LIMIT("NPS_PASSIVE_UPPER_LIMIT", "Passive upper limit", "8"),
	NPS_PROMOTER_UPPER_LIMIT("NPS_PROMOTER_UPPER_LIMIT", "Promoter upper limit", "10"),
	TOKEN_TRANSACTION_ENABLED("TOKEN_TRANSACTION_ENABLED", "Token Transaction Enabled", "false"),
	RECAPTCHA_ENABLED("RECAPTCHA_ENABLED", "Recaptcha Enabled", "false"),
	RECAPTCHA_MINIMUM_SCORE("RECAPTCHA_MINIMUM_SCORE", "Recaptcha Minimum Score range 1 - 10", "5"),
	INBOX_ARCHIVE_RETENTION_DAYS("INBOX_ARCHIVE_RETENTION_DAYS", "Inbox Archive Retention Days", "30"),
	RSM_POINT_EXPIRY_DURATION("RSM_POINT_EXPIRY_DURATION", "RSM Point Expiry Duration In Months", "24");

	private final String code;

	private final String description;

	private final String value;

	GlobalSystemConfigurationEnum(String code, String description, String value) {
		this.code = code;
		this.description = description;
		this.value = value;
	}

	public static Optional<GlobalSystemConfigurationEnum> filterByCode(String code) {

		return Arrays.stream(GlobalSystemConfigurationEnum.class.getEnumConstants())
				.filter(systemConfigurationEnum -> systemConfigurationEnum.getCode().equals(code)).findFirst();
	}

	public Long getLong() {
		return Long.parseLong(this.value);
	}

	public Integer getInteger() {
		return Integer.parseInt(this.value);
	}

	@Override
	public String toString() {
		return "SystemConfigurationEnum{" + "code='" + code + '\'' + ", description='" + description + '\'' + ", value="
				+ value + '}';
	}

}
