package my.com.mandrill.utilities.general.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiProductHighlightResponse implements Serializable {

	@JsonProperty("default")
	private List<String> defaultHighlights;

	@JsonProperty("suggested")
	private List<String> suggestedHighlights;

}
