package my.com.mandrill.utilities.general.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiProductResponse implements Serializable {

	@JsonProperty("card_type")
	private String cardType;

	private String entity;

	private String name;

	@JsonProperty("product_id")
	private String productId;

	private String type;

	private String logo;

	private String issuerCode;

	private String issuerType;

	private String description;

	private String productLink;

	private String applyLink;

	@JsonProperty("highlights")
	private AiProductHighlightResponse highlights;

}
