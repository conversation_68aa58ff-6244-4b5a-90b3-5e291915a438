package my.com.mandrill.utilities.general.constant;

public class <PERSON><PERSON><PERSON><PERSON> {

	public static final String GLOBAL_SYSTEM_CONFIGURATION = "global:system:configuration";

	public static final String SYSTEM_CONFIGURATION = "system:configuration";

	public static final String HOME_MENU_MAP = "home-menu-map";

	public static final String BLOG_LISTING = "blog-listing-%s%s%s%d";

	public static final String BLOG_DETAIL = "blog-detail-%s";

	public static final String RSM_WITHDRAWAL_TRANSACTION_CACHE_KEY = "rsm-withdrawal-transaction-cache-{%s}";

	public static final String REQUEST_OTP_CONCURRENT_CACHE_KEY = "request-otp-concurrent-{%s:%s}";

	public static final String USER_BY_LOGIN_CACHE = "USER_BY_LOGIN_CACHE";

	public static final String USER_DETAIL_PERMISSION_LOGIN_CACHE = "USER_DETAIL_PERMISSION_LOGIN_CACHE";

	public static final String USER_REF_NO_CACHE = "USER_REF_NO_CACHE";

	public static final String APP_USER_BY_REF_NO = "appuser:refno:v1";

	public static final String APP_USER_BY_PHONE_NUMBER = "appuser:phonenumber:v1";

	public static final String APP_USER_BY_EMAIL = "appuser:email:v1";

	public static final String USER_BY_ID_CACHE = "USER_BY_ID_CACHE";

	public static final String USER_SHORT_INFO_CACHE = "USER_SHORT_INFO_CACHE";

	public static final String COUNTRY_CACHE = "country:cache";

	public static final String STATE_CACHE = "state:cache";

	public static final String LOGIN_CONCURRENT_CACHE_KEY = "login-concurrent-{%s:%s}";

	public static final String CACHE_FORMAT = "%s::%s";

	public static final String PUBLIC_AUTHENTICATION_CACHE_KEY = "public-authentication";

	public static final String ACTIVE_ADVERTISEMENTS = "advertisements:active:%s:%s:%s";

	public static final String SURVEY_ACTIVE_FORM = "survey:active-form:%s";

	public static final String PRODUCT_GROUPS_CACHE = "product-groups:cache";

	public static final String PRODUCT_TYPE_CACHE = "product-types:cache:%s:%s:%s";

	public static final String PUBLIC_AUTHENTICATION_MODULE_CACHE_KEY = "public-module-authentication:v1:%s";

	private CacheKey() {
	}

}
