package my.com.mandrill.utilities.general.util;

import io.micrometer.tracing.Span;
import io.micrometer.tracing.TraceContext;
import io.micrometer.tracing.Tracer;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class TraceContextHelper {

	private final Tracer tracer;

	public String getRequestId() {
		return Optional.ofNullable(tracer.currentSpan()).map(Span::context).map(TraceContext::traceId).orElse(null);
	}

	public String getPlatformClient() {
		return MDC.get(RequestUtil.CLIENT_PLATFORM);
	}

}
