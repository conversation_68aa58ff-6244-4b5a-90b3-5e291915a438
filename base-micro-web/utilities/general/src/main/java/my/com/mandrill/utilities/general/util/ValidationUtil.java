package my.com.mandrill.utilities.general.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class ValidationUtil {

	private ValidationUtil() {
	}

	public static boolean isValidWebPlatformPattern(String input) {
		if (input == null || input.isBlank())
			return false;

		String[] parts = input.split(";");
		if (parts.length != 4)
			return false;

		String type = parts[0];
		String browserName = parts[1];
		String ip = parts[2];
		String osName = parts[3];

		return isValidIp(ip) && !type.isBlank() && !browserName.isBlank() && !osName.isBlank();
	}

	public static boolean isValidIp(String ip) {
		try {
			InetAddress inet = InetAddress.getByName(ip);
			return inet.getHostAddress().equals(ip);
		}
		catch (UnknownHostException e) {
			return false;
		}
	}

}
