package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public enum EntityName {

	VEHICLE("VEHICLE", "Vehicle", false), PROPERTY("PROPERTY", "Property", false),
	PROPERTY_STAGING("PROPERTY_STAGING", "Property Staging", false), UTILITY("UTILITY", "Utility", false),
	BANK("BANK", "Bank", false), CREDIT_CARD("CREDIT_CARD", "Know Your Card", true),
	INSURANCE("INSURANCE", "Insurance", false), LOAN("LOAN", "Loan", false), BANKLIST("BANKLIST", "Bank List", false),
	USER("USER", "User", false), LEGAL("LEGAL", "Legal", false),
	GET_TO_KNOW_CC("GET_TO_KNOW_CC", "Get To Know Credit Card", false),
	ADVERTISEMENT("ADVERTISEMENT", "Advertisement", false),
	KNOW_YOUR_LOAN_LIMIT("KNOW_YOUR_LOAN_LIMIT", "KNOW_YOUR_LOAN_LIMIT", false),
	LOAN_LIMIT("LOAN_LIMIT", "Know Your Loan Limit", true), BANNER("BANNER", "Banner", false),
	FINOLOGY_VEHICLE_INSURANCE("FINOLOGY_VEHICLE_INSURANCE", "Finology Insurance", false),
	M_AND_A_INTEREST("M_AND_A_INTEREST", "M&A Interest", false), SOLAROO("SOLAROO", "Solaroo", false),
	EWILL("EWILL", "eWill", false), STASHAWAY("STASHAWAY", "StashAway", false),
	AHAM_CAPITAL("AHAM_CAPITAL", "AHAM Capital", false), JOMHIBAH("JOMHIBAH", "JOMHIBAH", false),
	SINEGY_DAX("SINEGY_DAX", "Sinegy Dax", false), CREDIT_BUREAU("CREDIT_BUREAU", "Credit Bureau", false),
	EXPENSE("EXPENSE", "Expense", false), EASIWILL("EASIWILL", "Easi Will", false), CTOS("CTOS", "CTOS", false),
	INCOME("INCOME", "Income", false), VAULT("VAULT", "Vault", false), E_ACCESS("E_ACCESS", "E-Access", false),
	ATX("ATX", "ATX", false), MILIEU_SOLAR("MILIEU_SOLAR", "MilieuSolar", false), PAYMENT("PAYMENT", "Payment", false),
	MODULE("MODULE", "Module", true), REDIRECT("REDIRECT", "Redirect", true),
	CGS_INT_MY("CGS_INT_MY", "CGS Int MY", false), INVESTMENT("INVESTMENT", "INVESTMENT", false),
	SAVING_GOAL("SAVING_GOAL", "Saving Goal", false), DWS("DWS", "DWS", false),
	UP_BY_CGS_MY("UP_BY_CGS_MY", "UP by CGS MY", false), TRUELY_ESIM("TRUELY_ESIM", "Truely eSIM", false),

	/**
	 * is used for the search the product from AI side, for AdvertisementAdditionalData
	 * table
	 */
	PRODUCT("PRODUCT", "Ai Product", false),
	/**
	 * use it as product list to output the list of product that are available, for
	 * AdvertisementPredefinedProduct table. For Alliance Bank Campaign, is a requirement
	 * to make some card to display because some card are unavailable currently.
	 */
	PREDEFINED_PRODUCT("PREDEFINED_PRODUCT", "Products", true), FSM_ONE("FSM_ONE", "FSMOne", false),
	APEX_SECURITIES("APEX_SECURITIES", "Apex Securities", false);

	public static final List<EntityName> AFFILIATE_LINKS_REPORT_ENTITY_NAME = List.of(EASIWILL, EWILL, CREDIT_BUREAU,
			CTOS, MILIEU_SOLAR, M_AND_A_INTEREST, SOLAROO, STASHAWAY, AHAM_CAPITAL, JOMHIBAH, SINEGY_DAX, CGS_INT_MY,
			DWS, FSM_ONE, UP_BY_CGS_MY, APEX_SECURITIES);

	private final String code;

	private final String name;

	private final boolean isCampaign;

	public EntityName.EntityNameDTO getObject() {
		return EntityNameDTO.builder().code(code).name(name).isCampaign(isCampaign).build();
	}

	@AllArgsConstructor
	@Data
	@Builder
	public static class EntityNameDTO {

		private String code;

		private String name;

		private boolean isCampaign;

	}

}
