package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KafkaTopicConfig {

	public static final String GROUP = "bank";

	private final BaseProperties baseProperties;

	public static final String CALCULATE_LOAN_MONTHLY_TOPIC = "calculate-loan-monthly";

	@Bean
	public NewTopic calculateLoanMonthly() {
		return TopicBuilder.name(CALCULATE_LOAN_MONTHLY_TOPIC).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

}
