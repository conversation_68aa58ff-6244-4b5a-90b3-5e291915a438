package my.com.mandrill.component.service.impl;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.domain.UserInterestRecordAddon;
import my.com.mandrill.component.domain.UserInterestRecordVault;
import my.com.mandrill.component.dto.model.UserInterestRecordVaultCountDTO;
import my.com.mandrill.component.dto.model.UserInterestedRedirectDTO;
import my.com.mandrill.component.dto.request.ReportServiceRequest;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.UserInterestRecordAddonRepository;
import my.com.mandrill.component.repository.jpa.UserInterestRecordRepository;
import my.com.mandrill.component.repository.jpa.UserInterestRecordVaultRepository;
import my.com.mandrill.component.service.UserInterestRecordService;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.constant.UserInterestProductTypeEnum;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class UserInterestRecordServiceImpl implements UserInterestRecordService {

	private final UserInterestRecordRepository userInterestRecordRepository;

	private final UserInterestRecordVaultRepository userInterestRecordVaultRepository;

	private final UserInterestRecordAddonRepository userInterestRecordAddonRepository;

	@Override
	public Page<UserInterestRecord> findAll(ReportServiceRequest request) {
		Page<UserInterestRecord> interestRecords = userInterestRecordRepository
				.findAll((root, query, builder) -> getFilterReport(request, builder, root), request.getPageable());
		List<String> userInterestRecordIds = interestRecords.stream().map(AuditSection::getId).toList();

		if (!userInterestRecordIds.isEmpty()) {
			final Map<String, Long> userInterestVaultCount = findVaultCountByRecordIds(userInterestRecordIds);

			List<UserInterestRecordAddon> addons = userInterestRecordAddonRepository
					.findByUserInterestRecordIdIn(userInterestRecordIds);
			final Map<String, Set<UserInterestRecordAddon>> addonsMap = addons.stream()
					.collect(Collectors.groupingBy(v -> v.getUserInterestRecord().getId(), Collectors.toSet()));

			interestRecords = interestRecords.map(v -> {
				v.setVaultCount(userInterestVaultCount.getOrDefault(v.getId(), 0L));
				v.setAddons(addonsMap.getOrDefault(v.getId(), new HashSet<>()));
				return v;
			});
		}

		return interestRecords;
	}

	@Override
	public Map<String, Long> findVaultCountByRecordIds(List<String> recordIds) {
		if (CollectionUtils.isEmpty(recordIds)) {
			return Collections.emptyMap();
		}

		List<UserInterestRecordVaultCountDTO> vaultCounts = userInterestRecordVaultRepository
				.countByUserInterestRecordId(recordIds);
		return vaultCounts.stream().collect(Collectors.toMap(UserInterestRecordVaultCountDTO::getUserInterestRecordId,
				UserInterestRecordVaultCountDTO::getCount));
	}

	@Transactional
	@Override
	public void bulkUpdate(LeadRSMUpdateRequest request) {
		if (userInterestRecordRepository.existsAllByIdInAndRsmStatusAndRsmCommissionAttachedIsTrue(request.getIds(),
				RSMStatus.SUCCESS)) {
			throw new BusinessException(ErrorCodeGlobalEnum.CANNOT_PERFORM_BULK_UPDATE_BECAUSE_SOME_SUCCESS);
		}
		userInterestRecordRepository.bulkUpdateStatusByIdIn(request.getStatus(), request.getIds());
	}

	@Override
	public List<UserInterestRecord> findReportData(ReportServiceRequest request) {
		return userInterestRecordRepository.findAll((root, query, builder) -> getFilterReport(request, builder, root));
	}

	private Predicate getFilterReport(ReportServiceRequest request, CriteriaBuilder builder,
			Root<UserInterestRecord> root) {
		List<Predicate> predicates = new ArrayList<>();
		if (Objects.nonNull(request.getIsRedirect())) {
			predicates.add(builder.equal(root.get("isRedirect"), request.getIsRedirect()));
		}
		if (CollectionUtils.isNotEmpty(request.getIssuerCodes())) {
			predicates.add(root.get("issuerCode").in(request.getIssuerCodes()));
		}

		if (Objects.nonNull(request.getDateType()) && Objects.nonNull(request.getStartDate())
				&& Objects.nonNull(request.getEndDate())) {
			predicates.add(builder.greaterThan(root.get(request.getDateType().getFieldName()),
					request.getStartDate().atStartOfDay().atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()));
			predicates.add(builder.lessThan(root.get(request.getDateType().getFieldName()),
					request.getEndDate().atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()));
		}
		else {
			if (Objects.nonNull(request.getStartDate())) {
				final Instant startTimeFilter = request.getStartDate().atStartOfDay()
						.atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				predicates.add(builder.greaterThan(root.get("createdDate"), startTimeFilter));
			}

			if (Objects.nonNull(request.getEndDate())) {
				final Instant endTimeFilter = request.getEndDate().atTime(LocalTime.MAX)
						.atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				predicates.add(builder.lessThan(root.get("createdDate"), endTimeFilter));
			}
		}

		if (StringUtils.isNotBlank(request.getFullName())) {
			predicates.add(
					builder.like(builder.lower(root.get("fullName")), "%" + request.getFullName().toLowerCase() + "%"));
		}
		if (StringUtils.isNotBlank(request.getProductType())) {
			predicates.add(builder.equal(root.get("productType"), request.getProductType()));
		}
		if (CollectionUtils.isNotEmpty(request.getProductIds())) {
			predicates.add(root.get("productId").in(request.getProductIds()));
		}
		if (CollectionUtils.isNotEmpty(request.getProviderIds())) {
			predicates.add(root.get("providerId").in(request.getProviderIds()));
		}
		if (StringUtils.isNotEmpty(request.getProductName())) {
			predicates.add(builder.like(builder.lower(root.get("productName")),
					"%" + request.getProductName().toLowerCase() + "%"));
		}
		if (StringUtils.isNotBlank(request.getRefNo())) {
			predicates.add(builder.like(root.get("refNo"), "%" + request.getRefNo() + "%"));
		}
		if (StringUtils.isNotBlank(request.getUserRefNo())) {
			predicates.add(builder.like(root.get("userRefNo"), "%" + request.getUserRefNo() + "%"));
		}
		if (CollectionUtils.isNotEmpty(request.getRsmRelation())) {
			predicates.add(root.get("rsmRelation").in(request.getRsmRelation()));
		}
		if (CollectionUtils.isNotEmpty(request.getProductTypes())) {
			predicates.add(root.get("productType").in(request.getProductTypes()));
		}
		if (CollectionUtils.isNotEmpty(request.getRsmStatus())) {
			predicates.add(root.get("rsmStatus").in(request.getRsmStatus()));
		}
		if (CollectionUtils.isNotEmpty(request.getRsmCommissionAttached())) {
			predicates.add(root.get("rsmCommissionAttached").in(request.getRsmCommissionAttached()));
		}
		if (CollectionUtils.isNotEmpty(request.getRsmEligible())) {
			predicates.add(root.get("rsmEligible").in(request.getRsmEligible()));
		}
		return builder.and(predicates.toArray(new Predicate[0]));
	}

	@Override
	public UserInterestRecord findByIdAndIssuerCodes(String id, Set<String> issuerCodes) {
		return userInterestRecordRepository.findByIdAndIssuerCodeIn(id, issuerCodes)
				.orElseThrow(ExceptionPredicate.userInterestNotFound(id));
	}

	@Override
	public Optional<UserInterestRecord> findFirstByUserIdAndProductIdAndIsRedirectFalseOrderByReapplyDateDesc(
			String userId, String productId) {
		return userInterestRecordRepository
				.findFirstByUserIdAndProductIdAndIsRedirectFalseOrderByReapplyDateDesc(userId, productId);
	}

	@Override
	@Transactional
	public UserInterestRecord save(UserInterestRecord userInterestRecord) {
		return userInterestRecordRepository.save(userInterestRecord);
	}

	@Override
	public List<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			Instant startDate, Instant endDate) {
		return userInterestRecordRepository
				.findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(startDate, endDate);
	}

	@Override
	public Page<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			Instant startDate, Instant endDate, Pageable pageable) {
		return userInterestRecordRepository
				.findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(startDate, endDate,
						pageable);
	}

	@Override
	public List<UserInterestRecord> findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			List<String> institutions, Instant startDate, Instant endDate) {
		return userInterestRecordRepository
				.findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
						institutions, startDate, endDate);
	}

	@Override
	public Page<UserInterestRecord> findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			List<String> institutions, Instant startDate, Instant endDate, Pageable pageable) {
		return userInterestRecordRepository
				.findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
						institutions, startDate, endDate, pageable);
	}

	@Override
	public List<UserInterestedRedirectDTO> getAllRedirectTrueRecordBetweenDate(Instant startDateTime,
			Instant endDateTime) {
		return userInterestRecordRepository
				.findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectTrueOrderByCreatedDateAsc(
						startDateTime, endDateTime)
				.stream().map(this::toDTO).toList();
	}

	@Override
	public Page<UserInterestedRedirectDTO> countAllBetweenDateAndIsRedirectTrue(Instant dateStart, Instant dateEnd,
			Pageable pageable) {
		return userInterestRecordRepository.findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectTrue(
				dateStart, dateEnd, pageable).map(this::toDTO);
	}

	@Override
	public Long countBetweenDateAndIsRedirectTrue(Instant dateStart, Instant dateEnd) {
		return userInterestRecordRepository.countBetweenDateAndIsRedirect(dateStart, dateEnd, true);
	}

	@Override
	public Long countBetweenDateAndIsRedirectFalse(Instant dateStart, Instant dateEnd) {
		return userInterestRecordRepository.countBetweenDateAndIsRedirect(dateStart, dateEnd, false);
	}

	/**
	 * Returns a {@code UserInterestRecord} for a given product. If a record is present,
	 * this means that the user has registered their interest for a certain product. If
	 * null, this means that the user has either never registered their interest for a
	 * certain product, or that the user has previously registered their interest for a
	 * CREDIT_CARD product type, but the application has since expired (2 week expiry).
	 *
	 * @see my.com.mandrill.component.service.UserInterestRecordIntegrationService#insert
	 */
	@Override
	public UserInterestRecord getRecordByProductId(String userId, String productId) {
		Optional<UserInterestRecord> userInterestRecord = userInterestRecordRepository
				.findFirstByUserIdAndProductIdAndIsRedirectFalseOrderByReapplyDateDesc(userId, productId);
		if (userInterestRecord.isEmpty()) {
			return null;
		}

		UserInterestRecord userInterestRecordUnwrapped = userInterestRecord.get();
		UserInterestProductTypeEnum productType = UserInterestProductTypeEnum
				.fromCode(userInterestRecordUnwrapped.getProductType()).orElse(null);
		if (productType == null)
			return userInterestRecordUnwrapped;

		switch (productType) {
			case CREDIT_CARD -> {
				// loongyeat: If user has re-applied a card and the reapplyDate
				// has not yet passed, return a record to FE to disable the
				// "Apply Now" button.
				Instant reapplyDate = userInterestRecordUnwrapped.getReapplyDate();
				if (reapplyDate != null && Instant.now().isBefore(reapplyDate)) {
					return userInterestRecordUnwrapped;
				}

				return null;
			}
			case CURRENT_ACCOUNT, BUSINESS_LOAN, CAR_INSURANCE -> {
				// loongyeat: Allow re-application as many times for these cases
				return null;
			}
			default -> {
				return userInterestRecordUnwrapped;
			}
		}

	}

	@Override
	public long countAllianceBankCampaign(Instant dateStart, Instant dateEnd, String issuerCode, String productType,
			UserInterestedSource source) {
		return (userInterestRecordRepository.countByIssuerCodeAndProductTypeAndSourceAndCreatedDateBetween(issuerCode,
				productType, source, dateStart, dateEnd));
	}

	@Override
	public List<UserInterestRecordVault> findVaultByRecordId(String recordId) {
		return userInterestRecordVaultRepository.findByUserInterestRecordId(recordId);
	}

	private UserInterestedRedirectDTO toDTO(UserInterestRecord userInterestRecord) {
		UserInterestedRedirectDTO userInterestedRedirectDTO = new UserInterestedRedirectDTO();
		userInterestedRedirectDTO.setCreatedDate(userInterestRecord.getCreatedDate()
				.atZone(TimeConstant.DEFAULT_ZONE_ID).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		userInterestedRedirectDTO.setIssuerType(userInterestRecord.getIssuerType());
		userInterestedRedirectDTO.setProvider(userInterestRecord.getProviderId());
		userInterestedRedirectDTO.setProductType(userInterestRecord.getProductType());
		userInterestedRedirectDTO.setProductName(userInterestRecord.getProductName());
		userInterestedRedirectDTO.setFullName(userInterestRecord.getFullName());
		userInterestedRedirectDTO.setPhoneNumber(userInterestRecord.getPhoneNumber());
		userInterestedRedirectDTO.setEmail(userInterestRecord.getEmail());
		userInterestedRedirectDTO.setUserRefNo(userInterestRecord.getUserRefNo());
		return userInterestedRedirectDTO;
	}

	@Override
	public UserInterestRecord findById(String id) {
		return userInterestRecordRepository.findById(id).orElseThrow(ExceptionPredicate.userInterestNotFound(id));
	}

	@Override
	public Set<String> findAllApplicationIdsExcludingUserIdsIn(Set<String> applicationIds, Set<String> userIds) {
		if (applicationIds.isEmpty())
			return Set.of();

		return userInterestRecordRepository.findAllById(applicationIds).stream()
				.filter(u -> !userIds.contains(u.getUserId())).map(UserInterestRecord::getId)
				.collect(Collectors.toSet());
	}

}
