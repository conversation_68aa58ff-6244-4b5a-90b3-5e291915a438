package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.domain.UserInterestRecordVault;
import my.com.mandrill.component.dto.model.UserInterestedRedirectDTO;
import my.com.mandrill.component.dto.request.ReportServiceRequest;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface UserInterestRecordService {

	UserInterestRecord getRecordByProductId(String userId, String productId);

	Optional<UserInterestRecord> findFirstByUserIdAndProductIdAndIsRedirectFalseOrderByReapplyDateDesc(String userId,
			String productId);

	UserInterestRecord save(UserInterestRecord userInterestRecord);

	List<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			Instant startDate, Instant endDate);

	Page<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			Instant startDate, Instant endDate, Pageable pageable);

	List<UserInterestRecord> findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			List<String> institutions, Instant startDate, Instant endDate);

	List<UserInterestRecord> findReportData(ReportServiceRequest request);

	Page<UserInterestRecord> findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			List<String> institutions, Instant startDate, Instant endDate, Pageable pageable);

	Page<UserInterestedRedirectDTO> countAllBetweenDateAndIsRedirectTrue(Instant dateStart, Instant dateEnd,
			Pageable pageable);

	Long countBetweenDateAndIsRedirectTrue(Instant dateStart, Instant dateEnd);

	Long countBetweenDateAndIsRedirectFalse(Instant dateStart, Instant dateEnd);

	long countAllianceBankCampaign(Instant dateStart, Instant dateEnd, String issuerCode, String productType,
			UserInterestedSource source);

	Page<UserInterestRecord> findAll(ReportServiceRequest request);

	UserInterestRecord findByIdAndIssuerCodes(String id, Set<String> issuerCodes);

	List<UserInterestRecordVault> findVaultByRecordId(String recordId);

	List<UserInterestedRedirectDTO> getAllRedirectTrueRecordBetweenDate(Instant startDateTime, Instant endDateTime);

	UserInterestRecord findById(String id);

	Set<String> findAllApplicationIdsExcludingUserIdsIn(Set<String> applicationIds, Set<String> userIds);

	Map<String, Long> findVaultCountByRecordIds(List<String> recordIds);

	void bulkUpdate(LeadRSMUpdateRequest request);

}
