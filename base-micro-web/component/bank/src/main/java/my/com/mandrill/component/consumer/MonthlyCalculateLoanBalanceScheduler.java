package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.utilities.feign.dto.SchedulerPushNotificationDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MonthlyCalculateLoanBalanceScheduler {

	private final ********************** loanIntegrationService;

	@KafkaListener(topics = KafkaTopicConfig.CALCULATE_LOAN_MONTHLY_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.CALCULATE_LOAN_MONTHLY_TOPIC)
	public void consume(String message) throws JsonProcessingException {
		log.info("Monthly Loan Calculation Begin {}", message);
		Instant now = Instant.now();

		try {
			loanIntegrationService.getAllProcessedOnGoingLoans();

			log.info("Monthly Loan Calculation Ended, Delay: {} ms", (System.currentTimeMillis() - now.toEpochMilli()));

		}
		catch (Exception e) {
			log.error("Error occurred during Monthly Loan Calculation for message: {}. Error: {}", message,
					e.getMessage(), e);
		}
	}

}
