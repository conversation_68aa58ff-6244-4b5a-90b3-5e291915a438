package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.request.NewLoanRequest;
import my.com.mandrill.component.dto.response.LoanAggregateResponse;
import my.com.mandrill.utilities.general.constant.EntityName;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

public interface LoanIntegrationService {

	void delete(String id, String userId);

	void completeUserJourney(String loanId, String userJourneyId);

	void sendDashboardActivity(Instant createdDate);

	Loan save(Loan loan);

	LoanAggregateResponse countTotalLoan(String userId);

	List<Loan> findLoanForKYLL(String userId);

	List<String> findAttachedLoan(String userId, EntityName entityName);

	List<Loan> findAllV2(String userId, Sort sort);

	Loan findByIdV2(String id, String userId);

	void deleteV2(String id, String userId);

	void mapEntityDetails(List<Loan> loans);

	void detachByEntityIdAndEntityName(String entityId, EntityName entityName, String userId);

	BigDecimal calculateNewLoanEndingBalance(NewLoanRequest newLoanRequest);

	void getAllProcessedOnGoingLoans();

}
