package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;

import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewLoanRequest {

	@Digits(integer = 15, fraction = 2)
	private BigDecimal amount;

	@Max(100)
	@Digits(integer = 5, fraction = 2)
	private BigDecimal percentage;

	@Digits(integer = 5, fraction = 2)
	private BigDecimal interestRate;

	private LoanTypeEnum type;

	@NotNull
	private Month repaymentStartMonth;

	@NotNull
	@Schema(implementation = Short.class, example = "1997")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "yyyy")
	private Year repaymentStartYear;

	@Digits(integer = 15, fraction = 2)
	private Short duration;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	private BigDecimal monthlyInstallment;

}
