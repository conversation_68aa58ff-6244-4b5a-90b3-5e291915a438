package my.com.mandrill.component.config;

import my.com.mandrill.component.constant.LoanEligibilityCommitmentType;
import my.com.mandrill.component.constant.LoanEligibilityIncomeType;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.BankDetailResponse;
import my.com.mandrill.component.dto.response.LoanEligibilityResponse;
import my.com.mandrill.component.dto.response.PaymentAccountResponse;
import my.com.mandrill.component.dto.response.ProductConfigurationResponse;
import my.com.mandrill.utilities.feign.dto.BankDetailVaultLinkDTO;
import my.com.mandrill.utilities.feign.dto.ProviderDTO;
import my.com.mandrill.utilities.feign.dto.ReminderRequest;
import my.com.mandrill.utilities.feign.dto.VaultAttachmentResponse;
import my.com.mandrill.utilities.feign.dto.model.LoanPlusReportDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordVaultDTO;
import my.com.mandrill.utilities.feign.dto.request.BankListAttachmentRequestDTO;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.feign.dto.response.*;
import my.com.mandrill.utilities.general.constant.UserInterestProductTypeEnum;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.service.LazyLoadingAwareMapper;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, builder = @Builder(disableBuilder = true))
public interface MapStructConverter extends LazyLoadingAwareMapper {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	ReminderRequest toReminderRequest(ReminderIntegrationRequest reminder);

	Loan toLoan(CreateLoanIntegration createLoan);

	Loan toLoan(UpdateLoanIntegration updateLoan);

	LoanDTO toLoanDTO(Loan result);

	Loan toLoan(CreateLoanV2 createLoanV2);

	BankListDTO toBankListDTO(BankList bankList);

	@Mapping(target = "bankList.id", source = "bank.bankList.id")
	@Mapping(target = "bankList.name", source = "bank.bankList.name")
	BankDetailVaultLinkDTO toBankDetailVaultLinkDTO(BankDetail bankDetail);

	LoanLimitDTO toLoanLimitDTO(LoanLimit loanLimit);

	@Mapping(target = "idTypeId", source = "idType.id")
	LoanEligibilityApplicant toLoanEligibilityApplicant(LoanEligibilityRequest request);

	@Mapping(target = "idTypeId", source = "idType.id")
	LoanEligibilityApplicant toLoanEligibilityApplicant(LoanEligibilityApplicantRequest request);

	@Mapping(target = "idType.id", source = "idTypeId")
	LoanEligibilityApplicantDTO toLoanEligibilityApplicantDTO(LoanEligibilityApplicant loanEligibilityApplicant);

	default LoanEligibilityIncomeDTO toLoanEligibilityIncomeDTO(Set<LoanEligibilityIncome> incomes) {
		Map<LoanEligibilityIncomeType, BigDecimal> incomeMap = incomes.stream()
				.collect(Collectors.toMap(LoanEligibilityIncome::getType, LoanEligibilityIncome::getValue));

		LoanEligibilityIncomeDTO income = new LoanEligibilityIncomeDTO();
		income.setAsb(incomeMap.get(LoanEligibilityIncomeType.ASB));
		income.setCommission(incomeMap.get(LoanEligibilityIncomeType.COMMISSION));
		income.setContractualBonus(incomeMap.get(LoanEligibilityIncomeType.CONTRACTUAL_BONUS));
		income.setGrossFixedAllowance(incomeMap.get(LoanEligibilityIncomeType.GROSS_FIXED_ALLOWANCE));
		income.setGrossSalary(incomeMap.get(LoanEligibilityIncomeType.GROSS_SALARY));
		income.setInterestDeposit(incomeMap.get(LoanEligibilityIncomeType.INTEREST_DEPOSIT));
		income.setOvertime(incomeMap.get(LoanEligibilityIncomeType.OVERTIME));
		income.setPerformanceBonus(incomeMap.get(LoanEligibilityIncomeType.PERFORMANCE_BONUS));
		income.setRental(incomeMap.get(LoanEligibilityIncomeType.RENTAL));
		income.setSelfEmployed(incomeMap.get(LoanEligibilityIncomeType.SELF_EMPLOYED));
		income.setPcb(incomeMap.get(LoanEligibilityIncomeType.PCB));

		return income;
	}

	default LoanEligibilityCommitmentDTO toLoanEligibilityCommitmentDTO(Set<LoanEligibilityCommitment> commitments) {
		Map<LoanEligibilityCommitmentType, BigDecimal> incomeMap = commitments.stream().collect(
				Collectors.toMap(LoanEligibilityCommitment::getType, LoanEligibilityCommitment::getMonthlyInstalment));

		LoanEligibilityCommitmentDTO commitment = new LoanEligibilityCommitmentDTO();
		commitment.setAsbLoan(incomeMap.get(LoanEligibilityCommitmentType.ASB_LOAN));
		commitment.setCreditCard(incomeMap.get(LoanEligibilityCommitmentType.CREDIT_CARD));
		commitment.setHirePurchase(incomeMap.get(LoanEligibilityCommitmentType.HIRE_PURCHASE));
		commitment.setMortgageLoan(incomeMap.get(LoanEligibilityCommitmentType.MORTGAGE_LOAN));
		commitment.setOtherLoan(incomeMap.get(LoanEligibilityCommitmentType.OTHER_LOAN));
		commitment.setOverdraft(incomeMap.get(LoanEligibilityCommitmentType.OVERDRAFT));
		commitment.setPersonalLoan(incomeMap.get(LoanEligibilityCommitmentType.PERSONAL_LOAN));

		return commitment;
	}

	LoanEligibilityProductDTO toLoanEligibilityProductDTO(LoanEligibilityProduct loanEligibilityProduct);

	LoanEligibilityDTO toLoanEligibilityDTO(LoanEligibility loanEligibility);

	LoanEligibilityResponse toLoanEligibilityResponse(LoanEligibility loanEligibility);

	LoanEligibilityLender toLoanEligibilityLender(PreApprovalResponse.BestMatchingBanker bestMatchingBanker);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "loanEligibility", ignore = true)
	@Mapping(target = "createdDate", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "lastModifiedDate", ignore = true)
	@Mapping(target = "lastModifiedBy", ignore = true)
	LoanEligibilityProduct toLoanEligibilityProduct(LoanEligibilityProduct loanEligibilityProduct);

	BankListAttachmentRequestDTO toBankListAttachmentRequestDTO(AttachmentRequest attachmentRequest);

	@Mapping(target = "bankList", source = "bank.bankList")
	@Mapping(target = "combineCreditCardLimit", source = "bank.combineCreditCardLimit")
	BankDetailResponse toBankDetailResponse(BankDetail bankDetail);

	BankDetail toBankDetail(BankDetailCreateRequest bankDetailCreateRequest);

	BankDetailDTO toBankDetailDTO(BankDetail bankDetail);

	LoanPlusReportDTO toLoanPlusReportDTO(LoanEligibility loanEligibility);

	BankIncludedDTO toBankIncludedDTO(BankList bankList);

	UserInterestRecordViewDTO toInterestRecordView(UserInterestRecord record);

	UserInterestRecordDetailDTO toInterestRecordDetail(UserInterestRecord record);

	ProductConfigurationDTO toProductConfigurationDTO(ProductConfiguration productConfiguration);

	ProductConfiguration toProductConfiguration(ProductConfigurationRequest productConfigurationRequest);

	ProductConfigurationResponse toProductConfigurationResponse(ProductConfiguration productConfiguration);

	ProductConfigurationTemplateDTO toProductConfigTemplateDTO(
			ProductConfigurationTemplate productConfigurationTemplate);

	ProductConfigurationTemplate toProductConfigurationTemplate(
			ProductConfigurationTemplateRequest productConfigurationRequest);

	ProductConfigurationTemplatePageDTO toProductConfigTemplatePageDTO(
			ProductConfigurationTemplate productConfigurationTemplate);

	ProductConfigurationTemplateProductDTO toProductConfigurationTemplateProductDTO(
			ProductConfigurationTemplate template);

	LoanEligibilityLenderProduct toLoanEligibilityLenderProduct(
			LoanEligibilityLenderProductResponse.ProductInfo productInfo);

	UserInterestVaultResponse toInterestVaultResponse(UserInterestRecord data);

	@Mapping(source = "icNumber", target = "nric")
	UserInterestRecord toUserInterestRecord(CreateUserInterestedRecordRequest data);

	@Mapping(source = "race", target = "ethnicity")
	@Mapping(source = "address", target = "address1")
	@Mapping(source = "state", target = "stateId")
	UserPublicWebRequest toPublicWebRequest(CreateUserInterestedRecordRequest data);

	@Mapping(source = "bank.userId", target = "userId")
	@Mapping(source = "bank.bankList", target = "bankList")
	my.com.mandrill.utilities.general.dto.BankDetailDTO toBankDetailDTOGeneral(BankDetail bankDetail);

	PaymentAccountResponse toPaymentAccountResponse(PaymentAccount paymentAccount);

	PaymentAccountDetailResponse toPaymentAccountDetailResponse(PaymentAccount paymentAccount);

	@Mapping(source = "productType", target = "productType", qualifiedByName = "mapProductCodeToName")
	UserInterestRecordRSMViewDTO toUserInterestRecordRSMViewDTO(UserInterestRecord data);

	ProviderDTO toProviderDTO(BankList bankList);

	@Mapping(expression = "java(data.getStartDate() != null ? data.getStartDate() : data.getApplicationDate())",
			target = "startDate")
	@Mapping(expression = "java(data.getEndDate() != null ? data.getEndDate() : data.getApplicationDate())",
			target = "endDate")
	ReportServiceRequest toReportServiceRequest(RSMLeadRequest data);

	@Named("mapProductCodeToName")
	default String mapProductCodeToName(String productType) {
		return UserInterestProductTypeEnum.getNameByCode(productType);
	}

	@Mapping(source = "productType", target = "productType", qualifiedByName = "mapProductCodeToName")
	@Mapping(source = "source.name", target = "source")
	UserInterestRecordRSMPaginationDTO toUserInterestRecordRSMPaginationDTO(UserInterestRecord record);

	@Mapping(source = "vaultId", target = "vaultId")
	@Mapping(source = "data.id", target = "attachmentId")
	UserInterestRecordVaultDTO toUserInterestRecordVaultDTO(VaultAttachmentResponse data, String vaultId);

	Loan toLoan(NewLoanRequest newLoanRequest);

}