package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.IssuerType;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.domain.UserInterestRecordAddon;
import my.com.mandrill.component.domain.UserInterestRecordVault;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.model.UserInterestedRedirectDTO;
import my.com.mandrill.component.dto.request.CreateUserInterestRecord;
import my.com.mandrill.component.dto.request.CreateUserInterestRedirectRecord;
import my.com.mandrill.component.dto.request.ReportServiceRequest;
import my.com.mandrill.component.dto.response.UserInterestRecordPageResponse;
import my.com.mandrill.component.dto.response.UserInterestRecordResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.UserInterestRecordAddonService;
import my.com.mandrill.component.service.UserInterestRecordIntegrationService;
import my.com.mandrill.component.service.UserInterestRecordService;
import my.com.mandrill.component.service.UserInterestRecordVaultService;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.core.util.InstitutionUtil;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordVaultDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestVaultRequestDTO;
import my.com.mandrill.utilities.feign.dto.request.FindRelationTypeRequest;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.feign.dto.response.UserInterestVaultResponse;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static my.com.mandrill.utilities.general.constant.TimeConstant.DEFAULT_TIMEZONE;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserInterestRecordIntegrationServiceImpl implements UserInterestRecordIntegrationService {

	private final UserInterestRecordService userInterestRecordService;

	private final ProxyFeignClient proxyFeignClient;

	private final RunningNumberUtil runningNumberUtil;

	private final ObjectMapper objectMapper;

	private static final String BALANCE_TRANSFER_NOT_AVAILABLE = "NA";

	private static final String BALANCE_TRANSFER_TRUE = "Yes";

	private static final String BALANCE_TRANSFER_FALSE = "No";

	private final UserInterestRecordVaultService userInterestRecordVaultService;

	private final UserInterestRecordAddonService userInterestRecordAddonService;

	@Override
	public List<UserInterestRecordResponse> export(List<CurrentUserInstitutionsDTO> institutionsDTO,
			ReportServiceRequest request) {

		if (request.getStartDate().isAfter(request.getEndDate())) {
			throw new BusinessException(ErrorCodeEnum.FILTER_DATE_IS_NOT_VALID);
		}

		// get all by institution
		HashMap<String, String> institutionMap = new HashMap<>();
		List<InstitutionDTO> institutionDTOS = proxyFeignClient.getAccountFeignClient().getAllInstitutionByAiMapping();

		for (InstitutionDTO institutionDTO : institutionDTOS) {
			institutionMap.put(institutionDTO.getId(), institutionDTO.getName());
		}

		List<String> institutionsId = institutionsDTO.stream().map(CurrentUserInstitutionsDTO::getId).toList();
		Set<String> providerId = request.getProviderIds();
		if (providerId == null || providerId.isEmpty() || institutionsId.stream().anyMatch(providerId::contains)) {
			request.setProviderIds(Collections.emptySet());
		}

		List<UserInterestRecord> recordResponses = userInterestRecordService.findReportData(request);
		List<String> userInterestedRecords = recordResponses.stream().map(AuditSection::getId).toList();

		final Map<String, List<UserInterestRecordAddon>> addonsMap = userInterestRecordAddonService
				.findByInterestRecordId(userInterestedRecords).stream()
				.collect(Collectors.groupingBy(v -> v.getUserInterestRecord().getId()));

		// adsMap here is storing cached advertisement instead of calling same API with
		// same content multiple times
		final Map<String, String> adsMap = new HashMap<>();
		return recordResponses.stream().map(re -> mapToDto(re, institutionMap, adsMap, addonsMap)).toList();
	}

	@Override
	public Page<UserInterestRecordPageResponse> export(List<CurrentUserInstitutionsDTO> institutionsDTO,
			LocalDate startDate, LocalDate endDate, List<String> providerId, Pageable pageable) {

		if (startDate.isAfter(endDate)) {
			throw new BusinessException(ErrorCodeEnum.FILTER_DATE_IS_NOT_VALID);
		}

		final Instant dateStart = startDate.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		final Instant dateEnd = endDate.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();

		// get all by institution
		HashMap<String, String> institutionMap = new HashMap<>();
		List<InstitutionDTO> institutionDTOS = proxyFeignClient.getAccountFeignClient().getAllInstitutionByAiMapping();

		for (InstitutionDTO institutionDTO : institutionDTOS) {
			institutionMap.put(institutionDTO.getId(), institutionDTO.getName());
		}

		Page<UserInterestRecord> recordResponses;

		boolean isMandrillUser = InstitutionUtil.isHextarUser(institutionsDTO);
		List<String> institutionsId = institutionsDTO.stream().map(CurrentUserInstitutionsDTO::getId).toList();

		if (isMandrillUser) {
			if (providerId == null || providerId.isEmpty() || institutionsId.stream().anyMatch(providerId::contains)) {
				recordResponses = userInterestRecordService
						.findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(dateStart,
								dateEnd, pageable);
			}
			else {
				recordResponses = userInterestRecordService
						.findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
								providerId, dateStart, dateEnd, pageable);
			}

		}
		else {
			recordResponses = userInterestRecordService
					.findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
							providerId, dateStart, dateEnd, pageable);
		}

		// adsMap here is storing cached advertisement instead of calling same API with
		// same content multiple times
		final Map<String, String> adsMap = new HashMap<>();

		List<UserInterestRecordPageResponse> responseRecord = recordResponses.stream()
				.map(response -> mapUserInterestRecordPageDto(response, institutionMap, adsMap)).toList();

		return new PageImpl<>(responseRecord, recordResponses.getPageable(), recordResponses.getTotalElements());

	}

	@Override
	public List<UserInterestedRedirectDTO> export(LocalDate startDate, LocalDate endDate) {
		if (startDate.isAfter(endDate)) {
			throw new BusinessException(ErrorCodeEnum.FILTER_DATE_IS_NOT_VALID);
		}

		final Instant startDateTime = startDate.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		final Instant endDateTime = endDate.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();

		List<InstitutionDTO> institutionDTOS = proxyFeignClient.getAccountFeignClient().getAllInstitution();
		Map<String, String> institutionMap = institutionDTOS.stream()
				.collect(Collectors.toMap(InstitutionDTO::getId, InstitutionDTO::getName));

		List<UserInterestedRedirectDTO> userInterestedRedirectDTOS = userInterestRecordService
				.getAllRedirectTrueRecordBetweenDate(startDateTime, endDateTime);

		mapUserInterestedRedirectDTO(userInterestedRedirectDTOS, institutionMap);

		return userInterestedRedirectDTOS;
	}

	@Override
	public Page<UserInterestedRedirectDTO> export(LocalDate startDate, LocalDate endDate, Pageable pageable) {
		if (startDate.isAfter(endDate)) {
			throw new BusinessException(ErrorCodeEnum.FILTER_DATE_IS_NOT_VALID);
		}

		final Instant dateStart = startDate.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		final Instant dateEnd = endDate.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();

		List<InstitutionDTO> institutionDTOS = proxyFeignClient.getAccountFeignClient().getAllInstitution();
		Map<String, String> institutionMap = institutionDTOS.stream()
				.collect(Collectors.toMap(InstitutionDTO::getId, InstitutionDTO::getName));

		Page<UserInterestedRedirectDTO> userInterestedRedirectDTOS = userInterestRecordService
				.countAllBetweenDateAndIsRedirectTrue(dateStart, dateEnd, pageable);

		mapUserInterestedRedirectDTO(userInterestedRedirectDTOS, institutionMap);

		return userInterestedRedirectDTOS;
	}

	@Override
	public Long countRedirect(LocalDate startDate, LocalDate endDate) {
		if (startDate.isAfter(endDate)) {
			throw new BusinessException(ErrorCodeEnum.FILTER_DATE_IS_NOT_VALID);
		}

		final Instant dateStart = startDate.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		final Instant dateEnd = endDate.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		return userInterestRecordService.countBetweenDateAndIsRedirectTrue(dateStart, dateEnd);
	}

	@Override
	public Long count(LocalDate startDate, LocalDate endDate) {
		if (startDate.isAfter(endDate)) {
			throw new BusinessException(ErrorCodeEnum.FILTER_DATE_IS_NOT_VALID);
		}

		final Instant dateStart = startDate.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		final Instant dateEnd = endDate.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
		return userInterestRecordService.countBetweenDateAndIsRedirectFalse(dateStart, dateEnd);
	}

	@Override
	public UserInterestRecord insertRedirect(CreateUserInterestRedirectRecord createUserInterest,
			CurrentUserIdDTO userDTO) throws JsonProcessingException {
		if (!UserInterestProductTypeEnum.isExist(createUserInterest.getProductType())) {
			throw new BusinessException(ErrorCodeEnum.PRODUCT_TYPE_IS_NOT_EXIST);
		}

		InstitutionDTO institutionDTO = proxyFeignClient.getAccountFeignClient()
				.getInstitutionByAiMapping(createUserInterest.getIssuerCode());
		String response = proxyFeignClient.getAiFeignClient().product(createUserInterest.getProductType(), null, null,
				createUserInterest.getProductName(), createUserInterest.getProductId()).getBody();
		List<ProductDTO> productDTOList = objectMapper.readValue(response, new TypeReference<>() {
		});

		RedirectUserInterestRecordDTO redirectUserInterestRecordDTO = new RedirectUserInterestRecordDTO();
		redirectUserInterestRecordDTO.setUserId(userDTO.getId());
		redirectUserInterestRecordDTO.setUserFullName(userDTO.getFullName());
		redirectUserInterestRecordDTO.setUserPhoneCountry(userDTO.getPhoneCountry());
		redirectUserInterestRecordDTO.setUserPhoneNumber(userDTO.getPhoneNumber());
		redirectUserInterestRecordDTO.setUserEmail(userDTO.getEmail());
		redirectUserInterestRecordDTO.setUserRefNo(userDTO.getRefNo());
		redirectUserInterestRecordDTO.setInstitutionId(institutionDTO.getId());
		redirectUserInterestRecordDTO.setNric(userDTO.getNric());

		IncomeDTO incomeDTO = proxyFeignClient.getAccountFeignClient().getCurrentUserIncome().stream()
				.filter(incomeDto1 -> IncomeTypeEnum.SALARY.name().equals(incomeDto1.getIncomeType().getCode()))
				.findAny().orElse(null);
		redirectUserInterestRecordDTO.setIncomeAmount(incomeDTO != null ? incomeDTO.getMonthlyIncomeAmount() : null);
		redirectUserInterestRecordDTO.setLoginType(userDTO.getLoginType());

		return assignRedirectUserInterestRecord(createUserInterest, redirectUserInterestRecordDTO, productDTOList);
	}

	@Override
	@Transactional
	public UserInterestRecord insert(CreateUserInterestRecord createUserInterest, UserDTO userDTO) {
		if (!UserInterestProductTypeEnum.isExist(createUserInterest.getProductType())) {
			throw new BusinessException(ErrorCodeEnum.PRODUCT_TYPE_IS_NOT_EXIST);
		}

		// loongyeat: Don't allow reapplies if the latest application's reapply date
		// hasn't yet passed.
		Optional<UserInterestRecord> existingRecord = userInterestRecordService
				.findFirstByUserIdAndProductIdAndIsRedirectFalseOrderByReapplyDateDesc(userDTO.getId(),
						createUserInterest.getProductId());
		if (existingRecord.isPresent()
				&& UserInterestProductTypeEnum.CREDIT_CARD.getCode().equals(existingRecord.get().getProductType())) {
			Instant reapplyDate = existingRecord.get().getReapplyDate();
			if (reapplyDate != null && Instant.now().isBefore(reapplyDate)) {
				throw new BusinessException(ErrorCodeEnum.USER_INTEREST_ALREADY_APPLIED);
			}
		}
		RSMRelationType rsmRelation = proxyFeignClient.getMoneyXCoreFeignClient()
				.findRelationType(FindRelationTypeRequest.builder().referredId(userDTO.getId()).build());

		InstitutionDTO institutionDTO = proxyFeignClient.getAccountFeignClient()
				.getInstitutionByAiMapping(createUserInterest.getIssuerCode());
		UserInterestRecord userInterestRecord = new UserInterestRecord();
		userInterestRecord.setProviderId(institutionDTO.getId());
		userInterestRecord.setIssuerCode(createUserInterest.getIssuerCode());
		userInterestRecord.setIssuerType(createUserInterest.getIssuerType());
		userInterestRecord.setUserId(userDTO.getId());
		userInterestRecord.setEmail(createUserInterest.getEmail());
		userInterestRecord.setFullName(userDTO.getFullName());
		userInterestRecord.setPhoneNumber(createUserInterest.getPhoneNumber());
		userInterestRecord.setProductType(createUserInterest.getProductType());
		userInterestRecord.setProductName(createUserInterest.getProductName());
		userInterestRecord.setProductId(createUserInterest.getProductId());
		userInterestRecord.setIsBalanceTransfer(
				createUserInterest.getIsBalanceTransfer() != null && createUserInterest.getIsBalanceTransfer());
		userInterestRecord.setRefNo(runningNumberUtil.getLatestRunningNumber(
				RunningNumberModule.USER_INTERESTED_RECORD.name(), userDTO.getLoginType().isAdmin()));
		userInterestRecord.setNric(StringUtils.isNotBlank(createUserInterest.getNric()) ? createUserInterest.getNric()
				: userDTO.getNric());
		userInterestRecord.setUserRefNo(userDTO.getRefNo());
		userInterestRecord.setIncomeAmount(createUserInterest.getIncomeAmount());
		userInterestRecord.setRsmEligible(true);
		userInterestRecord.setRsmRelation(rsmRelation);
		userInterestRecord.setRsmStatus(RSMStatus.PENDING);
		handleSource(userInterestRecord, createUserInterest);

		if (createUserInterest.getProductType().equals(UserInterestProductTypeEnum.CREDIT_CARD.getCode())) {
			// loongyeat: Add a date where users can reapply after this specified date
			// (only
			// for CREDIT_CARD product types).
			userInterestRecord.setReapplyDate(Instant.now().plus(14, ChronoUnit.DAYS));
		}

		UserInterestRecord data = userInterestRecordService.save(userInterestRecord);
		if (!createUserInterest.getVaultIds().isEmpty()) {
			String applyDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
			List<String> vaultIds = proxyFeignClient.getCommonFeignClient()
					.rewriteVault(UserInterestVaultRequestDTO.builder().insurerCode(createUserInterest.getIssuerCode())
							.vaultIds(createUserInterest.getVaultIds()).additionalPrefix(String.format("%s_%s_%s_",
									institutionDTO.getName(), applyDate, userInterestRecord.getFullName()))
							.build());
			for (String vaultId : vaultIds) {
				UserInterestRecordVault userInterestRecordVault = new UserInterestRecordVault();
				userInterestRecordVault.setVaultId(vaultId);
				userInterestRecordVault.setUserInterestRecord(data);
				userInterestRecordVaultService.save(userInterestRecordVault);
			}

		}
		if (!CollectionUtils.isEmpty(createUserInterest.getAddons())) {
			userInterestRecordAddonService.create(createUserInterest.getAddons(), data.getId());
		}

		return data;
	}

	private void mapUserInterestedRedirectDTO(List<UserInterestedRedirectDTO> userInterestedRedirectList,
			Map<String, String> institutionMap) {
		for (UserInterestedRedirectDTO data : userInterestedRedirectList) {
			data.setProvider(institutionMap.get(data.getProvider()));
			data.setProductType(UserInterestProductTypeEnum.getProductTypeName(data.getProductType()));
			data.setIssuerType(IssuerType.valueOf(data.getIssuerType()).getName());
		}
	}

	private void mapUserInterestedRedirectDTO(Page<UserInterestedRedirectDTO> userInterestedRedirectList,
			Map<String, String> institutionMap) {
		for (UserInterestedRedirectDTO data : userInterestedRedirectList) {
			data.setProvider(institutionMap.get(data.getProvider()));
			data.setProductType(UserInterestProductTypeEnum.getProductTypeName(data.getProductType()));
			data.setIssuerType(IssuerType.valueOf(data.getIssuerType()).getName());
		}
	}

	private UserInterestRecordResponse mapToDto(UserInterestRecord userInterestRecord,
			HashMap<String, String> institutionMap, Map<String, String> adsMap,
			Map<String, List<UserInterestRecordAddon>> addonsMap) {

		UserInterestRecordResponse userInterestRecordResponse = new UserInterestRecordResponse();
		userInterestRecordResponse.setFullName(userInterestRecord.getFullName());
		userInterestRecordResponse.setEmail(userInterestRecord.getEmail());
		userInterestRecordResponse.setPhoneNumber(userInterestRecord.getPhoneNumber());
		userInterestRecordResponse.setProviderId(userInterestRecord.getProviderId());
		userInterestRecordResponse.setIssuerCode(userInterestRecord.getIssuerCode());
		userInterestRecordResponse.setIssuerType(userInterestRecord.getIssuerType());
		userInterestRecordResponse
				.setProductType(UserInterestProductTypeEnum.getProductTypeName(userInterestRecord.getProductType()));
		userInterestRecordResponse.setProductName(userInterestRecord.getProductName());
		userInterestRecordResponse.setCreatedDate(userInterestRecord.getCreatedDate());
		userInterestRecordResponse.setProviderName(institutionMap.get(userInterestRecord.getProviderId()));
		userInterestRecordResponse.setProductId(userInterestRecord.getProductId());
		userInterestRecordResponse.setRefNo(userInterestRecord.getRefNo());
		userInterestRecordResponse.setNric(userInterestRecord.getNric());
		userInterestRecordResponse.setIsBalanceTransfer(userInterestRecord.getIsBalanceTransfer());
		userInterestRecordResponse.setUserRefNo(userInterestRecord.getUserRefNo());
		userInterestRecordResponse.setIncomeAmount(userInterestRecord.getIncomeAmount());

		if (Objects.nonNull(addonsMap.get(userInterestRecord.getId()))) {
			userInterestRecordResponse.setAddons(
					addonsMap.get(userInterestRecord.getId()).stream().map(v -> v.getFieldName() + ":" + v.getValue())
							.collect(Collectors.joining(System.lineSeparator())));
		}

		// Handle Source
		if (userInterestRecord.getSource() != null) {
			if (userInterestRecord.getSource().equals(UserInterestedSource.ADVERTISEMENT)) {
				handleSourceAdvertisementIntegration(userInterestRecord, adsMap, userInterestRecordResponse);
			}
			else {
				userInterestRecordResponse.setSource(userInterestRecord.getSource().getName());
			}
		}

		return userInterestRecordResponse;
	}

	private UserInterestRecordPageResponse mapUserInterestRecordPageDto(UserInterestRecord userInterestRecord,
			HashMap<String, String> institutionMap, Map<String, String> adsMap) {

		UserInterestRecordPageResponse response = new UserInterestRecordPageResponse();
		response.setId(userInterestRecord.getId());
		response.setRefNo(userInterestRecord.getRefNo());
		response.setCreatedLocalDate(
				userInterestRecord.getCreatedDate().atZone(ZoneId.of(DEFAULT_TIMEZONE)).toLocalDate());
		response.setCreatedLocalTime(
				userInterestRecord.getCreatedDate().atZone(ZoneId.of(DEFAULT_TIMEZONE)).toLocalTime());
		response.setFullName(userInterestRecord.getFullName());
		response.setEmail(userInterestRecord.getEmail());
		response.setPhoneNumber(userInterestRecord.getPhoneNumber());
		response.setNric(userInterestRecord.getNric());
		response.setProductType(UserInterestProductTypeEnum.getProductTypeName(userInterestRecord.getProductType()));
		response.setProductName(userInterestRecord.getProductName());
		response.setIssuerType(userInterestRecord.getIssuerType());
		response.setProviderName(institutionMap.get(userInterestRecord.getProviderId()));
		response.setUserRefNo(userInterestRecord.getUserRefNo());
		response.setVaultCount(userInterestRecord.getVaultCount());
		response.setIncomeAmount(userInterestRecord.getIncomeAmount());

		Boolean isBalanceTransfer = userInterestRecord.getIsBalanceTransfer();
		if (isBalanceTransfer == null) {
			response.setBalanceTransfer(BALANCE_TRANSFER_NOT_AVAILABLE);
		}
		else if (isBalanceTransfer.equals(Boolean.TRUE)) {
			response.setBalanceTransfer(BALANCE_TRANSFER_TRUE);
		}
		else {
			response.setBalanceTransfer(BALANCE_TRANSFER_FALSE);
		}

		// Handle Source
		if (userInterestRecord.getSource() != null) {
			if (userInterestRecord.getSource().equals(UserInterestedSource.ADVERTISEMENT)) {
				handleSourceAdvertisementIntegration(userInterestRecord, adsMap, response);
			}
			else {
				response.setSource(userInterestRecord.getSource().getName());
			}
		}

		return response;
	}

	private void handleSourceAdvertisementIntegration(UserInterestRecord userInterestRecord, Map<String, String> adsMap,
			UserInterestRecordResponse userInterestRecordResponse) {
		if (StringUtils.isBlank(userInterestRecord.getSourceId())) {
			log.warn("[{}]: Handling blank advertisement source", userInterestRecord.getRefNo());
			userInterestRecordResponse.setSource(userInterestRecord.getSource().getName());
		}
		else {
			final String cached = adsMap.get(userInterestRecord.getSourceId());
			if (StringUtils.isNotBlank(cached)) {
				log.debug("[{}]: Using cached advertisement source. Key: {}, Value: {}", userInterestRecord.getRefNo(),
						userInterestRecord.getSourceId(), cached);
				userInterestRecordResponse.setSource(cached);
			}
			else {

				String sourceName;
				try {
					sourceName = proxyFeignClient.getPromotionFeignClient()
							.getAdvertisementById(userInterestRecord.getSourceId()).getName();

					UserInterestedSource source = userInterestRecord.getSource();

					// Check if the campaign doesn't have the associated name. If it
					// doesn't,
					// we concat
					final String finalSourceName = sourceName;
					if (source.getAssociations().stream()
							.noneMatch(assoc -> StringUtils.containsAnyIgnoreCase(finalSourceName, assoc))) {
						sourceName = sourceName.concat(" ").concat(source.getName());
					}

				}
				catch (EntityNotFoundException e) {
					log.warn("Advertisement not found: {}", userInterestRecord.getSourceId());
					sourceName = userInterestRecord.getSource().getName();
				}
				catch (Exception e) {
					log.warn("Error happening when trying to fetch Advertisement: {}, Reason: {}",
							userInterestRecord.getSourceId(), e.getMessage());
					sourceName = userInterestRecord.getSource().getName();
				}

				userInterestRecordResponse.setSource(sourceName);
				adsMap.put(userInterestRecord.getSourceId(), sourceName);
			}

		}
	}

	private void handleSource(UserInterestRecord userInterestRecord, HandleSource handleSource) {
		if (handleSource == null || handleSource.getSource() == null || userInterestRecord == null)
			return;

		final UserInterestedSource source = handleSource.getSource();

		userInterestRecord.setSource(handleSource.getSource());
		if (source == UserInterestedSource.ADVERTISEMENT) {
			if (StringUtils.isBlank(handleSource.getSourceId())) {
				throw new BusinessException(ErrorCodeEnum.MISSING_SOURCE_ID_FOR_ADVERTISEMENT_SOURCE);
			}

			userInterestRecord.setSourceId(handleSource.getSourceId());
		}

	}

	@Override
	public long countAllianceBankCampaign(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId, String issuerCode,
			String productType, UserInterestedSource source) {
		Instant dateStart = dateFrom.atStartOfDay().atZone(zoneId).toInstant();
		Instant dateEnd = dateTo.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		return userInterestRecordService.countAllianceBankCampaign(dateStart, dateEnd, issuerCode, productType, source);
	}

	@Override
	public UserInterestRecord findById(String currentInstitutionId, String id) {
		Set<String> issuerCodes = proxyFeignClient.getAccountFeignClient()
				.getInstitutionAiMapping(currentInstitutionId);
		return userInterestRecordService.findByIdAndIssuerCodes(id, issuerCodes);
	}

	@Override
	public UserInterestRecordDetailDTO findDetail(String currentInstitutionId, String id) {
		UserInterestRecord userInterestRecord = findById(currentInstitutionId, id);
		List<UserInterestRecordVault> userInterestVaults = userInterestRecordService
				.findVaultByRecordId(userInterestRecord.getId());

		List<VaultResponse> vaults = new ArrayList<>();
		if (!userInterestVaults.isEmpty()) {
			List<String> attachmentGroupIds = userInterestVaults.stream().map(UserInterestRecordVault::getVaultId)
					.toList();

			vaults = proxyFeignClient.getCommonFeignClient().findVaultsById(attachmentGroupIds);
		}

		UserInterestRecordDetailDTO userInterestRecordDetail = MapStructConverter.MAPPER
				.toInterestRecordDetail(userInterestRecord);
		userInterestRecordDetail.setVaults(vaults);

		return userInterestRecordDetail;
	}

	@Override
	public UserInterestRecordRSMViewDTO findById(String id) {
		UserInterestRecord record = userInterestRecordService.findById(id);

		Map<String, String> typesGroup = findMappingProductGroup(Set.of(record.getProductType()));

		InstitutionDTO institution = proxyFeignClient.getAccountFeignClient()
				.getInstitutionByAiMappingPrivate(record.getIssuerCode());
		UserInterestRecordRSMViewDTO result = MapStructConverter.MAPPER.toUserInterestRecordRSMViewDTO(record);
		result.setProviderId(institution.getId());
		result.setProviderName(institution.getName());
		result.setApplicationType(record.isRedirect() ? ApplicationType.REDIRECT : ApplicationType.LEAD_GEN);
		result.setApplicationTypeName(result.getApplicationType().getName());
		if (typesGroup.containsKey(record.getProductType())) {
			result.setProductCategory(ProductGroupEnum.getNameByValue(typesGroup.get(record.getProductType())));
		}
		List<String> vaultIds = userInterestRecordVaultService.findVaultByRecordId(record.getId());
		if (!CollectionUtils.isEmpty(vaultIds)) {
			List<VaultResponse> vaults = proxyFeignClient.getCommonFeignClient().findVaultsById(vaultIds);
			List<UserInterestRecordVaultDTO> documents = vaults.stream()
					.map(vault -> vault.getAttachments().stream().map(attachment -> MapStructConverter.MAPPER
							.toUserInterestRecordVaultDTO(attachment, vault.getId())).toList())
					.flatMap(List::stream).toList();
			result.setDocuments(documents);
		}

		return result;
	}

	@Override
	public UserInterestVaultResponse findVaultAndIssuerByRecordId(String recordId) {
		UserInterestRecord userInterestRecord = userInterestRecordService.findById(recordId);
		List<String> vaultIds = userInterestRecordVaultService.findVaultByRecordId(recordId);

		InstitutionDTO institutionDTO = proxyFeignClient.getAccountFeignClient()
				.getInstitutionByAiMapping(userInterestRecord.getIssuerCode());

		UserInterestVaultResponse result = MapStructConverter.MAPPER.toInterestVaultResponse(userInterestRecord);
		result.setProviderName(institutionDTO.getName());
		result.setVaultIds(vaultIds);
		return result;
	}

	@Override
	public UserInterestRecord insertRedirectPublic(CreateUserInterestRedirectRecord createUserInterest,
			UserPublicWebResponse userPublicWebResponse) throws JsonProcessingException {
		if (!UserInterestProductTypeEnum.isExist(createUserInterest.getProductType())) {
			throw new BusinessException(ErrorCodeEnum.PRODUCT_TYPE_IS_NOT_EXIST);
		}

		InstitutionDTO institutionDTO = proxyFeignClient.getAccountFeignClient()
				.getInstitutionByAiMappingPrivate(createUserInterest.getIssuerCode());
		String response = proxyFeignClient.getAiFeignClient().productPrivate(createUserInterest.getProductType(), null,
				null, createUserInterest.getProductName(), createUserInterest.getProductId()).getBody();
		List<ProductDTO> productDTOList = objectMapper.readValue(response, new TypeReference<>() {
		});

		RedirectUserInterestRecordDTO redirectUserInterestRecordDTO = new RedirectUserInterestRecordDTO();
		redirectUserInterestRecordDTO.setUserId(userPublicWebResponse.getId());
		redirectUserInterestRecordDTO.setUserFullName(userPublicWebResponse.getFullName());
		redirectUserInterestRecordDTO.setUserPhoneCountry(userPublicWebResponse.getPhoneCountry());
		redirectUserInterestRecordDTO.setUserPhoneNumber(userPublicWebResponse.getPhoneNumber());
		redirectUserInterestRecordDTO.setUserEmail(userPublicWebResponse.getEmail());
		redirectUserInterestRecordDTO.setUserRefNo(userPublicWebResponse.getUserRefNo());
		redirectUserInterestRecordDTO.setInstitutionId(institutionDTO.getId());
		redirectUserInterestRecordDTO.setNric(userPublicWebResponse.getNric());

		return assignRedirectUserInterestRecord(createUserInterest, redirectUserInterestRecordDTO, productDTOList);
	}

	private UserInterestRecord assignRedirectUserInterestRecord(CreateUserInterestRedirectRecord createUserInterest,
			RedirectUserInterestRecordDTO redirectUserInterestRecordDTO, List<ProductDTO> productDTOList) {
		RSMRelationType rsmRelation = proxyFeignClient.getMoneyXCoreFeignClient().findRelationType(
				FindRelationTypeRequest.builder().referredId(redirectUserInterestRecordDTO.getUserId()).build());

		BigDecimal incomeAmount = proxyFeignClient.getAccountFeignClient()
				.findSalaryByUserId(redirectUserInterestRecordDTO.getUserId());

		UserInterestRecord userInterestRecord = new UserInterestRecord();
		userInterestRecord.setProviderId(redirectUserInterestRecordDTO.getInstitutionId());
		userInterestRecord.setIssuerCode(createUserInterest.getIssuerCode());
		userInterestRecord.setIssuerType(createUserInterest.getIssuerType());
		userInterestRecord.setUserId(redirectUserInterestRecordDTO.getUserId());
		userInterestRecord.setFullName(redirectUserInterestRecordDTO.getUserFullName());
		userInterestRecord.setPhoneNumber(redirectUserInterestRecordDTO.getUserPhoneCountry()
				+ redirectUserInterestRecordDTO.getUserPhoneNumber());
		userInterestRecord.setEmail(redirectUserInterestRecordDTO.getUserEmail());
		userInterestRecord.setProductType(createUserInterest.getProductType());
		userInterestRecord.setProductName(createUserInterest.getProductName());
		userInterestRecord.setProductId(createUserInterest.getProductId());
		userInterestRecord.setRefNo(
				runningNumberUtil.getLatestRunningNumber(RunningNumberModule.USER_INTERESTED_RECORD.name(), false));
		userInterestRecord.setNric(redirectUserInterestRecordDTO.getNric());
		userInterestRecord.setIncomeAmount(incomeAmount);
		userInterestRecord.setRedirect(true);
		userInterestRecord.setRsmEligible(true);
		userInterestRecord.setRsmStatus(RSMStatus.PENDING);
		userInterestRecord.setRsmRelation(rsmRelation);
		userInterestRecord.setIsBalanceTransfer(
				createUserInterest.getIsBalanceTransfer() != null && createUserInterest.getIsBalanceTransfer());
		userInterestRecord.setUserRefNo(redirectUserInterestRecordDTO.getUserRefNo());
		if (!productDTOList.isEmpty()) {
			// Get the first ProductDTO of the array since it always returns one product
			// by the product id
			ProductDTO productDTO = productDTOList.get(0);
			userInterestRecord.setUrl(productDTO.getApplyLink());
		}
		handleSource(userInterestRecord, createUserInterest);

		return userInterestRecordService.save(userInterestRecord);

	}

	private Map<String, String> findMappingProductGroup(Set<String> productTypes) {
		if (CollectionUtils.isEmpty(productTypes)) {
			return Collections.emptyMap();
		}

		return proxyFeignClient.getAiFeignClient().findProductType(new ArrayList<>(), productTypes, "ADMIN").stream()
				.collect(Collectors.toMap(ProductTypeDTO::getValue, v -> v.getProductGroup().getValue(), (v, v2) -> v));
	}

	private Map<String, String> findMappingInstitutionName(List<String> institutionIds) {
		if (CollectionUtils.isEmpty(institutionIds)) {
			return Collections.emptyMap();
		}
		return proxyFeignClient.getAccountFeignClient().findInstitutionById(institutionIds).stream()
				.collect(Collectors.toMap(InstitutionDTO::getId, InstitutionDTO::getName, (v1, v2) -> v1));
	}

	private List<UserInterestRecordRSMPaginationDTO> mapUserInterestRecordRSMPagination(
			List<UserInterestRecord> records) {
		if (CollectionUtils.isEmpty(records)) {
			return Collections.emptyList();
		}

		Map<String, String> institutionMap = findMappingInstitutionName(
				records.stream().map(UserInterestRecord::getProviderId).toList());

		Set<String> productTypes = records.stream().map(UserInterestRecord::getProductType).collect(Collectors.toSet());
		Map<String, String> typesGroup = findMappingProductGroup(productTypes);
		Map<Boolean, String> balanceTransferMap = Map.of(Boolean.TRUE, BALANCE_TRANSFER_TRUE, Boolean.FALSE,
				BALANCE_TRANSFER_FALSE);

		Map<String, Long> vaultCountMapping = userInterestRecordService
				.findVaultCountByRecordIds(records.stream().map(UserInterestRecord::getId).toList());

		return records.stream().map(record -> {
			UserInterestRecordRSMPaginationDTO result = MapStructConverter.MAPPER
					.toUserInterestRecordRSMPaginationDTO(record);
			ApplicationType applicationType = record.isRedirect() ? ApplicationType.REDIRECT : ApplicationType.LEAD_GEN;
			result.setApplicationType(applicationType);
			result.setApplicationTypeName(applicationType.getName());
			result.setProviderName(institutionMap.get(record.getProviderId()));
			result.setBalanceTransfer(BALANCE_TRANSFER_NOT_AVAILABLE);
			result.setVaultCount(vaultCountMapping.getOrDefault(record.getId(), 0L));
			if (Objects.nonNull(record.getIsBalanceTransfer())) {
				result.setBalanceTransfer(balanceTransferMap.get(record.getIsBalanceTransfer()));
			}
			if (typesGroup.containsKey(record.getProductType())) {
				result.setProductCategory(ProductGroupEnum.getNameByValue(typesGroup.get(record.getProductType())));
			}
			return result;
		}).toList();
	}

	@Override
	public Page<UserInterestRecordRSMPaginationDTO> rsmLead(Pageable pageable, RSMLeadRequest request) {
		ReportServiceRequest query = MapStructConverter.MAPPER.toReportServiceRequest(request);
		query.setPageable(pageable);
		Page<UserInterestRecord> records = userInterestRecordService.findAll(query);
		return new PageImpl<>(mapUserInterestRecordRSMPagination(records.toList()), records.getPageable(),
				records.getTotalElements());
	}

	@Override
	public List<UserInterestRecordRSMPaginationDTO> rsmLeadReport(RSMLeadRequest request) {
		ReportServiceRequest query = MapStructConverter.MAPPER.toReportServiceRequest(request);
		List<UserInterestRecord> records = userInterestRecordService.findReportData(query);
		return mapUserInterestRecordRSMPagination(records);
	}

	@Override
	public void updateRsmInfo(LeadRSMUpdateRequest request) {
		UserInterestRecord record = userInterestRecordService.findById(request.getId());
		record.setRsmStatus(request.getStatus());
		record.setRsmCommissionAttached(request.isCommissionAttached());
		userInterestRecordService.save(record);
	}

	@Override
	public Set<String> findAllApplicationIdsExcludingUserIdsIn(Set<String> applicationIds, Set<String> userIds) {
		return userInterestRecordService.findAllApplicationIdsExcludingUserIdsIn(applicationIds, userIds);
	}

}
