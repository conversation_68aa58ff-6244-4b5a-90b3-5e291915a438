package my.com.mandrill.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.utilities.general.service.SchedulerDataConfig;

@Getter
@AllArgsConstructor
public enum SchedulerDataInit implements SchedulerDataConfig {

	CALCULATE_LOAN_MONTHLY_TOPIC(KafkaTopicConfig.CALCULATE_LOAN_MONTHLY_TOPIC, KafkaTopicConfig.GROUP,
			KafkaTopicConfig.CALCULATE_LOAN_MONTHLY_TOPIC, MONTHLY_FIRST_DAY_12AM_KL_CRON);

	private final String destination;

	private final String jobGroup;

	private final String jobName;

	private final String cronExpression;

}
