package my.com.mandrill.component.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.IntegrationLoanPlusProperties;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.LoanEligibility;
import my.com.mandrill.component.domain.LoanEligibilityApplicant;
import my.com.mandrill.component.domain.LoanEligibilityProduct;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.LoanEligibilityApplicantRequest;
import my.com.mandrill.component.dto.request.LoanEligibilityProductRequest;
import my.com.mandrill.component.dto.request.LoanEligibilityRequest;
import my.com.mandrill.component.dto.response.LoanEligibilityResponse;
import my.com.mandrill.component.dto.response.ProductSuggestedValueResponse;
import my.com.mandrill.component.service.InternalLoanEligibilityIntegrationService;
import my.com.mandrill.component.service.LoanEligibilityIntegrationService;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.model.LoanPlusReportDTO;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;
import my.com.mandrill.utilities.general.util.DateUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("loan-eligibilities")
public class LoanEligibilityController {

	private final AccountFeignClient accountFeignClient;

	private final ********************** loanEligibilityService;

	private final LoanEligibilityIntegrationService loanEligibilityIntegrationService;

	private final InternalLoanEligibilityIntegrationService internalLoanEligibilityIntegrationService;

	private final IntegrationLoanPlusProperties integrationLoanPlusProperties;

	@PostMapping("applicant")
	public LoanEligibilityApplicantDTO createNewApplicant(@Valid @RequestBody LoanEligibilityRequest request)
			throws JsonProcessingException {
		CurrentUserIdDTO userDTO = accountFeignClient.getCurrentUserId();

		LoanEligibility latest = null;
		if (StringUtils.isNotBlank(request.getEntityId())) {
			latest = loanEligibilityService.findLatest(userDTO.getId(), request.getEntityName(), request.getEntityId());
		}

		loanEligibilityIntegrationService.validateNewApplicant(request);
		LoanEligibilityApplicant applicant = loanEligibilityService.createNewApplicant(request, userDTO);
		LoanEligibilityApplicantDTO result = MapStructConverter.MAPPER.toLoanEligibilityApplicantDTO(applicant);
		result.setIdType(accountFeignClient.findIdTypeById(result.getIdType().getId()));

		loanEligibilityService.updateAppUser(userDTO, result);

		loanEligibilityService.assignLatestRecord(applicant.getLoanEligibility(), latest,
				loanEligibilityIntegrationService.getUserData(userDTO.getId()));
		return result;
	}

	@GetMapping("latest/{entityName}")
	public ResponseEntity<LoanEligibilityResponse> findLatest(@PathVariable EntityName entityName,
			@RequestParam(required = false) String entityId) {
		LoanEligibility loanEligibility = loanEligibilityService.findLatest(SecurityUtil.currentUserId(), entityName,
				entityId);

		if (loanEligibility == null) {
			return ResponseEntity.noContent().build();
		}

		if (StringUtils.isBlank(loanEligibility.getCaseCode())) {
			// Recalculating
			loanEligibilityService.assignLatestRecord(loanEligibility, loanEligibility,
					loanEligibilityIntegrationService.getUserData(SecurityUtil.currentUserId()));
		}

		LoanEligibilityResponse result = MapStructConverter.MAPPER.toLoanEligibilityResponse(loanEligibility);

		if (result.getApplicant() != null) {
			result.getApplicant()
					.setIdType(accountFeignClient.findIdTypeById(result.getApplicant().getIdType().getId()));
		}
		return ResponseEntity.ok(result);
	}

	@GetMapping("{id}/applicant")
	public ResponseEntity<LoanEligibilityApplicantDTO> findApplicant(@PathVariable String id) {
		LoanEligibilityApplicant applicant = loanEligibilityService.findApplicant(id, SecurityUtil.currentUserId());

		if (applicant == null) {
			return ResponseEntity.noContent().build();
		}

		LoanEligibilityApplicantDTO result = MapStructConverter.MAPPER.toLoanEligibilityApplicantDTO(applicant);
		result.setIdType(accountFeignClient.findIdTypeById(result.getIdType().getId()));
		return ResponseEntity.ok(result);
	}

	@PutMapping("{id}/applicant")
	public LoanEligibilityApplicantDTO updateApplicant(@Valid @RequestBody LoanEligibilityApplicantRequest request,
			@PathVariable String id) throws JsonProcessingException {
		CurrentUserIdDTO userDTO = accountFeignClient.getCurrentUserId();

		loanEligibilityIntegrationService.validateApplicant(request);
		LoanEligibilityApplicant applicant = loanEligibilityService.updateApplicant(id, request, userDTO.getId());
		LoanEligibilityApplicantDTO result = MapStructConverter.MAPPER.toLoanEligibilityApplicantDTO(applicant);
		result.setIdType(accountFeignClient.findIdTypeById(result.getIdType().getId()));

		loanEligibilityService.updateAppUser(userDTO, result);

		return result;
	}

	@PutMapping("{id}/incomes")
	public LoanEligibilityIncomeDTO updateIncomes(@Valid @RequestBody LoanEligibilityIncomeDTO request,
			@PathVariable String id) {
		loanEligibilityService.updateIncomes(id, request, SecurityUtil.currentUserId());
		return MapStructConverter.MAPPER
				.toLoanEligibilityIncomeDTO(loanEligibilityService.findIncomes(id, SecurityUtil.currentUserId()));
	}

	@GetMapping("{id}/incomes")
	public LoanEligibilityIncomeDTO findIncomes(@PathVariable String id) {
		return MapStructConverter.MAPPER
				.toLoanEligibilityIncomeDTO(loanEligibilityService.findIncomes(id, SecurityUtil.currentUserId()));
	}

	@PutMapping("{id}/commitments")
	public LoanEligibilityCommitmentDTO updateCommitments(@Valid @RequestBody LoanEligibilityCommitmentDTO request,
			@PathVariable String id) {
		loanEligibilityService.updateCommitments(id, request, SecurityUtil.currentUserId());
		return MapStructConverter.MAPPER.toLoanEligibilityCommitmentDTO(
				loanEligibilityService.findCommitments(id, SecurityUtil.currentUserId()));
	}

	@GetMapping("{id}/commitments")
	public LoanEligibilityCommitmentDTO findCommitments(@PathVariable String id) {
		return MapStructConverter.MAPPER.toLoanEligibilityCommitmentDTO(
				loanEligibilityService.findCommitments(id, SecurityUtil.currentUserId()));
	}

	@GetMapping("{id}/product")
	public ResponseEntity<LoanEligibilityProductDTO> findProduct(@PathVariable String id) {
		LoanEligibilityProduct product = loanEligibilityService.findProduct(id, SecurityUtil.currentUserId());

		if (product == null) {
			return ResponseEntity.noContent().build();
		}

		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanEligibilityProductDTO(product));
	}

	@PutMapping("{id}/product")
	public LoanEligibilityProductDTO updateProduct(@Valid @RequestBody LoanEligibilityProductRequest request,
			@PathVariable String id) {
		LoanEligibilityProduct product = loanEligibilityService.updateProduct(id, request,
				SecurityUtil.currentUserId());
		return MapStructConverter.MAPPER.toLoanEligibilityProductDTO(product);
	}

	@PutMapping("{id}/case-creation")
	public LoanEligibilityDTO caseCreation(@PathVariable String id) {

		LoanEligibility loanEligibility;
		if (integrationLoanPlusProperties.isEnabled()) {
			loanEligibility = loanEligibilityIntegrationService.caseCreation(id, SecurityUtil.currentUserId());
		}
		else {
			loanEligibility = internalLoanEligibilityIntegrationService.caseCreationInternal(id,
					SecurityUtil.currentUserId());
		}

		LoanEligibilityDTO result = MapStructConverter.MAPPER.toLoanEligibilityDTO(loanEligibility);
		result.setLoanPlusEnabled(integrationLoanPlusProperties.isEnabled());

		return result;
	}

	@PutMapping("{id}/pre-approval")
	public LoanEligibilityResponse preApproval(@PathVariable String id) {

		LoanEligibility loanEligibility;
		if (integrationLoanPlusProperties.isEnabled()) {
			loanEligibility = loanEligibilityIntegrationService.preApproval(id, SecurityUtil.currentUserId());
		}
		else {
			loanEligibility = internalLoanEligibilityIntegrationService.preApprovalInternal(id,
					SecurityUtil.currentUserId());
		}

		return MapStructConverter.MAPPER.toLoanEligibilityResponse(loanEligibility);
	}

	@PutMapping("{id}/lender-assignment")
	public LoanEligibilityResponse lenderAssignment(@PathVariable String id, @Valid @RequestBody ObjectRequest lender) {
		LoanEligibility loanEligibility = loanEligibilityIntegrationService.lenderAssignment(id,
				SecurityUtil.currentUserId(), lender);
		return MapStructConverter.MAPPER.toLoanEligibilityResponse(loanEligibility);
	}

	@GetMapping("{id}/product/suggested-value")
	public ResponseEntity<ProductSuggestedValueResponse> productSuggestedValue(@PathVariable String id) {

		ProductSuggestedValueResponse result = loanEligibilityIntegrationService.productSuggestedValue(id,
				SecurityUtil.currentUserId());

		return ResponseEntity.ok(result);
	}

	@Hidden
	@GetMapping("integration/report")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_READ_LOAN_PLUS)")
	public ResponseEntity<List<LoanPlusReportDTO>> exportDataReport(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate) {
		return ResponseEntity.ok(loanEligibilityIntegrationService.exportReportData(startDate, endDate));
	}

	@GetMapping("pagination")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_READ_LOAN_PLUS)")
	public ResponseEntity<Page<LoanPlusReportDTO>> exportDataReport(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate, Pageable pageable) {
		return ResponseEntity.ok(loanEligibilityIntegrationService.exportReportData(startDate, endDate, pageable));
	}

}
