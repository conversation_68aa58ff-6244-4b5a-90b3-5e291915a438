<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <include file="liquibase/changelog/common-component_20230222_0001_initial_tables.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230222_0002_initial_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230302_jose_01_new_entity_user_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230313_jose_0001_new_entity_journey_configuration.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230315_jose_0001_alter_entity_user_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230316_jose_0001_alter_entity_user_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230316_jose_0002_insert_journey_configuration_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230318_darmawan_0001_added_entity_attachment_group.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230318_darmawan_0002_added_entity_attachment.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230322_wuikeat_0001_insert_journey_configuration_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230323_jose_0001_new_entity_interest_type.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230323_jose_0002_insert_interest_type_data.xml"
             relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230323_jose_0003_insert_journey_configuration_interest_type_data.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230323_jose_0004_new_entity_user_journey_interest_type.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230327_jose_0001_new_entity_journey_configuration_sub.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230328_jose_0001_insert_data_journey_configuration_sub.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230329_muhdlaziem_0001_added_utility_table.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230406_jose_0001_insert_data_property.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230410_jose_0001_update_journey_configuration_data.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230412_jose_0001_drop_table_journery_config_sub.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230419_jose_0001_drop_alter_add_tables.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230425_jose_0001_delete_table_data.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230425_jose_0002_add_FK_constraint.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230426_jose_0001_insert_data.xml"
            relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230428_muhdlaziem_0001_vehicle_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230503_muhdlaziem_0001_utility_refactor.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230503_agustri_0001_add_field_active_country.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230505_wuikeat_0001_add_column_in_utility_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230505_wuikeat_0002_utility_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230507_jose_0001_alter_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230507_jose_0002_alter_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230508_jose_0001_alter_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230508_jose_0002_added_utility_type_table_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230508_jose_0003_alter_table_utility_type.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230511_wuikeat_0001_bank_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230512_jose_0001_alter_table_journey_configuration.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230515_jose_0001_update_table_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230512_wuikeat_0001_rename_bank_intro_to_form.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230512_wuikeat_0002_credit_card_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230515_jose_0002_alter_table_journey_configuration_group.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230516_jose_0001_insurance_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230516_jose_0002_update_property_interest_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230516_jose_0003_update_journey_configuration_group_data.xml"
             relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230517_wuikeat_0001_interest_typ_credit_card_journey_configuration.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230518_jose_0001_remove_current_bank_creditcard_configurations.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230518_jose_0002_alter_table_interest_type.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230518_jose_0003_re-insert_bank_cc_journey.xml"
            relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230518_andy_0001_loan_journey.xml"/>
    <include file="liquibase/changelog/common-component_20230512_agustri_0001_upload_transaction.xml"/>
    <include
            file="liquibase/changelog/common-component_20230522_jose_0001_alter_table_user_journey.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230523_jose_0001_alter_table_insert_data_journey_configuration.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230524_jose_0001_alter_table.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230524_jose_0002_insert_INFO_Screen_data.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230525_jose_0001_insert_INFO_Screen_data.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230529_wuikeat_0001_interest_type_for_credit_card_change_name.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230529_wuikeat_0002_interest_type_change_name_for_credit_card_balance_transfer.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230529_jose_0001_journey_configuration_change_credit_card_name.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230530_jose_0001_update_skip_on_complete.xml"
            relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/common-component_20230531_jose_0001_update_loan_interest_point_to_loan_info_flow.xml"
            relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/common-component_20230601_andy_0001_update_loan_interest_name.xml"/>
    <include file="liquibase/changelog/common-component_20230601_andy_0002_delete_unused_user_journey.xml"/>
    <include file="liquibase/changelog/common-component_20230523_wuikeat_0001_alter_table_attachment_group.xml"/>
    <include file="liquibase/changelog/common-component_20230529_wuikeat_0001_add_doc_expiry_date.xml"/>
    <include file="liquibase/changelog/common-component_20230531_wuikeat_0001_create_vaulty_type.xml"/>
    <include file="liquibase/changelog/common-component_20230601_wuikeat_0001_create_identity_type.xml"/>
    <include file="liquibase/changelog/common-component_20230601_wuikeat_0002_add_document_type.xml"/>
    <include file="liquibase/changelog/common-component_20230608_agustri_0001_add_column_upload_transaction.xml"/>
    <include file="liquibase/changelog/common-component_20230614_muhdlaziem_0001_add_encrypt_attachment.xml"/>
    <include file="liquibase/changelog/common-component_20230619_andy_0001_insurance_vehicle_journey.xml"/>
    <include file="liquibase/changelog/common-component_20230620_andy_0001_alter_table_user_journey_origin.xml"/>
    <include file="liquibase/changelog/common-component_20230623_agustri_0001_remove_constraint_utility_type.xml"/>
    <include file="liquibase/changelog/common-component_20230623_agustri_0002_alter_code_utility_type_not_null.xml"/>
    <include file="liquibase/changelog/common-component_20230622_agustri_0001_journey_inactive_utility.xml"/>
    <include file="liquibase/changelog/common-component_20230628_andy_0001_user_journey_ai_mapping.xml"/>
    <include file="liquibase/changelog/common-component_20230630_andy_0001_user_journey_interest_type_id.xml"/>
    <include file="liquibase/changelog/common-component_20230630_andy_0001_user_journey_update_ai_mapping.xml"/>
    <include file="liquibase/changelog/common-component_20230619_wuikeat_0001_vault_drop_down_enhancement.xml"/>
    <include file="liquibase/changelog/common-component_20230621_wuikeat_0001_add_vault_type.xml"/>
    <include file="liquibase/changelog/common-component_20230628_wuikeat_0001_remove_document_type_name.xml"/>
    <include file="/liquibase/changelog/common-component_20230628_wuikeat_0001_add_ocr_type.xml"/>
    <include file="/liquibase/changelog/common-component_20230703_agustri_0001_add_system_configuration.xml"/>
    <include file="/liquibase/changelog/common-component_20230709_muhdlaziem_0001_vehicle_uj.xml"/>
    <include file="/liquibase/changelog/common-component_20230712_muhdlaziem_0001_vehicle_uj_skip_null.xml"/>
    <include file="/liquibase/changelog/common-component_20230710_wuikeat_0001_create_know_your_card_user_journey.xml"/>
    <include file="/liquibase/changelog/common-component_20230713_andy_0001_loan_user_journey_update.xml"/>
    <include file="/liquibase/changelog/common-component_20230721_wuikeat_0001_add_flag_for_others.xml"/>
    <include file="/liquibase/changelog/common-component_20230803_wuikeat_0001_set_skip_to_step_to_null.xml"/>
    <include file="/liquibase/changelog/common-component_20230818_andy_0001_product_type_group_platform.xml"/>
    <include
            file="/liquibase/changelog/common-component_20230822_andy_0001_add_system_configuration_tnc_privacy_version.xml"/>
    <include file="/liquibase/changelog/common-component_20230823_andy_0001_update_product_type_group_platform.xml"/>
    <include file="/liquibase/changelog/common-component_20230908_wuikeat_0001_cater_property_sub_flow.xml"/>
    <include
            file="/liquibase/changelog/common-component_20230911_wuikeat_0001_rename_property_property_uj_description.xml"/>
    <include
            file="/liquibase/changelog/common-component_20230914_anto_0001_new_entity_global_system_configuration.xml"/>
    <include file="/liquibase/changelog/common-component_20230921_anto_0001_new_entity_ekyc.xml"/>
    <include file="/liquibase/changelog/common-component_20230925_andy_0001_update_applicant_id.xml"/>
    <include file="/liquibase/changelog/common-component_20230926_anto_0001_ekyc_trans_update_applicant_id.xml"/>
    <include file="/liquibase/changelog/common-component_20230926_anto_0002_ekyc_trans_rename.xml"/>
    <include file="/liquibase/changelog/common-component_20230926_anto_0003_ekyc_trans_update_fk.xml"/>
    <include file="/liquibase/changelog/common-component_20230928_andy_0001_ekyc_attachment_group_id.xml"/>
    <include file="/liquibase/changelog/common-component_20231006_andy_0001_ekyc_exceed_max_attempt.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231017_andy_0001_add_system_configuration_platform_agreement.xml"/>
    <include
            file="/liquibase/changelog/common-component_20230911_wuikeat_0001_rename_property_property_uj_description.xml"/>
    <include file="/liquibase/changelog/common-component_20230913_maulana_0001_initial_salary_category.xml"/>
    <include file="/liquibase/changelog/common-component_20230913_maulana_0002_feeding_data_salary_category.xml"/>
    <include file="/liquibase/changelog/common-component_20230912_maulana_0001_socso_eis_mtd.xml"/>
    <include file="/liquibase/changelog/common-component_20230912_maulana_0002_insert_socso_eis_mtd.xml"/>
    <include
            file="/liquibase/changelog/common-component_20230914_anto_0001_new_entity_global_system_configuration.xml"/>
    <include
            file="liquibase/changelog/common-component_20230912_wuikeat_0001_know_your_loan_user_journey_configuration.xml"/>
    <include file="liquibase/changelog/common-component_20230915_wuikeat_0001_kyl_sub_flow.xml"/>
    <include
            file="/liquibase/changelog/common-component_20230925_maulana_0001_update_journey_configuration_for_kyl.xml"/>
    <include file="/liquibase/changelog/common-component_20231005_muhdlaziem_0001_loan_limit_product_group.xml"/>
    <include file="/liquibase/changelog/common-component_20231010_maulana_0001_add_config_for_otp_attemp.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231102_wuikeat_0001_correct_critical_illness_insurance_apple_value.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231017_andy_0001_add_system_configuration_platform_agreement.xml"/>
    <include file="/liquibase/changelog/common-component_20231025_wuikeat_0001_add_bank_statement_in_finance_type.xml"/>
    <include file="/liquibase/changelog/common-component_20231116_muhdlaziem_0001_edgeprop_mapping_on state.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231128_wuikeat_0001_new_user_journey_config_property_flow_with_loan_eligibility.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231129_wuikeat_0001_new_user_journey_config_loan_property_flow.xml"/>
    <include file="/liquibase/changelog/common-component_20231130_wuikeat_0001_disable_property_info_as_main_flow.xml"/>
    <include file="/liquibase/changelog/common-component_20231202_andy_0001_alter_table_upload_transaction.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231202_andy_0002_alter_table_upload_transaction_uploader.xml"/>
    <include file="/liquibase/changelog/common-component_20231212_maulana_0001_user_journey_for_vehicle_finology.xml"/>
    <include file="/liquibase/changelog/common-component_20231213_wuikeat_0001_add_journey_step_complete.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231215_maulana_0001_add_missing_journey_step_for_finology.xml"/>
    <include
            file="/liquibase/changelog/common-component_20231219_andy_0001_alter_table_upload_transaction_error_msg.xml"/>
    <include file="/liquibase/changelog/common-component_20231227_maulana_0001_add_missing_uj_for_finology.xml"/>
    <include file="/liquibase/changelog/common-component_20231228_maulana_0001_update_sequence_uj.xml"/>
    <include file="/liquibase/changelog/common-component_20231229_muhdlaziem_0001_vehicle_finology_jcg.xml"/>
    <include file="/liquibase/changelog/common-component_20240103_wuikeat_0001_uj_to_cater_property_1_2_and_2_0.xml"/>
    <include file="/liquibase/changelog/common-component_20240104_wuikeat_fix_interest_type_for_property_and_loan.xml"/>
    <include file="/liquibase/changelog/common-component_20240104_wuikeat_0002_rename_loan_v2_property_type.xml"/>
    <include
            file="/liquibase/changelog/common-component_20240104_wuikeat_0003_add_skip_on_add_account_for_property_uj.xml"/>
    <include
            file="/liquibase/changelog/common-component_20240104_wuikeat_0004_correction_on_interest_type_for_morgage.xml"/>
    <include
            file="liquibase/changelog/common-component_20240119_loongyeat_0001_add_is_editable_flag_for_attachment_group.xml"/>
    <include file="liquibase/changelog/common-component_20240119_loongyeat_0002_add_ekyc_docs_identity_type.xml"/>
    <include file="liquibase/changelog/common-component_20240119_loongyeat_0003_add_attachment_subtype.xml"/>
    <include file="liquibase/changelog/common-component_20240122_loongyeat_0001_make_is_editable_not_null.xml"/>
    <include file="liquibase/changelog/common-component_20240206_wuikeat_0001_add_api_minimum_version.xml"/>
    <include file="liquibase/changelog/common-component_20240208_hobas_0001_alter_country_table.xml"/>
    <include file="liquibase/changelog/common-component_20240208_hobas_0002_update_country_data.xml"/>
    <include
            file="liquibase/changelog/common-component_20240220_wuikeat_0001_global_config_number_registered_user_number.xml"/>
    <include file="liquibase/changelog/common-component_20240415_kuswandi_0001_system_config.xml"/>
    <include file="liquibase/changelog/common-component_20240523_kuswandi_0001_add_finance_type.xml"/>
    <include file="liquibase/changelog/common-component_20240606_kuswandi_0001_new_skip_asset.xml"/>
    <include
            file="liquibase/changelog/common-component_20240606_mengfoong_0001_ekyc_transaction_add_action_and_payload.xml"/>
    <include file="liquibase/changelog/common-component_20240702_kuswandi_0001_add_finance_type.xml"/>
    <include file="liquibase/changelog/common-component_20240710_kuswandi_0001_correction_on_finance_type.xml"/>
    <include file="liquibase/changelog/common-component_20240712_maulana_0001_alter_home_loan_interest_rate.xml"/>
    <include file="liquibase/changelog/common-component_20240724_khairunnisa_0001_new_entity_postcode.xml"/>
    <include file="liquibase/changelog/common-component_20240724_khairunnisa_0002_insert_postcode.xml"/>
    <include file="liquibase/changelog/common-component_20240806_maulana_0001_alter_attachment.xml"/>
    <include file="liquibase/changelog/common-component_20240813_maulana_0001_top_up_number_history.xml"/>
    <include file="liquibase/changelog/common-component_20240814_maulana_0001_atx_init.xml"/>
    <include file="liquibase/changelog/common-component_20240815_maulana_0001_alter_denom_amount.xml"/>
    <include file="liquibase/changelog/common-component_20240812_andy_0001_update_utility_code.xml"/>
    <include file="liquibase/changelog/common-component_20240819_kuswandi_0001_alter_number_trans.xml"/>
    <include file="liquibase/changelog/common-component_20240822_kuswandi_0001_alter_attachment.xml"/>
    <include file="liquibase/changelog/common-component_20240814_mengfoong_0001_kyll.xml"/>
    <include file="liquibase/changelog/common-component_20240823_kuswandi_0001_alter_telco.xml"/>
    <include file="liquibase/changelog/common-component_20240823_kuswandi_0002_add_finance_type.xml"/>
    <include file="liquibase/changelog/common-component_20240824_deniarianto_0001_table_city_district.xml"/>
    <include file="liquibase/changelog/common-component_20241024_monika_0001_insert_city.xml"/>
    <include file="liquibase/changelog/common-component_20241024_monika_0002_insert_postcode.xml"/>
    <include file="liquibase/changelog/common-component_20241024_monika_0003_insert_district.xml"/>
    <include file="liquibase/changelog/common-component_20241028_monika_0001_add_table_city_district.xml"/>
    <include
            file="liquibase/changelog/common-component_20241108_deniarianto_0001_session_management_global_configuration.xml"/>
    <include file="liquibase/changelog/common-component_20241112_diknes_0001_global_config_infobip_sms_enabled.xml"/>
    <include
            file="liquibase/changelog/common-component_20241127_diknes_0001_global_config_infobip_whatsapp_flow_enabled.xml"/>
    <include
            file="liquibase/changelog/common-component_20241205_monika_0001_attachmentgroup_add_public_key_id.xml"/>
    <include file="liquibase/changelog/common-component_20241206_kuswandi_0001_alter_upload-transaction.xml"/>
    <include file="liquibase/changelog/common-component_20241220_monika_0001_create_table_saving_goal_target.xml"/>
    <include file="liquibase/changelog/common-component_20250102_kuswandi_0001_create_table_faq.xml"/>
    <include file="liquibase/changelog/common-component_20250107_monika_0001_create_table_product_suggestion_category.xml"/>
    <include file="liquibase/changelog/common-component_20250107_monika_0002_create_table_product_suggestion.xml"/>
    <include file="liquibase/changelog/common-component_20250117_kuswandi_0001_add_product_suggestion.xml"/>
    <include file="liquibase/changelog/common-component_20241220_diknes_0001_create_table_saving_goal.xml"/>
    <include file="liquibase/changelog/common-component_20241224_deniarianto_0001_create_table_saving_activity.xml"/>
    <include file="liquibase/changelog/common-component_20241227_monika_0001_insert_saving_parameter.xml"/>
    <include file="liquibase/changelog/common-component_20250106_deniarianto_0001_alter_column_amount_saving_activity.xml"/>
    <include file="liquibase/changelog/common-component_20250110_chooiyie_0001_update_product_suggestion.xml"/>
    <include file="liquibase/changelog/common-component_20250116_deniarianto_0001_add_rsm_global_config.xml"/>
    <include file="liquibase/changelog/common-component_20250217_kuswandi_0001_create_table_affiliate_product.xml"/>
    <include file="liquibase/changelog/common-component_20250225_monika_0001_add_product_type.xml"/>
    <include file="liquibase/changelog/common-component_20250226_monika_0001_insert_affiliate_products.xml"/>
    <include file="liquibase/changelog/common-component_20250226_monika_0002_update_affiliate_products.xml"/>
    <include file="liquibase/changelog/common-component_20250227_monika_0001_insert_affiliate_products.xml"/>
    <include file="liquibase/changelog/common-component_20250227_monika_0001_update_jomhibah_product.xml"/>
    <include file="liquibase/changelog/common-component_20250228_kuswandi_0001_create_table_provider_product_types.xml"/>
    <include file="liquibase/changelog/common-component_20250302_monika_0001_insert_provider_product_types.xml"/>
    <include file="liquibase/changelog/common-component_20250303_monika_0001_insert_provider_product_types.xml"/>
    <include file="liquibase/changelog/common-component_20250304_deniarianto_0001_update_stashaway_name.xml"/>
    <include file="liquibase/changelog/common-component_20250312_kuswaandi_0001_update_product_suggestion_category.xml"/>
    <include file="liquibase/changelog/common-component_20250403_monika_0001_insert_product_suggestions.xml"/>
    <include file="liquibase/changelog/common-component_20250403_chooiyie_0001_update_affiliate_products.xml"/>
    <include file="liquibase/changelog/common-component_20250403_chooiyie_0003_update_provider_product_types.xml"/>
    <include file="liquibase/changelog/common-component_20250407_chooiyie_0001_update_product_suggestion.xml"/>
    <include file="liquibase/changelog/common-component_20250411_kuswandi_0001_download_transaction.xml"/>
    <include file="liquibase/changelog/common-component_20250414_kuswandi_0001_alter_download_transaction.xml"/>
    <include file="liquibase/changelog/common-component_20250414_kuswandi_0002_alter_download_transaction.xml"/>
    <include file="liquibase/changelog/common-component_20250414_monika_0001_remove_provider_product_type.xml"/>
    <include
            file="liquibase/changelog/common-component_20250520_diknes_0001_update_apex_securities_affiliate_products.xml"/>
    <include
            file="liquibase/changelog/common-component_20250520_diknes_0002_update_apex_securities_provider_product_types.xml"/>
    <include
            file="liquibase/changelog/common-component_20250520_diknes_0003_update_apex_securities_product_suggestion.xml"/>
    <include
            file="liquibase/changelog/common-component_20250521_weishun_0001_alter_product_suggestion_add_column_exclusion_tag.xml"/>
    <include file="liquibase/changelog/common-component_20250521_weishun_0002_insert_product_suggestions.xml"/>
    <include file="liquibase/changelog/common-component_20250502_deniarianto_0001_add_fail_login_and_enabled_device_bind.xml"/>
    <include file="liquibase/changelog/common-component_20250509_kuswandi_0001_alter_download_transaction.xml"/>
    <include file="liquibase/changelog/common-component_20250513_kuswandi_0001_create_table_blood_type.xml"/>
    <include file="liquibase/changelog/common-component_20250513_kuswandi_0002_insert_blood_type_preset.xml"/>
    <include file="liquibase/changelog/common-component_20250521_deniarianto_0001_insert_duplicate_product_retirement.xml"/>
</databaseChangeLog>
