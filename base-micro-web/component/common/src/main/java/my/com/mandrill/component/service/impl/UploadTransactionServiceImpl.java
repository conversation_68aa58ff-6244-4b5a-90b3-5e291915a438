package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constants.UploadTransactionStatus;
import my.com.mandrill.component.domain.UploadTransaction;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.UploadTransactionRepository;
import my.com.mandrill.component.service.UploadTransactionService;
import my.com.mandrill.utilities.general.constant.UploadTransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class UploadTransactionServiceImpl implements UploadTransactionService {

	private final UploadTransactionRepository uploadTransactionRepository;

	public UploadTransaction findById(String id) {
		return this.uploadTransactionRepository.findById(id)
				.orElseThrow(ExceptionPredicate.uploadTransactionNotFoundById(id));
	}

	@Override
	public Page<UploadTransaction> findAllUploadTransactions(Pageable page, UploadTransactionType type,
			String institutionId) {
		return this.uploadTransactionRepository.findAllByTypeAndInstitutionId(page, type, institutionId);
	}

	@Override
	public UploadTransaction findByIdAndInstitutionId(String id, String institutionId) {
		return this.uploadTransactionRepository.findByIdAndInstitutionId(id, institutionId)
				.orElseThrow(ExceptionPredicate.uploadTransactionNotFoundByIdAndInstitutionId(id, institutionId));
	}

	@Override
	@Transactional
	public UploadTransaction create(UploadTransaction uploadTransaction) {
		return this.uploadTransactionRepository.save(uploadTransaction);
	}

	@Override
	@Transactional
	public UploadTransaction update(UploadTransaction uploadTransaction) {
		UploadTransaction old = this.findById(uploadTransaction.getId());

		old.setFileName(uploadTransaction.getFileName());
		old.setType(uploadTransaction.getType());
		old.setStatus(uploadTransaction.getStatus());
		old.setUploadTime(uploadTransaction.getUploadTime());
		old.setSuccessRecords(uploadTransaction.getSuccessRecords());
		old.setFailRecords(uploadTransaction.getFailRecords());
		old.setErrorMessage(uploadTransaction.getErrorMessage());
		old.setUploadBy(uploadTransaction.getUploadBy());

		return this.uploadTransactionRepository.save(old);
	}

	@Override
	public boolean existsInProgressUpload(UploadTransactionType type, String institutionId) {
		return this.uploadTransactionRepository.existsByTypeAndInstitutionIdAndStatus(type, institutionId,
				UploadTransactionStatus.IN_PROGRESS);
	}

}
