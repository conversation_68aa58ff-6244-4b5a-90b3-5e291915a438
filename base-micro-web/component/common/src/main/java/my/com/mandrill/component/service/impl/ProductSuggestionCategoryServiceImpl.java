package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.ProductSuggestion;
import my.com.mandrill.component.domain.ProductSuggestionCategory;
import my.com.mandrill.component.repository.jpa.ProductSuggestionCategoryRepository;
import my.com.mandrill.component.service.ProductSuggestionCategoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Set;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class ProductSuggestionCategoryServiceImpl implements ProductSuggestionCategoryService {

	private final ProductSuggestionCategoryRepository productSuggestionCategoryRepository;

	@Override
	public List<ProductSuggestionCategory> findAllWithProductSuggestion(String type) {
		return productSuggestionCategoryRepository.findAllWithProductSuggestions(type).stream()
				.filter(category -> CollectionUtils.isNotEmpty(category.getProductSuggestions()))
				.peek(category -> category.getProductSuggestions()
						.sort(Comparator.comparing(ProductSuggestion::getSequence)))
				.toList();
	}

	@Override
	public List<ProductSuggestionCategory> findAllWithProductSuggestionWithExclusions(String type,
			Set<String> excludedTags) {
		return productSuggestionCategoryRepository.findAllWithProductSuggestionWithExclusions(type, excludedTags);
	}

}
