package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constants.ProfileCompletionModuleType;
import my.com.mandrill.component.domain.IdentityType;
import my.com.mandrill.component.domain.UserSkipAssetResponse;
import my.com.mandrill.component.dto.model.CompletionCountDTO;
import my.com.mandrill.component.dto.model.ProfileCompletionDTO;
import my.com.mandrill.component.dto.response.CompletionProfileResponse;
import my.com.mandrill.component.dto.response.UserJourneyResponse;
import my.com.mandrill.component.repository.jpa.UserSkipAssetResponseRepository;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.*;
import my.com.mandrill.utilities.feign.dto.response.AdvertisementResponse;
import my.com.mandrill.utilities.general.constant.Constant;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.JourneyConfigurationGroupEnum;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.math3.util.Pair;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static my.com.mandrill.utilities.general.constant.EntityName.LOAN_LIMIT;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProfileCompletionServiceImpl implements ProfileCompletionService {

	private final BankFeignClient bankFeignClient;

	private final UserJourneyService userJourneyService;

	private final EkycIntegrationService ekycIntegrationService;

	private final VaultService vaultService;

	private final IdentityTypeService identityTypeService;

	private final FinancialAnalysisFeignClient financialAnalysisFeignClient;

	private final InsuranceFeignClient insuranceFeignClient;

	private final PromotionFeignClient promotionFeignClient;

	private final PropertyFeignClient propertyFeignClient;

	private final UtilityFeignClient utilityFeignClient;

	private final VehicleFeignClient vehicleFeignClient;

	private final UserSkipAssetResponseRepository userSkipAssetResponseRepository;

	private final InvestmentFeignClient investmentFeignClient;

	@Override
	public List<ProfileCompletionDTO> getProfileCompletion(List<ProfileCompletionModuleType> modules, String userId) {
		List<UserJourneyResponse> completedUjs = MapStructConverter.MAPPER
				.toUserJourneyResponse(userJourneyService.findByCompleted(userId, null, null));

		return modules.stream().map(module -> {
			if (completedUjs.isEmpty()) {
				return new ProfileCompletionDTO(module, 0L);
			}

			switch (module) {
				case EKYC -> {
					return new ProfileCompletionDTO(module, ekycIntegrationService.countCompleted(userId));
				}
				case DRIVING_LICENSE -> {
					IdentityType identityType = identityTypeService
							.findByCodeAndActiveTrue(IdentityType.DRIVING_LICENSE);
					return new ProfileCompletionDTO(module, vaultService.countDocuments(userId, identityType.getId()));
				}
				case PASSPORT -> {
					IdentityType identityType = identityTypeService.findByCodeAndActiveTrue(IdentityType.PASSPORT);
					return new ProfileCompletionDTO(module, vaultService.countDocuments(userId, identityType.getId()));
				}
				case FINANALYSIS -> {
					long count = financialAnalysisFeignClient.countCompleted();
					return new ProfileCompletionDTO(module, count);
				}
				case KYLL -> {

					// https://mandrill.atlassian.net/browse/PRJA-1167
					// Hide this module on FE if campaign is
					// inactive.
					Optional<AdvertisementResponse> kyllAd = promotionFeignClient
							.findAllActiveAdvertisements(Sort.unsorted(), "en").stream()
							.filter(ads -> Constant.PROMOTION_ADVERTISEMENT_KYLL_ID.equals(ads.getId())).findFirst();
					if (kyllAd.isEmpty()) {
						return new ProfileCompletionDTO(module, null);
					}

					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.kyllJourneyCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, bankFeignClient.countLoanLimit());
					}
					break;
				}
				case BANK -> {
					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.bankJourneyCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, bankFeignClient.countBank(EntityName.BANK));
					}
					break;
				}
				case PROPERTY -> {
					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.propertyJourneyCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, propertyFeignClient.count());
					}
					break;
				}
				case CREDIT_CARD -> {
					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.creditCardCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, bankFeignClient.countBank(EntityName.CREDIT_CARD));
					}
					break;
				}
				case VEHICLE -> {
					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.vehicleJourneyCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, vehicleFeignClient.count());
					}
					break;
				}
				case UTILITY -> {
					return new ProfileCompletionDTO(module, utilityFeignClient.count());
				}
				case INSURANCE -> {
					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.insuranceJourneyCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, insuranceFeignClient.count());
					}
					break;
				}
				case LOAN -> {
					if (completedUjs.stream().anyMatch(ujResponse -> JourneyConfigurationGroupEnum.loanJourneyCodes
							.contains(ujResponse.getJourneyGroupName()))) {
						return new ProfileCompletionDTO(module, bankFeignClient.countLoan());
					}
					break;
				}
				default -> {
					return new ProfileCompletionDTO(module, null);
				}
			}

			return new ProfileCompletionDTO(module, 0L);
		}).toList();
	}

	private static Function<Pair<ProfileCompletionModuleType, Long>, CompletionCountDTO> getPairCompletionCountDTOFunction(
			Set<ProfileCompletionModuleType> skipAssetSet, Set<ProfileCompletionModuleType> hideModule) {
		Function<Pair<ProfileCompletionModuleType, Long>, Boolean> checkingComplete = (v) -> {
			if (skipAssetSet.contains(v.getFirst())) {
				return true;
			}
			if (Objects.isNull(v.getSecond())) {
				return false;
			}
			return v.getSecond() > 0;
		};

		return (pair) -> CompletionCountDTO.builder().module(pair.getFirst()).isComplete(checkingComplete.apply(pair))
				.isSkip(skipAssetSet.contains(pair.getFirst())).count(pair.getSecond())
				.hide(hideModule.contains(pair.getFirst())).build();
	}

	@Override
	public CompletableFuture<CompletionProfileResponse> getProfileCompletionCount(String userId, Boolean isRevamp) {
		ProfileCompletionModuleType[] modules = ProfileCompletionModuleType.values();

		Map<EntityName, ProfileCompletionModuleType> entityToProfileModule = Map.ofEntries(
				Map.entry(EntityName.BANK, ProfileCompletionModuleType.BANK),
				Map.entry(EntityName.PROPERTY, ProfileCompletionModuleType.PROPERTY),
				Map.entry(EntityName.CREDIT_CARD, ProfileCompletionModuleType.CREDIT_CARD),
				Map.entry(EntityName.VEHICLE, ProfileCompletionModuleType.VEHICLE),
				Map.entry(EntityName.UTILITY, ProfileCompletionModuleType.UTILITY),
				Map.entry(EntityName.INSURANCE, ProfileCompletionModuleType.INSURANCE),
				Map.entry(EntityName.LOAN, ProfileCompletionModuleType.LOAN),
				Map.entry(EntityName.INVESTMENT, ProfileCompletionModuleType.INVESTMENT));

		List<UserSkipAssetResponse> skipAssets = userSkipAssetResponseRepository.findByUserId(userId);
		Set<ProfileCompletionModuleType> skipAssetSet = skipAssets.stream()
				.map(item -> entityToProfileModule.get(item.getEntityName())).collect(Collectors.toSet());

		Map<ProfileCompletionModuleType, Supplier<Long>> suppliers = new HashedMap<>();
		suppliers.put(ProfileCompletionModuleType.EKYC, () -> ekycIntegrationService.countCompleted(userId));
		suppliers.put(ProfileCompletionModuleType.DRIVING_LICENSE, () -> {
			IdentityType identityType = identityTypeService.findByCodeAndActiveTrue(IdentityType.DRIVING_LICENSE);
			return vaultService.countDocuments(userId, identityType.getId());
		});
		suppliers.put(ProfileCompletionModuleType.PASSPORT, () -> {
			IdentityType identityType = identityTypeService.findByCodeAndActiveTrue(IdentityType.PASSPORT);
			return vaultService.countDocuments(userId, identityType.getId());
		});
		suppliers.put(ProfileCompletionModuleType.FINANALYSIS, financialAnalysisFeignClient::countCompleted);
		suppliers.put(ProfileCompletionModuleType.KYLL, () -> {
			Optional<AdvertisementResponse> kyllAd = promotionFeignClient
					.findAllActiveAdvertisements(Sort.unsorted(), "en").stream()
					.filter(ads -> LOAN_LIMIT.equals(ads.getEntityName())).findFirst();
			if (kyllAd.isEmpty()) {
				return null;
			}
			return bankFeignClient.countLoanLimit();
		});

		suppliers.put(ProfileCompletionModuleType.BANK, () -> bankFeignClient.countBank(EntityName.BANK));
		suppliers.put(ProfileCompletionModuleType.PROPERTY, propertyFeignClient::count);
		suppliers.put(ProfileCompletionModuleType.CREDIT_CARD, () -> bankFeignClient.countBank(EntityName.CREDIT_CARD));
		suppliers.put(ProfileCompletionModuleType.VEHICLE, vehicleFeignClient::count);
		suppliers.put(ProfileCompletionModuleType.UTILITY, utilityFeignClient::count);
		suppliers.put(ProfileCompletionModuleType.INSURANCE, insuranceFeignClient::count);
		suppliers.put(ProfileCompletionModuleType.LOAN, bankFeignClient::countLoan);

		if (Boolean.TRUE.equals(isRevamp)) {
			suppliers.put(ProfileCompletionModuleType.INVESTMENT, investmentFeignClient::count);
		}

		List<CompletableFuture<Pair<ProfileCompletionModuleType, Long>>> futures = new ArrayList<>();
		for (ProfileCompletionModuleType module : modules) {
			Supplier<Long> supplier = suppliers.get(module);
			if (Objects.nonNull(supplier)) {
				log.info("register supplier to future: {}", module);

				RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
				futures.add(CompletableFuture.supplyAsync(() -> {
					RequestContextHolder.setRequestAttributes(requestAttributes);
					log.info("executing count for module : {}", module);
					return new Pair<>(module, supplier.get());
				}).exceptionally((___) -> {
					log.error("exceptionally handle for module: {} fallback value returned", module);
					return new Pair<>(module, 0L);
				}));
			}
		}

		return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenApply(__ -> {
			Map<ProfileCompletionModuleType, ProfileCompletionModuleType> specialMerge = Map.of(
					ProfileCompletionModuleType.EKYC, ProfileCompletionModuleType.EKYC,
					ProfileCompletionModuleType.DRIVING_LICENSE, ProfileCompletionModuleType.EKYC,
					ProfileCompletionModuleType.PASSPORT, ProfileCompletionModuleType.EKYC);

			Set<ProfileCompletionModuleType> hideModule = Set.of(ProfileCompletionModuleType.INVESTMENT);

			log.info("success receive data from future...");

			Function<Pair<ProfileCompletionModuleType, Long>, CompletionCountDTO> completionCountFunction = getPairCompletionCountDTOFunction(
					skipAssetSet, hideModule);

			List<CompletionCountDTO> completions = futures.stream().map(CompletableFuture::join)
					.map(completionCountFunction).filter(v -> Objects.nonNull((v.getCount())))
					.collect(Collectors.toList());

			Set<ProfileCompletionModuleType> total = new HashSet<>();
			Set<ProfileCompletionModuleType> completed = new HashSet<>();

			for (CompletionCountDTO cc : completions) {
				if (hideModule.contains(cc.getModule())) {
					continue;
				}

				total.add(specialMerge.getOrDefault(cc.getModule(), cc.getModule()));
				if (cc.getIsComplete()) {
					completed.add(specialMerge.getOrDefault(cc.getModule(), cc.getModule()));
				}
			}

			double percentage = 0.0;
			if (!total.isEmpty()) {
				percentage = (double) completed.size() / total.size();
			}

			return new CompletionProfileResponse(completions, total.size(), completed.size(), percentage);
		});
	}

}
