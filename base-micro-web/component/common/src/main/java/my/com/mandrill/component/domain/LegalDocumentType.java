package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "legal_document_type", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
public class LegalDocumentType extends AuditSection {

	@Size(max = 255)
	@NotBlank
	@Column(name = "code", nullable = false)
	private String code;

	@Size(max = 255)
	@NotBlank
	@Column(name = "name", nullable = false)
	private String name;

	@Size(max = 255)
	@Column(name = "description")
	private String description;

	@NotNull
	@Column(name = "active", nullable = false, columnDefinition = "BOOLEAN DEFAULT TRUE")
	private Boolean active = true;

	@Column(name = "others", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
	private boolean others = false;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		LegalDocumentType that = (LegalDocumentType) o;
		return getId() != null && Objects.equals(getId(), that.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
