package my.com.mandrill.component.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "survey")
public class SurveyProperties {

	private List<String> salaryRangeQuestionIds = new ArrayList<>();

}
