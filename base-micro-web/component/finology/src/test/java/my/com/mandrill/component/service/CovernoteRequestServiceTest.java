package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.CoverNoteRequest;
import my.com.mandrill.component.domain.CoverNoteResponse;
import my.com.mandrill.component.repository.jpa.CovernoteRequestRepository;
import my.com.mandrill.component.service.impl.CovernoteRequestServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CovernoteRequestServiceTest {

	@InjectMocks
	CovernoteRequestServiceImpl covernoteRequestService;

	@Mock
	CovernoteRequestRepository covernoteRequestRepository;

	HashMap<String, CoverNoteResponse> coverNoteResponseHashMapMock;

	CoverNoteRequest coverNoteRequestMock;

	List<CoverNoteRequest> coverNoteRequestListMock;

	@BeforeEach
	void setup() {
		coverNoteResponseHashMapMock = new HashMap<>();
		coverNoteRequestMock = new CoverNoteRequest();
		coverNoteRequestListMock = new ArrayList<>();
	}

	@Test
	void findCovernoteByQuotationNo_GiveValidArgument_Positive() {
		List<String> req = List.of("id");
		coverNoteRequestListMock.add(new CoverNoteRequest());
		when(covernoteRequestRepository.findAllByQuotationNoIn(any())).thenReturn(coverNoteRequestListMock);
		assertThat(covernoteRequestService.findCovernoteByQuotationNo(req)).hasSize(1);
	}

	@Test
	void save_GiveValidArgument_Positive() {
		when(covernoteRequestRepository.save(any())).thenReturn(coverNoteRequestMock);
		assertThat(covernoteRequestService.save(new CoverNoteRequest())).isEqualTo(coverNoteRequestMock);
	}

}
