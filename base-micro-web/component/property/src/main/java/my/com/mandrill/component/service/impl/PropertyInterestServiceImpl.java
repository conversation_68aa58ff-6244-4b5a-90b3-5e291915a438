package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.PropertyInterest;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.PropertyInterestRepository;
import my.com.mandrill.component.repository.jpa.spec.PropertyInterestSpecification;
import my.com.mandrill.component.service.PropertyInterestService;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.query.FluentQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PropertyInterestServiceImpl implements PropertyInterestService {

	private final PropertyInterestRepository propertyInterestRepository;

	@Override
	public void save(PropertyInterest propertyInterest) {
		propertyInterestRepository.save(propertyInterest);
	}

	@Override
	public Page<PropertyInterest> findAll(Instant startDate, Instant endDate, Pageable pageable) {
		return propertyInterestRepository.findBy(PropertyInterestSpecification.filterDateRange(startDate, endDate),
				v -> v.page(pageable));
	}

	@Override
	public List<PropertyInterest> findAll(Instant startDate, Instant endDate) {
		return propertyInterestRepository.findBy(PropertyInterestSpecification.filterDateRange(startDate, endDate),
				FluentQuery.FetchableFluentQuery::all);
	}

	@Override
	public Page<PropertyInterest> findAll(Specification<PropertyInterest> spec, Pageable pageable) {
		return propertyInterestRepository.findAll(spec, pageable);
	}

	@Override
	@Transactional
	public UserInterestRecordRSMViewDTO findRsmLeadDetail(String id) {
		PropertyInterest record = propertyInterestRepository.findById(id)
				.orElseThrow(ExceptionPredicate.propertyInterestNotFound(id));

		UserInterestRecordRSMViewDTO result = MapStructConverter.MAPPER.toUserInterestRecordRSMViewDTO(record);
		result.setProductCategory(ApplicationType.PROPERTY.getName());
		result.setProductType(ApplicationType.PROPERTY.getName());
		result.setApplicationType(ApplicationType.PROPERTY);
		result.setApplicationTypeName(ApplicationType.PROPERTY.getName());
		return result;
	}

	@Override
	@Transactional
	public void updateRsmInfo(LeadRSMUpdateRequest request) {
		if (CollectionUtils.isNotEmpty(request.getIds())) {
			bulkUpdate(request);
			return;
		}
		singleUpdate(request);
	}

	private void bulkUpdate(LeadRSMUpdateRequest request) {
		if (propertyInterestRepository.existsAllByIdInAndRsmStatusAndRsmCommissionAttachedIsTrue(request.getIds(),
				RSMStatus.SUCCESS)) {
			throw new BusinessException(ErrorCodeGlobalEnum.CANNOT_PERFORM_BULK_UPDATE_BECAUSE_SOME_SUCCESS);
		}
		propertyInterestRepository.bulkUpdateStatusByIdIn(request.getStatus(), request.getIds());
	}

	private void singleUpdate(LeadRSMUpdateRequest request) {
		PropertyInterest propertyInterest = propertyInterestRepository.findById(request.getId())
				.orElseThrow(ExceptionPredicate.propertyInterestNotFound(request.getId()));

		propertyInterest.setRsmStatus(request.getStatus());
		propertyInterest.setRsmCommissionAttached(request.isCommissionAttached());
		propertyInterestRepository.save(propertyInterest);
	}

	@Override
	public List<PropertyInterest> findAll(Specification<PropertyInterest> spec) {
		return propertyInterestRepository.findAll(spec);
	}

	@Override
	public Set<String> findAllApplicationIdsExcludingUserIdsIn(Set<String> applicationIds, Set<String> userIds) {
		if (applicationIds.isEmpty())
			return Set.of();

		return propertyInterestRepository.findAllById(applicationIds).stream()
				.filter(u -> !userIds.contains(u.getUserId())).map(PropertyInterest::getId).collect(Collectors.toSet());
	}

}
