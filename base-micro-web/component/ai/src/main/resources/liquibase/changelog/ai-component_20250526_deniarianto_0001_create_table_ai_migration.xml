<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.19.xsd">

    <changeSet id="ai-component_20250526_deniarianto_0001" author="deniarianto">

        <createTable tableName="products">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_product_id"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="issuer_code" type="TEXT"/>
            <column name="issuer_type" type="TEXT"/>
            <column name="entity" type="TEXT"/>
            <column name="card_type" type="TEXT"/>
            <column name="logo" type="TEXT"/>
            <column name="product_link" type="TEXT"/>
            <column name="apply_link" type="TEXT"/>
        </createTable>

        <createTable tableName="product_highlights">
            <column name="id" type="INT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_product_highlight_id"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="product_id" type="VARCHAR(36)">
                <constraints nullable="false" foreignKeyName="fk_product_highlights_products"
                             referencedTableName="products"
                             referencedColumnNames="id"/>
            </column>
            <column name="category" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="highlight_text" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <sql>
            ALTER TABLE ai.product_highlights
                ADD CONSTRAINT chk_category_valid
                    CHECK (category IN ('default', 'additional', 'suggested'));
        </sql>
    </changeSet>
</databaseChangeLog>