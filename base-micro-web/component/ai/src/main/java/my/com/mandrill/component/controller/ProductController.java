package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.IssuerTypeEnum;
import my.com.mandrill.component.domain.ProductGroup;
import my.com.mandrill.component.domain.ProductPlatform;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.ProductGroupDTO;
import my.com.mandrill.component.dto.model.ProductPlatformDTO;
import my.com.mandrill.component.dto.model.ProductSimpleSelectionDTO;
import my.com.mandrill.component.dto.model.ProductSuggestionMatchDTO;
import my.com.mandrill.component.dto.model.ProductTypeDTO;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.component.dto.request.ProductForYouRequest;
import my.com.mandrill.component.dto.request.ProductLoanLimitRequest;
import my.com.mandrill.component.dto.request.ProductSearchRequest;
import my.com.mandrill.component.dto.request.ProductSuggestionRequest;
import my.com.mandrill.component.dto.request.RetirementProductSuggestionRequest;
import my.com.mandrill.component.dto.request.SuggestionLoanLimitRequest;
import my.com.mandrill.component.dto.response.PartnerResponse;
import my.com.mandrill.component.dto.response.ProductResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.ProductIntgService;
import my.com.mandrill.component.service.ProductService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.client.BankFeignClient;
import my.com.mandrill.utilities.feign.client.InsuranceFeignClient;
import my.com.mandrill.utilities.feign.client.InvestmentFeignClient;
import my.com.mandrill.utilities.feign.dto.ProviderDTO;
import my.com.mandrill.utilities.feign.dto.request.LoanEligibilityLenderProductRequest;
import my.com.mandrill.utilities.feign.dto.response.LoanEligibilityLenderProductResponse;
import my.com.mandrill.utilities.feign.dto.response.RefinanceProductResponse;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import my.com.mandrill.utilities.general.dto.BankListDTO;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("product")
@Slf4j
@RequiredArgsConstructor
public class ProductController {

	private static final List<String> GROUP_INSURANCE = List.of("insurance", "insurance-property", "insurance-vehicle");

	private static final List<String> GROUP_BANK = List.of("credit-card", "loan", "loan-property", "loan-vehicle",
			"loan-limit", "bank-account");

	private static final List<String> GROUP_INVESTMENT = List.of("investments");

	private final ProductService productService;

	private final ValidationService validationService;

	private final BankFeignClient bankFeignClient;

	private final InsuranceFeignClient insuranceFeignClient;

	private final InvestmentFeignClient investmentFeignClient;

	private final ObjectMapper objectMapper;

	private final ProductIntgService productIntgService;

	@GetMapping("product-list/{type}")
	public ResponseEntity<String> productList(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type) {

		return productService.findProductList(page, size, type);
	}

	@GetMapping("product-filters/{type}")
	public ResponseEntity<String> productFiltersList(@PathVariable String type,
			@RequestParam(required = false) String recommendationType) {
		return productService.findProductFilters(type, recommendationType);
	}

	@GetMapping("product-details/{type}")
	public ResponseEntity<String> productDetailsList(@PathVariable String type, @RequestParam String productName) {

		return productService.findProductDetails(type, productName);
	}

	@GetMapping("product-compare/{type}")
	public ResponseEntity<String> productCompare(@PathVariable String type, @RequestParam String productName1,
			@RequestParam String productName2) {

		return productService.findProductCompare(type, productName1, productName2);
	}

	@GetMapping("product-category/{group}")
	public ResponseEntity<String> productCategory(@PathVariable String group) {

		return productService.findProductCategory(group);
	}

	@GetMapping("product/{type}")
	public ResponseEntity<String> product(@PathVariable String type, @RequestParam(required = false) String entityName,
			@RequestParam(required = false) String cardType, @RequestParam(required = false) String productName,
			@RequestParam(required = false) String productId) {

		return productService.findProduct(type, entityName, cardType, productName, productId);
	}

	@ServiceToServiceAccess
	@GetMapping("private/product/{type}")
	public ResponseEntity<String> productPrivate(@PathVariable String type,
			@RequestParam(required = false) String entityName, @RequestParam(required = false) String cardType,
			@RequestParam(required = false) String productName, @RequestParam(required = false) String productId) {

		return productService.findProduct(type, entityName, cardType, productName, productId);
	}

	@GetMapping("partner/{issuerType}/{issuerCode}")
	public ResponseEntity<PartnerResponse> getPartnerByIssuerCode(@PathVariable IssuerTypeEnum issuerType,
			@PathVariable String issuerCode) {
		PartnerResponse partnerResponse = null;
		switch (issuerType) {
			case BANK:
				BankListDTO bankList = bankFeignClient.getBankByIssuerCode(issuerCode);
				partnerResponse = PartnerResponse.builder().code(bankList.getCode()).name(bankList.getName())
						.active(bankList.getActive()).isPartner(bankList.getIsPartner())
						.issuerCode(bankList.getIssuerCode()).build();
				break;
			case INSURANCE:
				ProviderDTO provider = insuranceFeignClient.getProviderByIssuerCode(issuerCode);
				partnerResponse = PartnerResponse.builder().name(provider.getName()).active(provider.getActive())
						.isPartner(provider.getIsPartner()).issuerCode(provider.getIssuerCode()).build();
				break;
			default:
				throw new BusinessException(ErrorCodeEnum.ISSUER_TYPE_INVALID);
		}
		return ResponseEntity.ok(partnerResponse);
	}

	@GetMapping("product-filters/loan-limit/{type}")
	public ResponseEntity<String> productFilterLoanLimit(@PathVariable String type) {
		return productService.findProductFilterLoanLimit(type);
	}

	@PostMapping("product-refinance/{type}")
	public ResponseEntity<String> productRefinance(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type,
			@Valid @RequestBody ProductLoanLimitRequest body) {
		return productService.findProductRefinance(type, body, page, size);
	}

	@PostMapping("product-suggestions/loan-limit/{type}")
	public ResponseEntity<String> productSuggestionLoanLimit(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type,
			@Valid @RequestBody SuggestionLoanLimitRequest body) {
		return productService.findProductSuggestionLoanLimit(type, body, page, size);
	}

	/**
	 * @deprecated since 3.0.0
	 */
	@Deprecated(since = "3.0.0")
	@PostMapping("product-suggestions/{type}")
	public ResponseEntity<String> productSuggestions(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @RequestParam(required = false) String userJourneyId,
			@RequestParam(required = false) String group, @PathVariable String type,
			@RequestBody ProductSuggestionRequest body) {

		return productService.findProductSuggestion(page, size, userJourneyId, group, type, body);
	}

	// https://api.sit.moneyx.com.my/api/ai-component/product/product-list/current-account
	/**
	 * @deprecated since 3.0.0
	 */
	@Deprecated(since = "3.0.0")
	@GetMapping("product-for-you")
	public ResponseEntity<String> productForYou(@RequestParam(required = false) String size,
			@RequestParam String userJourneyId) {

		return productService.findProductForYou(size, userJourneyId);
	}

	@GetMapping("/groups")
	public List<ProductGroupDTO> getAllProductGroup(Sort sort, @RequestParam(required = false) Boolean aiJoin) {
		return productService.findAllProductGroups(sort);
	}

	@GetMapping("/platforms")
	public ResponseEntity<List<ProductPlatformDTO>> getAllProductPlatforms(Sort sort) {
		List<ProductPlatform> result = productService.findAllProductPlatform(sort);
		return ResponseEntity.ok(result.stream()
				.map(productPlatform -> objectMapper.convertValue(productPlatform, ProductPlatformDTO.class)).toList());
	}

	@GetMapping("/types/{platformCode}/{groupCode}")
	public ResponseEntity<List<ProductTypeDTO>> getAllProductTypesByPlatformCodeAndGroupCode(
			@SortDefault(sort = "sequence", direction = Sort.Direction.ASC) @SortDefault(sort = "value",
					direction = Sort.Direction.ASC) Sort sort,
			@PathVariable(name = "platformCode") String platformCode,
			@PathVariable(name = "groupCode") String groupCode) {
		List<ProductType> result = productService.findAllProductTypeByPlatformAndGroup(platformCode, groupCode, false,
				sort);
		return ResponseEntity.ok(result.stream()
				.map(productType -> objectMapper.convertValue(productType, ProductTypeDTO.class)).toList());
	}

	@GetMapping("/types/{platformCode}")
	public ResponseEntity<List<ProductTypeDTO>> getAllProductTypesByPlatFormCode(Sort sort,
			@PathVariable(name = "platformCode") String platformCode,
			@RequestParam(required = false) Boolean rsmActive) {
		List<ProductType> result = productService.findAllProductTypeByPlatformAndGroup(platformCode, null, rsmActive,
				sort);
		return ResponseEntity.ok(result.stream()
				.map(productType -> objectMapper.convertValue(productType, ProductTypeDTO.class)).toList());
	}

	@PostMapping(value = "v2/product-suggestions/{type}", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<String> productSuggestionsV2(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type,
			@RequestBody ProductSuggestionRequest body) {

		return productService.findProductSuggestion(page, size, type, body);
	}

	@PostMapping("v2/product-for-you")
	public ResponseEntity<String> productForYouV2(@RequestParam(required = false) String size,
			@RequestBody ProductForYouRequest productForYouRequest) {

		return productService.findProductForYou(size, productForYouRequest);
	}

	@GetMapping("v2/groups")
	public ResponseEntity<String> getAllProductGroup() {
		return productService.findAllProductGroupsAI();
	}

	@Hidden
	@PostMapping("integration/product-refinance/{type}")
	public ResponseEntity<List<LoanEligibilityLenderProductResponse>> getLoanEligibilityRefinanceProducts(
			@PathVariable String type, @Valid @RequestBody LoanEligibilityLenderProductRequest request) {

		List<LoanEligibilityLenderProductResponse> results = new ArrayList<>();
		int page = 1;
		int size = 100;
		final int MAX_PAGES = 50; // upper limit 5k products
		boolean hasMorePages = true;

		try {
			while (hasMorePages && page <= MAX_PAGES) {
				log.info("fetching refinance products, page {}", page);

				ResponseEntity<String> response = productService.findLoanEligibilityRefinanceProducts(type, request,
						String.valueOf(page), String.valueOf(size));

				if (response == null) {
					log.info("product service returned null response");
					break;
				}

				RefinanceProductResponse responseBody = objectMapper.readValue(response.getBody(),
						RefinanceProductResponse.class);

				results.addAll(LoanEligibilityLenderProductResponse.transformResponse(responseBody));

				hasMorePages = page * size < responseBody.getTotalCount();
				page++;
			}
		}
		catch (Exception e) {
			log.error("unexpected error occurred when fetching loan eligibility refinance products : {}",
					e.getMessage());
		}

		return ResponseEntity.ok(results);
	}

	@Hidden
	@PostMapping("integration/product-purchase/{type}")
	public ResponseEntity<List<LoanEligibilityLenderProductResponse>> getLoanEligibilityPurchaseProducts(
			@PathVariable String type, @Valid @RequestBody LoanEligibilityLenderProductRequest request) {

		List<LoanEligibilityLenderProductResponse> results = new ArrayList<>();
		int page = 1;
		int size = 100;
		final int MAX_PAGES = 50; // upper limit 5k products
		boolean hasMorePages = true;

		try {
			while (hasMorePages && page <= MAX_PAGES) {
				log.info("fetching purchase products, page {}", page);

				ResponseEntity<String> response = productService.findLoanEligibilityPurchaseProducts(type, request,
						String.valueOf(page), String.valueOf(size));

				if (response == null) {
					log.info("product service returned null response");
					break;
				}

				RefinanceProductResponse responseBody = objectMapper.readValue(response.getBody(),
						RefinanceProductResponse.class);

				results.addAll(LoanEligibilityLenderProductResponse.transformResponse(responseBody));

				hasMorePages = page * size < responseBody.getTotalCount();
				page++;
			}
		}
		catch (Exception e) {
			log.error("unexpected error occurred when fetching loan eligibility purchase products : {}",
					e.getMessage());
		}

		return ResponseEntity.ok(results);
	}

	@Hidden
	@GetMapping("types/status")
	public boolean findStatusProductTypeForPlatform(@RequestParam String productTypeCode,
			@RequestParam String platformCode) {
		return productService.isActiveProductTypeForPlatform(productTypeCode, platformCode);
	}

	@PostMapping("retirement-product-suggestions")
	public ResponseEntity<String> findProductSuggestionForRetirement(
			@RequestBody RetirementProductSuggestionRequest request, @RequestParam(required = false) String page,
			@RequestParam(required = false) String size) {

		return productService.findProductSuggestionForRetirement(request, page, size);
	}

	@GetMapping("product-suggestions/questionnaire/{type}")
	public List<ProductSuggestionMatchDTO> findProductSuggestionQuestionnaireType(@PathVariable SurveyFormType type) {
		return productService.findProductSuggestionMatch(type);
	}

	@GetMapping("types/group-id/{groupId}/{platformCode}")
	public ResponseEntity<List<ProductTypeDTO>> getProductTypeByGroupId(@PathVariable String groupId,
			@PathVariable(name = "platformCode") String platformCode, Sort sort) {
		List<ProductType> result = productService.findDistinctByProductGroupIdAndProductPlatformCodeAndActive(groupId,
				platformCode, true, sort);

		return ResponseEntity.ok(result.stream()
				.map(productType -> objectMapper.convertValue(productType, ProductTypeDTO.class)).toList());
	}

	@GetMapping("provider/{group}")
	public ResponseEntity<List<ProviderDTO>> getProductProvidersByType(@PathVariable String group, Sort sort) {

		List<ProviderDTO> providers = new ArrayList<>();

		if (GROUP_INVESTMENT.contains(group)) {

			providers = investmentFeignClient.getAllProvidersIntegration(sort);

		}
		else if (GROUP_INSURANCE.contains(group)) {

			providers = insuranceFeignClient.getProviderForProduct(sort);

		}
		else if (GROUP_BANK.contains(group)) {

			providers = bankFeignClient.getAllActiveBankList(sort);

		}

		return ResponseEntity.ok(providers);
	}

	@Hidden
	@PostMapping("product/integration")
	public RsmHeaderProductDTO validateProduct(@RequestBody @Valid RsmHeaderProductDTO product) {
		ProductGroup group = productService.findProductGroupById(product.getProductCategoryId());
		ProductType type = productService.findProductTypeById(product.getProductTypeId());

		product.setProductCategoryName(group.getValue());
		product.setProductTypeName(type.getValue());

		return product;
	}

	@ServiceToServiceAccess
	@GetMapping("/private/find-product-type")
	public List<ProductTypeDTO> getProductType(@RequestParam(name = "groupId", required = false) List<String> groupId,
			@RequestParam(name = "productType", required = false) List<String> productType,
			@RequestParam(name = "platformCode") String platformCode) {
		List<ProductType> result = new ArrayList<>();
		if (!CollectionUtils.isEmpty(groupId)) {
			result = productService.findActiveByGroupIdAndPlatform(groupId, platformCode);
		}
		else if (!CollectionUtils.isEmpty(productType)) {
			result = productService.findActiveProductTypeAndPlatform(productType, platformCode);
		}
		return result.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();

	}

	@GetMapping("/v3/products")
	public List<ProductSimpleSelectionDTO> findProductSimpleSelection(@RequestParam String productType,
			@RequestParam(required = false) String providerId, @RequestParam(required = false) Boolean rsmActive) {
		return productService.findProductSimpleSelection(productType, providerId, rsmActive);
	}

	@PostMapping("/v4/products/{currentInstitutionId}/{platformCode}")
	public ProductResponse findProductWithFlexibleFilter(@Valid @RequestBody ProductSearchRequest request,
			@RequestParam boolean rsmActive, @PathVariable String currentInstitutionId,
			@PathVariable String platformCode) {
		return productIntgService.findProductWithFlexibleFilter(request, rsmActive, currentInstitutionId, platformCode);
	}

}
