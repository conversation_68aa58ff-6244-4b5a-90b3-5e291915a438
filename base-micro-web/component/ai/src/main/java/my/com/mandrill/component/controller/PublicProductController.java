package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.ProductTypeDTO;
import my.com.mandrill.component.dto.request.ProductSuggestionRequest;
import my.com.mandrill.component.service.ProductService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/public/products")
@Slf4j
@RequiredArgsConstructor
public class PublicProductController {

	private final ProductService productService;

	private final ValidationService validationService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("product-list/{type}")
	public ResponseEntity<String> productList(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type) {
		return productService.findProductListPrivate(page, size, type);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("product/{type}")
	public ResponseEntity<String> product(@PathVariable String type, @RequestParam(required = false) String entityName,
			@RequestParam(required = false) String cardType, @RequestParam(required = false) String productName,
			@RequestParam(required = false) String productId) {
		return productService.findProduct(type, entityName, cardType, productName, productId);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("product-details/{type}")
	public ResponseEntity<String> productDetailsList(@PathVariable String type, @RequestParam String productName) {
		return productService.findProductDetails(type, productName);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("product-filters/{type}")
	public ResponseEntity<String> productFiltersList(@PathVariable String type,
			@RequestParam(required = false) String recommendationType) {
		return productService.findProductFilters(type, recommendationType);
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("product-suggestions/{type}")
	public ResponseEntity<String> productSuggestionsV2(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type,
			@RequestBody ProductSuggestionRequest body) {

		return productService.findProductSuggestionPrivate(page, size, type, body);
	}

	@GetMapping("/product-type/{platformCode}/{groupCode}")
	public List<ProductTypeDTO> productTypeByPlatformAndGroup(
			@SortDefault(sort = "sequence", direction = Sort.Direction.ASC) @SortDefault(sort = "value",
					direction = Sort.Direction.ASC) Sort sort,
			@PathVariable(name = "platformCode") String platformCode,
			@PathVariable(name = "groupCode") String groupCode) {
		List<ProductType> result = productService.findAllProductTypeByPlatformAndGroup(platformCode, groupCode, false,
				sort);
		return result.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();
	}

}
