package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.ProductGroup;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.ProductGroupDTO;
import my.com.mandrill.component.dto.model.ProductTypeDTO;
import my.com.mandrill.component.dto.request.ProductSearchRequest;
import my.com.mandrill.component.dto.response.ProductResponse;
import my.com.mandrill.component.service.ProductIntgService;
import my.com.mandrill.component.service.ProductService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.ProviderProductTypeDTO;
import my.com.mandrill.utilities.general.dto.model.ObjectDTO;
import my.com.mandrill.utilities.general.dto.request.ProviderProductTypeSearchRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductIntgServiceImpl implements ProductIntgService {

	private final ProductService productService;

	private final AccountFeignClient accountFeignClient;

	private final CommonFeignClient commonFeignClient;

	private final ValidationService validationService;

	private final ObjectMapper objectMapper;

	private static final Sort GROUP_SORTING = Sort.by(Sort.Direction.ASC, "code");

	private static final Sort TYPE_SORTING = Sort.by(Sort.Direction.ASC, "label");

	private static final Sort PROVIDER_SORTING = Sort.by(Sort.Direction.ASC, "name");

	@Override
	public ProductResponse findProductWithFlexibleFilter(ProductSearchRequest request, boolean rsmActive,
			String currentInstitutionId, String platformCode) {

		if (!CollectionUtils.isEmpty(request.getCategoryIds()))
			return filterCategory(request, rsmActive, currentInstitutionId, platformCode);

		else if (!CollectionUtils.isEmpty(request.getTypeIds()))
			return filterType(request, rsmActive, currentInstitutionId);

		else if (!CollectionUtils.isEmpty(request.getProviderIds()))
			return filterProvider(request, platformCode);

		else
			return getProductWithoutFilter(rsmActive, currentInstitutionId, platformCode);

	}

	private ProductResponse filterCategory(ProductSearchRequest request, boolean rsmActive, String currentInstitutionId,
			String platformCode) {

		List<ProductTypeDTO> typeDTOS;
		List<ObjectDTO> providerDTOS;

		List<ProductGroupDTO> groupDTOS = productService.findProductGroupByIdIn(request.getCategoryIds(),
				GROUP_SORTING);
		List<String> groupValues = groupDTOS.stream().map(ProductGroupDTO::getValue).toList();

		if (!CollectionUtils.isEmpty(request.getTypeIds())) {
			List<ProductType> types = productService.findProductTypeByIdIn(request.getTypeIds(), TYPE_SORTING);
			typeDTOS = types.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();

			List<String> typeValues = types.stream().map(ProductType::getValue).toList();

			providerDTOS = accountFeignClient.getChildInstitutionsSorted(currentInstitutionId, typeValues, groupValues,
					rsmActive);
		}
		else if (!CollectionUtils.isEmpty(request.getProviderIds())) {
			List<ProviderProductTypeDTO> providerProductTypeDTOS = commonFeignClient
					.findProviderProductTypeSearch(ProviderProductTypeSearchRequest.builder()
							.providerIds(request.getProviderIds()).productCategories(groupValues).build());

			providerDTOS = accountFeignClient.getProviders(
					providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProviderId).toList(),
					PROVIDER_SORTING);

			groupDTOS = productService.findProductGroupByValueIn(
					providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProductCategory).toList(),
					GROUP_SORTING);

			List<ProductType> types = productService.findByValueInAndProductPlatformCode(
					providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProductType).toList(), platformCode,
					TYPE_SORTING);
			typeDTOS = types.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();

		}
		else {
			List<ProductType> types = productService.findDistinctByProductGroupIdsAndProductPlatformCodeAndActive(
					request.getCategoryIds(), platformCode, true, TYPE_SORTING);
			typeDTOS = types.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();

			providerDTOS = accountFeignClient.getChildInstitutionsSorted(currentInstitutionId, null, groupValues,
					rsmActive);
		}

		return ProductResponse.builder().productCategories(groupDTOS).productTypes(typeDTOS)
				.productProviders(providerDTOS).build();

	}

	private ProductResponse filterType(ProductSearchRequest request, boolean rsmActive, String currentInstitutionId) {

		List<ProductGroupDTO> groupDTOS;
		List<ProductTypeDTO> typeDTOS;
		List<ObjectDTO> providerDTOS;

		List<ProductType> types = productService.findProductTypeByIdIn(request.getTypeIds(), TYPE_SORTING);
		typeDTOS = types.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();

		if (!CollectionUtils.isEmpty(request.getProviderIds())) {

			List<String> typeValue = types.stream().map(ProductType::getValue).toList();

			List<ProviderProductTypeDTO> providerProductTypeDTOS = commonFeignClient
					.findProviderProductTypeSearch(ProviderProductTypeSearchRequest.builder().productTypes(typeValue)
							.providerIds(request.getProviderIds()).build());

			providerDTOS = accountFeignClient.getProviders(
					providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProviderId).toList(),
					PROVIDER_SORTING);

			groupDTOS = productService.findProductGroupByValueIn(
					providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProductCategory).toList(),
					GROUP_SORTING);

		}
		else {

			List<ProductGroup> groups = types.stream().map(ProductType::getProductGroup).toList();
			groupDTOS = groups.stream().map(MapStructConverter.MAPPER::toProductGroupDTO).toList();

			providerDTOS = accountFeignClient.getChildInstitutionsSorted(currentInstitutionId,
					typeDTOS.stream().map(ProductTypeDTO::getValue).toList(), null, rsmActive);
		}

		return ProductResponse.builder().productCategories(groupDTOS).productTypes(typeDTOS)
				.productProviders(providerDTOS).build();

	}

	private ProductResponse filterProvider(ProductSearchRequest request, String platformCode) {

		List<ProductGroupDTO> groupDTOS;
		List<ProductTypeDTO> typeDTOS;
		List<ObjectDTO> providerDTOS;

		List<ProviderProductTypeDTO> providerProductTypeDTOS = commonFeignClient.findProviderProductTypeSearch(
				ProviderProductTypeSearchRequest.builder().providerIds(request.getProviderIds()).build());

		providerDTOS = accountFeignClient.getProviders(
				providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProviderId).toList(), PROVIDER_SORTING);

		groupDTOS = productService.findProductGroupByValueIn(
				providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProductCategory).toList(),
				GROUP_SORTING);

		List<ProductType> types = productService.findByValueInAndProductPlatformCode(
				providerProductTypeDTOS.stream().map(ProviderProductTypeDTO::getProductType).toList(), platformCode,
				TYPE_SORTING);
		typeDTOS = types.stream().map(MapStructConverter.MAPPER::toProductTypeDTO).toList();

		return ProductResponse.builder().productCategories(groupDTOS).productTypes(typeDTOS)
				.productProviders(providerDTOS).build();

	}

	private ProductResponse getProductWithoutFilter(boolean rsmActive, String currentInstitutionId,
			String platformCode) {

		List<ProductGroupDTO> groupDTOS = productService.findAllProductGroups(GROUP_SORTING);

		List<ProductType> types = productService.findAllProductTypeByPlatformAndGroup(platformCode, null, rsmActive,
				TYPE_SORTING);
		List<ProductTypeDTO> typeDTOS = types.stream()
				.map(productType -> objectMapper.convertValue(productType, ProductTypeDTO.class)).toList();

		List<ObjectDTO> providerDTOS = accountFeignClient.getChildInstitutionsSorted(currentInstitutionId, null, null,
				rsmActive);

		return ProductResponse.builder().productCategories(groupDTOS).productTypes(typeDTOS)
				.productProviders(providerDTOS).build();

	}

}