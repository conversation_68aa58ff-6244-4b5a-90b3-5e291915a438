package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductRepository extends JpaRepository<Product, String> {

	Page<Product> findByType(String type, Pageable pageable);

}
