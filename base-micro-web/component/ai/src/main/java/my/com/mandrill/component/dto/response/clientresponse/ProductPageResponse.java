package my.com.mandrill.component.dto.response.clientresponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductPageResponse<T> implements Serializable {

	private int page;

	@JsonProperty("per_page")
	private int perPage;

	@JsonProperty("total_count")
	private int totalCount;

	private T items;

	private String error;

	@JsonProperty("error_code")
	private Integer errorCode;

}
