package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;

@Entity
@Table(name = "product_highlights")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductHighlight extends AuditSection implements Serializable {

	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "product_id", nullable = false)
	private Product product;

	@Column(nullable = false)
	private String category;

	@Column(name = "highlight_text", nullable = false)
	private String highlightText;

}