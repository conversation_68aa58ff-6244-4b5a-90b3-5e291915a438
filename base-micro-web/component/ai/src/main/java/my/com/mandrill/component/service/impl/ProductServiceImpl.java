package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.ProductClient;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ProductTypeEnum;
import my.com.mandrill.component.domain.ProductGroup;
import my.com.mandrill.component.domain.ProductPlatform;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.AICachedResponse;
import my.com.mandrill.component.dto.model.ForYouDTO;
import my.com.mandrill.component.dto.model.ProductGroupDTO;
import my.com.mandrill.component.dto.model.ProductSimpleSelectionDTO;
import my.com.mandrill.component.dto.model.ProductSuggestionDTO;
import my.com.mandrill.component.dto.model.ProductSuggestionMatchDTO;
import my.com.mandrill.component.dto.model.clientmodel.ProductSimplePaginationDTO;
import my.com.mandrill.component.dto.request.ProductForYouRequest;
import my.com.mandrill.component.dto.request.ProductLoanLimitRequest;
import my.com.mandrill.component.dto.request.ProductSuggestionRequest;
import my.com.mandrill.component.dto.request.RetirementProductSuggestionRequest;
import my.com.mandrill.component.dto.request.SuggestionLoanLimitRequest;
import my.com.mandrill.component.dto.request.clientRequest.ProductFilterRequest;
import my.com.mandrill.component.dto.request.clientRequest.ProductSuggestionQuestionnaireRequest;
import my.com.mandrill.component.dto.response.UserDataAIResponse;
import my.com.mandrill.component.dto.response.clientresponse.ProductPageResponse;
import my.com.mandrill.component.dto.response.clientresponse.ProductSuggestionQuestionnaireResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.exception.NotSupportedException;
import my.com.mandrill.component.repository.jpa.ProductGroupRepository;
import my.com.mandrill.component.repository.jpa.ProductPlatformRepository;
import my.com.mandrill.component.repository.jpa.ProductRepository;
import my.com.mandrill.component.repository.jpa.ProductTypeRepository;
import my.com.mandrill.component.service.ProductService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.component.util.ProductPageResponseUtil;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.LoanDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.feign.dto.model.CurrentUserDataForAIDTO;
import my.com.mandrill.utilities.feign.dto.model.SurveyFormUserResultDTO;
import my.com.mandrill.utilities.feign.dto.request.LoanEligibilityLenderProductRequest;
import my.com.mandrill.utilities.feign.dto.response.JourneyConfigurationGroupResponse;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.JourneyConfigurationGroupType;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import my.com.mandrill.utilities.general.constant.SystemConfigurationEnum;
import my.com.mandrill.utilities.general.dto.response.AiProductResponse;
import my.com.mandrill.utilities.general.dto.response.UserJourneyResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

	public static final String GROUP_INVESTMENT = "investment";

	public static final String GROUP_CREDIT_CARD = "credit_card";

	public static final String GROUP_INSURANCE = "insurance";

	public static final String GROUP_LOAN = "loan";

	public static final String ISSUER_TYPE_BANK = "BANK";

	public static final String ISSUER_TYPE_INSURANCE = "INSURANCE";

	public static final String ISSUER_TYPE_BANK_INSURANCE = "BANK_INSURANCE";

	public static final String LOAN_LIMIT = "KNOW_YOUR_LOAN";

	private final ProductClient productListClient;

	private final CommonFeignClient commonFeignClient;

	private final ProxyFeignClient proxyFeignClient;

	private final ProductGroupRepository productGroupRepository;

	private final ProductPlatformRepository productPlatformRepository;

	private final ProductTypeRepository productTypeRepository;

	private final JSONUtil jsonUtil;

	private final RedisService redisService;

	private final ValidationService validationService;

	private final ProductRepository productRepository;

	@Override
	public ResponseEntity<String> findProductList(String page, String size, String type) {
		String issuerCodes = findAllIssuerCodesByType(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findProductList(page, size, type, issuerCodes);
	}

	@Override
	public ResponseEntity<String> findProductListPrivate(String page, String size, String type) {
		String issuerCodes = findAllIssuerCodesByTypePrivate(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findProductList(page, size, type, issuerCodes);
	}

	@Override
	public ResponseEntity<String> findProductFilters(String type, String recommendationType) {
		return productListClient.findProductFilterList(type, recommendationType);
	}

	@Override
	public ResponseEntity<String> findProductDetails(String type, String productName) {
		return productListClient.findProductDetailsList(type, productName);
	}

	@Override
	public ResponseEntity<String> findProductCompare(String type, String productName1, String productName2) {
		return productListClient.findProductCompare(type, productName1, productName2);
	}

	@Override
	public ResponseEntity<String> findProductCategory(String group) {
		return productListClient.findProductCategory(group);
	}

	@Override
	public ResponseEntity<String> findProduct(String type, String entityName, String cardType, String productName,
			String productId) {
		try {
			return productListClient.findProduct(type, entityName, cardType, productName, productId);
		}
		catch (Exception e) {
			log.error("AI Integration failed error: {}", e.getMessage());
			throw new BusinessException(ErrorCodeEnum.AI_INTEGRATION_FAILED_ERROR);
		}
	}

	@Override
	public ResponseEntity<String> findProductFilterLoanLimit(String type) {
		return productListClient.findProductFilterList(type, LOAN_LIMIT);
	}

	@Override
	public ResponseEntity<String> findProductRefinance(String type, ProductLoanLimitRequest body, String page,
			String size) {
		ProductSuggestionDTO request = buildLoanLimitPayload(body);

		log.debug("Refinance Loan Limit Payload: {}", request);
		return productListClient.findRefinance(type, request, page, size);
	}

	@Override
	public ResponseEntity<String> findProductSuggestionLoanLimit(String type, SuggestionLoanLimitRequest body,
			String page, String size) {
		ProductSuggestionDTO request = buildLoanLimitPayload(body);
		request.setFilters(body.getFilters());

		log.debug("Suggestion Loan Limit Payload: {}", request);
		return productListClient.findSuggestions(request, page, size, type, "", LOAN_LIMIT);
	}

	@Override
	public ResponseEntity<String> findProductSuggestion(String page, String size, String userJourneyId, String group,
			String type, ProductSuggestionRequest body) {
		String userId = SecurityUtil.currentUserId();
		ProductSuggestionDTO result = new ProductSuggestionDTO();
		result.setFilters(body.getFilters());
		if (!Strings.isBlank(userJourneyId)) {

			UserJourneyResponse userJourneyResponse = commonFeignClient
					.getNotCompletedUserJourneyByIdAndUserId(userJourneyId, userId);

			JourneyConfigurationGroupResponse journeyConfigurationGroupResponse = commonFeignClient
					.getJourneyConfigurationGroupByName(userJourneyResponse.getJourneyGroupName());

			try {
				UserDataAIResponse userData = findAllUserDataByGroupType(
						JourneyConfigurationGroupType.valueOf(journeyConfigurationGroupResponse.getType()));
				userData.setUserId(userId);
				result.setUserData(userData);
			}
			catch (IllegalArgumentException ex) {
				throw ExceptionPredicate
						.notSupportedByJourneyConfigurationGroupType(journeyConfigurationGroupResponse.getType()).get();
			}
		}
		else {
			if (!Strings.isBlank(group)) {
				UserDataAIResponse userData = new UserDataAIResponse();
				switch (group) {
					case GROUP_INVESTMENT, GROUP_CREDIT_CARD ->
						findAllUserDataByGroupType(JourneyConfigurationGroupType.BANK, userData);
					case GROUP_INSURANCE ->
						findAllUserDataByGroupType(JourneyConfigurationGroupType.INSURANCE, userData);
					case GROUP_LOAN -> findAllUserDataByGroupType(JourneyConfigurationGroupType.LOAN, userData);
					default -> log.warn("Unknown group: {}", group);
				}
				userData.setUserId(userId);
				result.setUserData(userData);
			}

		}
		String issuerCodes = findAllIssuerCodesByType(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findSuggestions(result, page, size, type, issuerCodes, null);
	}

	@Override
	public ResponseEntity<String> findProductForYou(String size, String userJourneyId) {
		ForYouDTO result = new ForYouDTO();
		String userId = SecurityUtil.currentUserId();

		UserJourneyResponse userJourneyResponse = commonFeignClient
				.getNotCompletedUserJourneyByIdAndUserId(userJourneyId, userId);

		JourneyConfigurationGroupResponse journeyConfigurationGroupResponse = commonFeignClient
				.getJourneyConfigurationGroupByName(userJourneyResponse.getJourneyGroupName());

		if (Boolean.TRUE.equals(journeyConfigurationGroupResponse.getMainFlow())) {
			result.setModule(journeyConfigurationGroupResponse.getAiMapping());
		}
		else {
			if (StringUtils.isNotBlank(userJourneyResponse.getAiMapping()))
				result.setInterests(
						Stream.of(userJourneyResponse.getAiMapping().split(",")).map(String::trim).toList());
		}
		try {
			UserDataAIResponse userData = findAllUserDataByGroupType(
					JourneyConfigurationGroupType.valueOf(journeyConfigurationGroupResponse.getType()));
			userData.setUserId(userId);
			result.setUser_data(userData);
		}
		catch (IllegalArgumentException ex) {
			throw ExceptionPredicate
					.notSupportedByJourneyConfigurationGroupType(journeyConfigurationGroupResponse.getType()).get();
		}
		String issuerCodes = findAllIssuerCodesByType(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findProductForYou(result, size, issuerCodes);
	}

	private UserDataAIResponse findAllUserDataByGroupType(JourneyConfigurationGroupType groupType,
			UserDataAIResponse response) {
		switch (groupType) {
			case BANK, CREDIT_CARD -> {
				response.setBank(proxyFeignClient.getBankFeignClient().getBank());
				return response;
			}
			case VEHICLE, VEHICLE_FINOLOGY -> {
				response.setVehicle(proxyFeignClient.getVehicleFeignClient().findAll(Sort.unsorted()));
				return response;
			}
			case PROPERTY, PROPERTY_LOAN_ELIGIBILITY -> {
				response.setProperty(proxyFeignClient.getPropertyFeignClient().getProperties());
				return response;
			}
			case INSURANCE -> {
				response.setInsurance(proxyFeignClient.getInsuranceFeignClient().findAll(Sort.unsorted()));
				return response;
			}
			case LOAN, LOAN_V2 -> {
				response.setLoan(proxyFeignClient.getBankFeignClient().findLoanAll(Sort.unsorted()));
				return response;
			}
			default -> throw ExceptionPredicate.notSupportedByJourneyConfigurationGroupType(groupType.getCode()).get();
		}
	}

	private UserDataAIResponse findAllUserDataByGroupType(JourneyConfigurationGroupType groupType) {
		UserDataAIResponse response = new UserDataAIResponse();
		return findAllUserDataByGroupType(groupType, response);
	}

	private ProductSuggestionDTO buildLoanLimitPayload(ProductLoanLimitRequest body) {
		UserDTO userDTO = proxyFeignClient.getAccountFeignClient().getAccount();

		// User Data Preparation
		UserDataAIResponse data = new UserDataAIResponse();
		data.setUserId(userDTO.getId());
		data.setUserAge(userDTO.getAge());
		data.setUserAppliedLoanAmount(body.getAppliedLoanAmount());
		data.setLoan(proxyFeignClient.getBankFeignClient().findLoanAll(Sort.unsorted()));

		// Get User's Salary
		if (userDTO.getEmploymentType() != null) {
			try {
				data.setUserIncome(proxyFeignClient.getAccountFeignClient()
						.findIncomeSalary(userDTO.getEmploymentType().getId()).getMonthlyIncomeAmount());
			}
			catch (EntityNotFoundException e) {
				data.setUserIncome(null);
			}
		}
		else {
			data.setUserIncome(null);
		}

		// Request Payload
		ProductSuggestionDTO request = new ProductSuggestionDTO();
		request.setUserData(data);

		return request;
	}

	private String findAllIssuerCodesByType(String issuerType) {
		String result = "";
		if (Boolean.parseBoolean(commonFeignClient.getSystemConfigurationByCodeAndInstitutionIdReturnDefault(
				SystemConfigurationEnum.AI_ISSUER_CODE_CONTROL.getCode(),
				SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getValue()).getValue())) {
			if (ISSUER_TYPE_BANK.equals(issuerType)) {
				result = proxyFeignClient.getBankFeignClient().getIssuerCodesByIsPartnerTrue();
			}
			else if (ISSUER_TYPE_INSURANCE.equals(issuerType)) {
				result = proxyFeignClient.getInsuranceFeignClient().getIssuerCodesByIsPartnerTrue();
			}
			else if (ISSUER_TYPE_BANK_INSURANCE.equals(issuerType)) {
				List<String> filteredList = new ArrayList<>();
				String bankIssuerCode = proxyFeignClient.getBankFeignClient().getIssuerCodesByIsPartnerTrue();
				String insuranceIssuerCode = proxyFeignClient.getInsuranceFeignClient().getIssuerCodesByIsPartnerTrue();
				if (!bankIssuerCode.isBlank()) {
					filteredList.add(bankIssuerCode);
				}
				if (!insuranceIssuerCode.isBlank()) {
					filteredList.add(insuranceIssuerCode);
				}
				result = filteredList.stream().map(Object::toString).collect(Collectors.joining(","));
			}
		}

		return result;
	}

	private String findAllIssuerCodesByTypePrivate(String issuerType) {
		String result = "";
		if (Boolean.parseBoolean(commonFeignClient.getSystemConfigurationByCodeAndInstitutionIdPrivate(
				SystemConfigurationEnum.AI_ISSUER_CODE_CONTROL.getCode(),
				SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getValue()).getValue())) {
			if (ISSUER_TYPE_BANK.equals(issuerType)) {
				result = proxyFeignClient.getBankFeignClient().getIssuerCodeByIsPartnerTruePrivate();
			}
			else if (ISSUER_TYPE_INSURANCE.equals(issuerType)) {
				result = proxyFeignClient.getInsuranceFeignClient().getIssuerCodeByIsPartnerTruePrivate();
			}
			else if (ISSUER_TYPE_BANK_INSURANCE.equals(issuerType)) {
				List<String> filteredList = new ArrayList<>();
				String bankIssuerCode = proxyFeignClient.getBankFeignClient().getIssuerCodeByIsPartnerTruePrivate();
				String insuranceIssuerCode = proxyFeignClient.getInsuranceFeignClient()
						.getIssuerCodeByIsPartnerTruePrivate();
				if (!bankIssuerCode.isBlank()) {
					filteredList.add(bankIssuerCode);
				}
				if (!insuranceIssuerCode.isBlank()) {
					filteredList.add(insuranceIssuerCode);
				}
				result = filteredList.stream().map(Object::toString).collect(Collectors.joining(","));
			}
		}

		return result;
	}

	@Override
	public List<ProductGroupDTO> findAllProductGroups(Sort sort) {
		return productGroupRepository.findActiveProductGroupsDistinct(sort);
	}

	@Override
	public List<ProductPlatform> findAllProductPlatform(Sort sort) {
		return productPlatformRepository.findAll(sort);
	}

	@Override
	public List<ProductType> findAllProductTypeByPlatformAndGroup(String productPlatformCode, String productGroupCode,
			Boolean rsmActive, Sort sort) {
		String cacheKey = CacheKey.PRODUCT_TYPE_CACHE.formatted(productPlatformCode, productGroupCode, rsmActive)
				+ sort.hashCode();
		Optional<List<ProductType>> dataInCache = redisService.getFromValue(cacheKey, new TypeReference<>() {
		});
		if (dataInCache.isPresent()) {
			log.info("product type from AI serve by cache: {}", cacheKey);
			return dataInCache.get();
		}
		validationService.validateGetAllProductTypes(productPlatformCode, productGroupCode);

		List<ProductType> productTypes;
		if (Boolean.TRUE.equals(rsmActive)) {
			// this is to get the product types which entitled for rsm commission
			List<String> rsmActiveProductTypeIds = proxyFeignClient.getMoneyXCoreFeignClient()
					.getRsmActiveProductTypes();
			productTypes = productTypeRepository.findAllByProductPlatformCodeAndActiveTrueAndIdIn(productPlatformCode,
					rsmActiveProductTypeIds, sort);
		}
		else if (StringUtils.isBlank(productGroupCode)) {
			productTypes = productTypeRepository.findAllByProductPlatformCodeAndActiveTrue(productPlatformCode, sort);
		}
		else {
			productTypes = productTypeRepository.findAllByProductPlatformCodeAndProductGroupCodeAndActiveTrue(
					productPlatformCode, productGroupCode, sort);
		}
		redisService.putToValue(cacheKey, productTypes, Duration.ofMinutes(1));
		return productTypes;
	}

	@Override
	public ResponseEntity<String> findProductSuggestion(String page, String size, String type,
			ProductSuggestionRequest body) {
		if (ProductTypeEnum.contains(type)) {
			Page<AiProductResponse> products = productRepository
					.findByType(type, PageRequest.of(Integer.parseInt(page), Integer.parseInt(size)))
					.map(MapStructConverter.MAPPER::toResponse);
			ProductPageResponse<List<AiProductResponse>> response = ProductPageResponseUtil.fromSpringPage(products);
			return ResponseEntity.ok(jsonUtil.convertToString(response));
		}
		ProductSuggestionDTO result = new ProductSuggestionDTO();
		result.setFilters(body.getFilters());
		result.setUserData(buildUserData());

		String issuerCodes = findAllIssuerCodesByType(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findSuggestions(result, page, size, type, issuerCodes, null);
	}

	@Override
	public ResponseEntity<String> findProductSuggestionPrivate(String page, String size, String type,
			ProductSuggestionRequest body) {
		ProductSuggestionDTO result = new ProductSuggestionDTO();
		result.setFilters(body.getFilters());

		String issuerCodes = findAllIssuerCodesByTypePrivate(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findSuggestions(result, page, size, type, issuerCodes, null);
	}

	@Override
	public ResponseEntity<String> findProductForYou(String size, ProductForYouRequest productForYouRequest) {
		ForYouDTO result = new ForYouDTO();
		result.setModule(productForYouRequest.getModule());
		result.setInterests(productForYouRequest.getInterests());
		result.setUser_data(buildUserData());
		result.setFilters(productForYouRequest.getFilters());

		String issuerCodes = findAllIssuerCodesByType(ISSUER_TYPE_BANK_INSURANCE);
		return productListClient.findProductForYou(result, size, issuerCodes);
	}

	@Override
	public ResponseEntity<String> findAllProductGroupsAI() {
		Optional<AICachedResponse> dataInCache = redisService.getFromValue(CacheKey.PRODUCT_GROUPS_CACHE,
				new TypeReference<>() {
				});
		if (dataInCache.isPresent()) {
			log.info("product group from AI serve by cache: {}", CacheKey.PRODUCT_GROUPS_CACHE);
			return ResponseEntity.status(dataInCache.get().getStatusCode()).contentType(MediaType.APPLICATION_JSON)
					.body(dataInCache.get().getBody());
		}

		ResponseEntity<String> response = productListClient.findProductGroups();
		AICachedResponse aiCachedResponse = new AICachedResponse(response.getBody(), response.getStatusCode().value());
		redisService.putToValue(CacheKey.PRODUCT_GROUPS_CACHE, aiCachedResponse, Duration.ofDays(1));

		return response;
	}

	@Override
	public ResponseEntity<String> findLoanEligibilityPurchaseProducts(String type,
			LoanEligibilityLenderProductRequest body, String page, String size) {
		ProductSuggestionDTO request = buildLoanEligibilityData(body);

		return productListClient.findSuggestions(request, page, size, type, "", LOAN_LIMIT);
	}

	@Override
	public ResponseEntity<String> findLoanEligibilityRefinanceProducts(String type,
			LoanEligibilityLenderProductRequest body, String page, String size) {
		ProductSuggestionDTO request = buildLoanEligibilityData(body);

		return productListClient.findRefinance(type, request, page, size);
	}

	private ProductSuggestionDTO buildLoanEligibilityData(LoanEligibilityLenderProductRequest body) {
		CurrentUserIdDTO userDTO = proxyFeignClient.getAccountFeignClient().getCurrentUserId();

		UserDataAIResponse data = new UserDataAIResponse();
		data.setUserId(userDTO.getId());
		data.setUserAge(userDTO.getAge());
		data.setUserAppliedLoanAmount(body.getAppliedLoanAmount());

		// set loan eligibility incomes
		data.setUserIncome(body.getTotalIncomes());

		// set loan eligibility commitments
		data.setLoan(Optional.ofNullable(body.getCommitments())
				.map(commitments -> commitments.stream().filter(Objects::nonNull)
						.map(commitment -> LoanDTO.builder().monthlyInstallment(commitment).build()).toList())
				.orElse(Collections.emptyList()));

		// Request Payload
		ProductSuggestionDTO request = new ProductSuggestionDTO();
		request.setUserData(data);

		return request;
	}

	private UserDataAIResponse buildUserData() {
		CurrentUserDataForAIDTO userData = proxyFeignClient.getAccountFeignClient()
				.getCurrentUserDataForAiIntegration();

		return MapStructConverter.MAPPER.toUserDataAIResponse(userData);
	}

	@Override
	public boolean isActiveProductTypeForPlatform(String code, String platformCode) {
		Optional<ProductType> productType = productTypeRepository.findFirstByValueAndProductPlatformCode(code,
				platformCode);
		if (productType.isPresent()) {
			return productType.get().getActive();
		}
		log.warn("configuration product-type not found, return false as default");
		return false;
	}

	@Override
	public ResponseEntity<String> findProductSuggestionForRetirement(RetirementProductSuggestionRequest request,
			String page, String size) {
		request.setUserId(SecurityUtil.currentUserId());
		return productListClient.findProductSuggestionForRetirement(request, page, size);
	}

	@Override
	public List<ProductSuggestionMatchDTO> findProductSuggestionMatch(SurveyFormType type) {
		SurveyFormUserResultDTO result = proxyFeignClient.getAnalyticFeignClient().checkSurveyFormResult(type);
		ProductSuggestionQuestionnaireRequest request = MapStructConverter.MAPPER
				.toProductSuggestionQuestionnaireRequest(result);
		ProductSuggestionQuestionnaireResponse response = switch (type) {
			case PRODUCT_CC -> productListClient.findProductCCSuggestionFromQuestionnaire(request);
			case PRODUCT_CASA -> productListClient.findProductCASASuggestionFromQuestionnaire(request);
			default -> throw new NotSupportedException("Support only CC and CASA");
		};
		return response.getMatches().stream().map(MapStructConverter.MAPPER::toProductSuggestionMatchDTO).toList();
	}

	@Override
	public List<ProductType> findDistinctByProductGroupIdAndProductPlatformCodeAndActive(String productGroupId,
			String productPlatformCode, Boolean active, Sort sort) {
		return productTypeRepository.findDistinctByProductGroupIdAndProductPlatformCodeAndActive(productGroupId,
				productPlatformCode, active, sort);
	}

	@Override
	public List<ProductType> findDistinctByProductGroupIdsAndProductPlatformCodeAndActive(List<String> productGroupIds,
			String productPlatformCode, Boolean active, Sort sort) {
		return productTypeRepository.findDistinctByProductGroupIdInAndProductPlatformCodeAndActive(productGroupIds,
				productPlatformCode, active, sort);
	}

	@Override
	public ProductGroup findProductGroupById(String id) {
		return productGroupRepository.findById(id).orElseThrow(ExceptionPredicate.productGroupNotFoundById(id));
	}

	@Override
	public ProductType findProductTypeById(String id) {
		return productTypeRepository.findById(id).orElseThrow(ExceptionPredicate.productTypeNotFoundById(id));
	}

	@Override
	public List<ProductType> findActiveByGroupIdAndPlatform(Collection<String> productGroupId,
			String productPlatformCode) {
		return productTypeRepository.findDistinctByProductGroupIdInAndProductPlatformCodeAndActive(productGroupId,
				productPlatformCode, true, Sort.by("label"));
	}

	@Override
	public List<ProductType> findActiveProductTypeAndPlatform(Collection<String> productType,
			String productPlatformCode) {
		return productTypeRepository.findDistinctByValueInAndProductPlatformCodeAndActive(productType,
				productPlatformCode, true, Sort.by("label"));
	}

	@Override
	public List<ProductSimpleSelectionDTO> findProductSimpleSelection(String productType, String providerId,
			Boolean rsmActive) {
		List<String> propertyTypes = List.of("property");
		if (propertyTypes.contains(productType)) {
			if (Boolean.TRUE.equals(rsmActive)) {
				Set<String> rsmActiveProductIds = proxyFeignClient.getMoneyXCoreFeignClient()
						.getRsmActiveProducts(productType);

				if (CollectionUtils.isEmpty(rsmActiveProductIds)) {
					return findPropertySimpleSelectionInstitution(providerId);
				}
				else
					return findPropertySimpleSelectionInstitution(rsmActiveProductIds);

			}
			else
				return findPropertySimpleSelectionInstitution(providerId);
		}

		ProductSuggestionDTO request = new ProductSuggestionDTO();
		boolean requestToAi = true;
		if (StringUtils.isNotBlank(providerId)) {
			Set<String> aiMapping = proxyFeignClient.getAccountFeignClient().getInstitutionAiMapping(providerId);

			if (CollectionUtils.isEmpty(aiMapping)) {
				requestToAi = false;
			}
			else {
				ProductFilterRequest request1 = ProductFilterRequest.builder()
						.bank(ProductFilterRequest.Bank.builder().bank(new ArrayList<>(aiMapping)).build()).build();
				request.setFilters(request1);
			}
		}

		List<ProductSimpleSelectionDTO> products = new ArrayList<>();

		List<String> providerIds = new ArrayList<>();
		if (StringUtils.isNotBlank(providerId)) {
			providerIds.add(providerId);
		}

		List<String> productTypes = new ArrayList<>();
		if (StringUtils.isNotBlank(productType)) {
			productTypes.add(productType);
		}

		List<ProductSimpleSelectionDTO> beResult = proxyFeignClient.getCommonFeignClient()
				.findAffiliateProduct(List.of(), providerIds, productTypes).stream()
				.map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();

		products.addAll(beResult);

		if (checkIfAffiliateProductType(beResult, productType)) {
			requestToAi = false;
		}

		if (requestToAi) {
			ProductSimplePaginationDTO result = jsonUtil.convertValueFromJson(
					productListClient.findSuggestions(request, "1", "200", productType, "", null).getBody(),
					new TypeReference<>() {
					});

			List<ProductSimpleSelectionDTO> aiResult = new ArrayList<>(
					result.getItems().stream().map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList());

			products.addAll(aiResult);
		}

		if (Boolean.TRUE.equals(rsmActive)) {
			// remove the products that are not entitle for rsm commission
			Set<String> rsmActiveProductIds = proxyFeignClient.getMoneyXCoreFeignClient().getRsmActiveProducts();
			products.removeIf(product -> !rsmActiveProductIds.contains(product.getId()));
		}

		return products;
	}

	private boolean checkIfAffiliateProductType(List<ProductSimpleSelectionDTO> affiliateProducts, String productType) {
		return affiliateProducts.stream()
				.anyMatch(affiliateProduct -> productType.equals(affiliateProduct.getProductType()));
	}

	private List<ProductSimpleSelectionDTO> findPropertySimpleSelectionInstitution(String developerId) {
		return proxyFeignClient.getPropertyFeignClient().findProjects(developerId).stream()
				.map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();
	}

	private List<ProductSimpleSelectionDTO> findPropertySimpleSelectionInstitution(Set<String> projectId) {
		return proxyFeignClient.getPropertyFeignClient().findProjectsByIds(projectId).stream()
				.map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();
	}

	@Override
	public List<ProductGroupDTO> findProductGroupByValueIn(List<String> values, Sort sort) {
		return productGroupRepository.findByValueIn(values, sort).stream()
				.map(MapStructConverter.MAPPER::toProductGroupDTO).toList();
	}

	@Override
	public List<ProductGroupDTO> findProductGroupByIdIn(List<String> ids, Sort sort) {
		return productGroupRepository.findByIdIn(ids, sort).stream().map(MapStructConverter.MAPPER::toProductGroupDTO)
				.toList();
	}

	@Override
	public List<ProductType> findProductTypeByIdIn(List<String> ids, Sort sort) {
		return productTypeRepository.findByIdIn(ids, sort);
	}

	@Override
	public List<ProductType> findByValueInAndProductPlatformCode(List<String> values, String platformCode, Sort sort) {
		return productTypeRepository.findByValueInAndProductPlatformCode(values, platformCode, sort);
	}

}