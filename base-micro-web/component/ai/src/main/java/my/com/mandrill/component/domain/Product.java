package my.com.mandrill.component.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "products")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Product extends AuditSection implements Serializable {

	@Column(nullable = false)
	private String name;

	@Column(nullable = false)
	private String type;

	private String description;

	@Column(name = "issuer_code")
	private String issuerCode;

	@Column(name = "issuer_type")
	private String issuerType;

	private String entity;

	@Column(name = "card_type")
	private String cardType;

	private String logo;

	@Column(name = "product_link")
	private String productLink;

	@Column(name = "apply_link")
	private String applyLink;

	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	@OneToMany(mappedBy = "product", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
	private List<ProductHighlight> highlights = new ArrayList<>();

}
