package my.com.mandrill.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ProductTypeEnum {

	WILL("will"), WASIAT("wasiat"), TRADING("trading"), CREDIT_PROFILE("credit-profile"),
	RENEWABLE_ENERGY("renewable-energy");

	private final String value;

	public static boolean contains(String type) {
		if (type == null)
			return false;
		return Arrays.stream(ProductTypeEnum.values()).anyMatch(e -> e.getValue().equalsIgnoreCase(type));
	}

}
