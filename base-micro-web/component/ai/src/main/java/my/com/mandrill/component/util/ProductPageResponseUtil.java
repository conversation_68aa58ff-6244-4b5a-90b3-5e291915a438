package my.com.mandrill.component.util;

import my.com.mandrill.component.dto.response.clientresponse.ProductPageResponse;
import org.springframework.data.domain.Page;

import java.util.List;

public class ProductPageResponseUtil {

	public static <T> ProductPageResponse<List<T>> fromSpringPage(Page<T> springPage) {
		ProductPageResponse<List<T>> response = new ProductPageResponse<>();

		response.setPage(springPage.getNumber() + 1);

		response.setPerPage(springPage.getSize());
		response.setTotalCount((int) springPage.getTotalElements());
		response.setItems(springPage.getContent());

		response.setError(null);
		response.setErrorCode(null);

		return response;
	}

	public static <T> ProductPageResponse<T> error(String errorMessage, int errorCode) {
		ProductPageResponse<T> response = new ProductPageResponse<>();
		response.setError(errorMessage);
		response.setErrorCode(errorCode);
		return response;
	}

}