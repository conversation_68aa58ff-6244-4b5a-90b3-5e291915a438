package my.com.mandrill.component.integration.user;

import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.ReferralCodeRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class PublicReferralIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ReferralCodeRepository referralCodeRepository;

	@AfterEach
	public void tearDown() {
		referralCodeRepository.deleteAll();
	}

	@Test
	void checkReferralCodeExistsUniqueSuccess() throws Exception {
		mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "QNu8k")
				.header("hash", hashKeyGenerator.getHash()).header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString())).andDo(print())
				.andExpect(status().isOk()).andExpect(MockMvcResultMatchers.jsonPath("$").value(true));
	}

	@Test
	void checkReferralCodeExistsMobilePhoneSuccess() throws Exception {
		mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "+60123456789")
				.header("hash", hashKeyGenerator.getHash()).header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString())).andDo(print())
				.andExpect(status().isOk()).andExpect(MockMvcResultMatchers.jsonPath("$").value(true));
	}

	@Test
	void checkReferralCodeExists_unauthorized_error() throws Exception {
		mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "QNu8k")).andDo(print())
				.andExpect(status().isUnauthorized());
	}

	@Test
	void checkReferralCodeExists_emptyParam_error() throws Exception {
		mockMvc.perform(get("/v1/public/referral/code-exists").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString())).andDo(print())
				.andExpect(status().isBadRequest());
	}

}
