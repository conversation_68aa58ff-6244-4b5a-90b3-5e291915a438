package my.com.mandrill.component.integration.user;

import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.ReferralCodeRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

public class PublicReferralCodeIT extends BaseIntegrationTest {

	private static final String REF_CODE_ID = "01JN2XG43X466VSPHN7MSC18Q1";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ReferralCodeRepository referralCodeRepository;

	@AfterEach
	public void tearDown() {
		referralCodeRepository.findById(REF_CODE_ID).ifPresent(referralCodeRepository::delete);
	}

	@Test
	void checkReferralCode_code_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "lqEso")
				.header("hash", hashKeyGenerator.getHash()).header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertTrue(Boolean.parseBoolean(mvcResult.getResponse().getContentAsString()));
	}

	@Test
	void checkReferralCode_codeInvalid_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "invalid")
				.header("hash", hashKeyGenerator.getHash()).header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertFalse(Boolean.parseBoolean(mvcResult.getResponse().getContentAsString()));
	}

	@Test
	void checkReferralCode_phoneNumber_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "+601122334456")
				.header("hash", hashKeyGenerator.getHash()).header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertTrue(Boolean.parseBoolean(mvcResult.getResponse().getContentAsString()));
	}

	@Test
	void checkReferralCode_phoneNumberNotRegistered_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "+601122334477")
				.header("hash", hashKeyGenerator.getHash()).header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertFalse(Boolean.parseBoolean(mvcResult.getResponse().getContentAsString()));
	}

	@Test
	void checkReferralCode_unauthorized_error() throws Exception {
		mockMvc.perform(get("/v1/public/referral/code-exists").queryParam("code", "lqEso"))
				.andExpect(MockMvcResultMatchers.status().isUnauthorized());
	}

	@Test
	void checkReferralCode_emptyParam_error() throws Exception {
		mockMvc.perform(get("/v1/public/referral/code-exists").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest());
	}

}
