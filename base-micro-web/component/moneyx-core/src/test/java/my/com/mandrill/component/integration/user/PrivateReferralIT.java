package my.com.mandrill.component.integration.user;

import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.PointWithdrawalRepository;
import my.com.mandrill.component.repository.ReferralCodeRepository;
import my.com.mandrill.component.repository.ReferreRepository;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class PrivateReferralIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ReferreRepository referreRepository;

	@Autowired
	private ReferralCodeRepository referralCodeRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@AfterEach
	public void tearDown() {
		pointTransactionRepository.deleteAll();
		pointWithdrawalRepository.deleteAll();
		referreRepository.deleteAll();
		referralCodeRepository.deleteAll();
	}

	@Test
	void findRelationTypeSuccess() throws Exception {
		mockMvc.perform(post("/v1/private/referral/find-relation-type").header(RequestUtil.INTERNAL_API_KEY, "123")
				.contentType(MediaType.APPLICATION_JSON).content(getRequest())).andDo(print())
				.andExpect(status().isOk())
				.andExpect(MockMvcResultMatchers.jsonPath("$").value(RSMRelationType.C2C.name()));
	}

	@Test
	void getReferralCodeOfReferrerByReferredIdSuccess() throws Exception {
		String expected = getExpectationString();
		mockMvc.perform(get("/v1/private/referral/referred-user-id/62af49e8-42cc-4786-b37a-3e1e68c98e9d")
				.header(RequestUtil.INTERNAL_API_KEY, "123")).andDo(print()).andExpect(status().isOk())
				.andExpect(MockMvcResultMatchers.content().json(expected, false));
	}

}
