package my.com.mandrill.component.integration.user;

import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.PointWithdrawalRepository;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class PrivateWithdrawalIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@AfterEach
	public void tearDown() {
		pointWithdrawalRepository.deleteAll();
		pointTransactionRepository.deleteAll();
	}

	@Test
	void testHasPendingWithdrawalTrue() throws Exception {
		mockMvc.perform(get("/v1/private/withdrawal/has-pending/62af49e8-42cc-4786-b37a-3e1e68c98e9d")
				.header(RequestUtil.INTERNAL_API_KEY, "123")).andDo(print()).andExpect(status().isOk())
				.andExpect(MockMvcResultMatchers.jsonPath("$").value(true));
	}

	@Test
	void testHasPendingWithdrawalFalse() throws Exception {
		mockMvc.perform(get("/v1/private/withdrawal/has-pending/dea534ca-8c34-4cbb-801f-57b6711c2789")
				.header(RequestUtil.INTERNAL_API_KEY, "123")).andDo(print()).andExpect(status().isOk())
				.andExpect(MockMvcResultMatchers.jsonPath("$").value(false));
	}

}
