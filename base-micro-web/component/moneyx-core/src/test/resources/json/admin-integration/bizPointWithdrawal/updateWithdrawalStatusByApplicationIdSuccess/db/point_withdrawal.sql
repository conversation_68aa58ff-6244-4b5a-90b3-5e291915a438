-- Dummy data for point_withdrawal table for updateWithdrawalStatusByApplicationIdSuccess integration test
INSERT INTO moneyx_core.point_withdrawal (
    id, created_date, created_by, last_modified_date, last_modified_by, user_id, user_ref_no, full_name, email, bank_name, account_holder_name, bank_account_no, payment_info_status, requested_amount, admin_fee, receivable_amount, status, failure_reason, cash_disbursement_date, source, ref_no, transaction_id, application_id
) VALUES
      ('1', NOW(), 'test-admin', NOW(), 'test-admin', 'user-1', 'U0001', 'Test User 1', '<EMAIL>', 'Test Bank', 'Test User 1', '**********', 'APPROVED', 100.00, 0.00, 100.00, 'PENDING', NULL, NULL, 'MXAPP', 'PDAPP0000000043', 'TXN0001', 'APPID0001'),
      ('2', NOW(), 'test-admin', NOW(), 'test-admin', 'user-2', 'U0002', 'Test User 2', '<EMAIL>', 'Test Bank', 'Test User 2', '**********', 'APPROVED', 200.00, 0.00, 200.00, 'PENDING', NULL, NULL, 'MXAPP', 'PDAPP0000000042', 'TXN0002', 'APPID0002');
