{"content": [{"refNo": "PDAPP0000000043", "applicationId": "PDAPP0000000043", "userRefNo": "**************", "fullName": "fahmi user two", "email": "<EMAIL>", "bankName": "AEON Bank", "accountHolderName": "fahmi user two", "bankAccountNo": "************", "paymentInfoStatus": "Approved", "requestedAmount": 5000, "adminFee": 0, "receivableAmount": 5000, "status": "Disbursed", "failureReason": "-", "cashDisbursementDate": "2025-03-19T03:17:43.328305Z", "createdDate": "2025-03-19T03:16:58.894410Z", "lastModifiedBy": "-", "lastModifiedDate": "2025-03-19T03:17:43.332063Z"}, {"refNo": "PDAPP0000000042", "applicationId": "PDAPP0000000042", "userRefNo": "**************", "fullName": "fahmi user two", "email": "<EMAIL>", "bankName": "AEON Bank", "accountHolderName": "fahmi user two", "bankAccountNo": "************", "paymentInfoStatus": "Approved", "requestedAmount": 5000, "adminFee": 0, "receivableAmount": 5000, "status": "Failed", "failureReason": "Testing reject scenario", "cashDisbursementDate": null, "createdDate": "2025-03-19T03:07:54.747426Z", "lastModifiedBy": "-", "lastModifiedDate": "2025-03-19T03:12:54.516562Z"}]}