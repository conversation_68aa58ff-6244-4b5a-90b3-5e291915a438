-- Dummy data for point_withdrawal table for bulkUpdateWithdrawalStatusByApplicationIdSuccess integration test
INSERT INTO point_withdrawal (
    id, created_date, created_by, last_modified_date, last_modified_by, user_id, user_ref_no, full_name, email, bank_name, account_holder_name, bank_account_no, payment_info_status, requested_amount, admin_fee, receivable_amount, status, failure_reason, cash_disbursement_date, source, ref_no, transaction_id, application_id
) VALUES
    ('3', NOW(), 'test-admin', NOW(), 'test-admin', 'user-3', 'U0003', 'Bulk User 1', '<EMAIL>', 'Bulk Bank', 'Bulk User 1', '**********', 'APPROVED', 300.00, 0.00, 300.00, 'PENDING', NULL, NULL, 'MXAPP', 'PDAPP0000000043', 'TXN0003', 'APPID0003'),
    ('4', NOW(), 'test-admin', NOW(), 'test-admin', 'user-4', 'U0004', 'Bulk User 2', '<EMAIL>', 'Bulk Bank', 'Bulk User 2', '**********', 'APPROVED', 400.00, 0.00, 400.00, 'PENDING', NULL, NULL, 'MXAPP', 'PDAPP0000000042', 'TXN0004', 'APPID0004');
