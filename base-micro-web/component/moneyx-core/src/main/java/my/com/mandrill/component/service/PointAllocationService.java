package my.com.mandrill.component.service;

import feign.Param;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.dto.projection.PointEarningAllocationProjection;

import java.util.List;

public interface PointAllocationService {

	List<PointEarningAllocationProjection> findAvailablePointEarnings(@Param("userId") String userId);

	void saveAll(List<PointAllocation> pointAllocations);

}
