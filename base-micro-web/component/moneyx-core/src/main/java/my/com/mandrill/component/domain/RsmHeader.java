package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;
import my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@Builder
@Table(name = "rsm_header")
@AllArgsConstructor
@NoArgsConstructor
public class RsmHeader extends AuditSectionTimeSeries {

	@NotNull
	@Builder.Default
	@Column(name = "is_active", nullable = false)
	private Boolean isActive = true;

	@NotBlank
	@Column(name = "commission_id")
	private String commissionId;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "commission_type")
	private RSMCommissionType commissionType;

	@Column(name = "product_category_id")
	private String productCategoryId;

	@Column(name = "product_category_name")
	private String productCategoryName;

	@Column(name = "product_subcategory_id")
	private String productSubcategoryId;

	@Column(name = "product_subcategory_name")
	private String productSubcategoryName;

	@Column(name = "product_subcategory_breakdown_id")
	private String productSubcategoryBreakdownId;

	@Column(name = "product_subcategory_breakdown_name")
	private String productSubcategoryBreakdownName;

	@Column(name = "product_type_id")
	private String productTypeId;

	@Column(name = "product_type_name")
	private String productTypeName;

	@Column(name = "product_provider_id")
	private String productProviderId;

	@Column(name = "product_provider_name")
	private String productProviderName;

	@Column(name = "product_id")
	private String productId;

	@Column(name = "product_name")
	private String productName;

	@NotNull
	@Column(name = "revenue")
	private BigDecimal revenue;

	@NotBlank
	@Column(name = "reward_name")
	private String rewardName;

	@Column(name = "remarks")
	private String remarks;

	@Enumerated(EnumType.STRING)
	@Column(name = "source")
	private SourceSystemEnum source;

	@Enumerated(EnumType.STRING)
	@Column(name = "frequency")
	private RSMFrequencyEnum frequency;

	@Enumerated(EnumType.STRING)
	@Column(name = "status")
	private RSMHeaderStatusEnum status;

	@Column(name = "start_date")
	private Instant startDate;

	@Column(name = "end_date")
	private Instant endDate;

	@Column(name = "external_revenue")
	private BigDecimal externalRevenue;

	@Column(name = "external_reward_name")
	private String externalRewardName;

	@Column(name = "external_start_date")
	private Instant externalStartDate;

	@Column(name = "external_end_date")
	private Instant externalEndDate;

	@Builder.Default
	@OneToMany(mappedBy = "header")
	private Set<RsmDetail> details = new HashSet<>();

}
