package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;
import my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RsmHeaderResponse {

	private String id;

	private String commissionId;

	private String rewardName;

	private RSMCommissionType commissionType;

	private Boolean isActive;

	private LocalDate createdDate;

	private RsmHeaderProductDTO product;

	private String remarks;

	private RSMFrequencyEnum frequency;

	private RSMHeaderStatusEnum status;

	private BigDecimal externalRevenue;

	private String externalRewardName;

	private LocalDate startDate;

	private LocalDate endDate;

	private LocalDate externalStartDate;

	private LocalDate externalEndDate;

}
