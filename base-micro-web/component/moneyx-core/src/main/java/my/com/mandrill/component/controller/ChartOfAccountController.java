package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.ChartOfAccount;
import my.com.mandrill.component.dto.model.ChartOfAccountData;
import my.com.mandrill.component.service.ChartOfAccountService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@PublicAuth
@RestController
@SecurityRequirements
@RequiredArgsConstructor
@RequestMapping("/coa")
public class ChartOfAccountController {

	private final ChartOfAccountService chartOfAccountService;

	@GetMapping
	public List<ChartOfAccountData> findAll() {
		List<ChartOfAccount> accounts = chartOfAccountService.findAll();
		return accounts.stream().map(MapStructConverter.MAPPER::toChartOfAccountData).toList();
	}

}
