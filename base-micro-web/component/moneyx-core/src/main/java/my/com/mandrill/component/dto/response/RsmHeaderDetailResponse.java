package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.dto.model.RsmHeaderDetailDTO;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;
import my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RsmHeaderDetailResponse {

	private String rewardName;

	private RSMCommissionType commissionType;

	private Boolean isActive;

	private RsmHeaderProductDTO product;

	private Map<RSMFocalType, RsmHeaderDetailDTO> detail;

	private String commissionId;

	private String id;

	private String remarks;

	private RSMFrequencyEnum frequency;

	private RSMHeaderStatusEnum status;

	private BigDecimal externalRevenue;

	private String externalRewardName;

	private LocalDate startDate;

	private LocalDate endDate;

	private LocalDate externalStartDate;

	private LocalDate externalEndDate;

}