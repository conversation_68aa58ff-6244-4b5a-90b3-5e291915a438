package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.BizAccountClient;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ChartOfAccountType;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.RSMCalculationUtil;
import my.com.mandrill.utilities.feign.dto.ProductTypeDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.*;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.CurrencyFormatterUtil;
import my.com.mandrill.utilities.general.util.DateConvertUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.util.Pair;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class LeadIntegrationServiceImpl implements LeadIntegrationService {

	private static final String REFERRAL_EARNINGS_POINT_NAME = "Referral Earnings";

	private static final String APPLICATION_EARNINGS_POINT_NAME = "Application Earnings";

	private final RsmCaptureService rsmCaptureService;

	private final PointEarningService pointEarningService;

	private final RsmDetailService rsmDetailService;

	private final RsmHeaderService rsmHeaderService;

	private final ProxyFeignClient proxyFeignClient;

	private final ReferreService referreService;

	private final BizAccountClient bizAccountClient;

	private final RunningNumberUtil runningNumberUtil;

	private final ChartOfAccountService chartOfAccountService;

	private final PointEarningIntgService pointEarningIntgService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	private final CurrencyFormatterUtil currencyFormatterUtil;

	private final PointTransactionIntgService pointTransactionIntgService;

	private final ReferralIntegrationService referralIntegrationService;

	private final FeatureFlagOutbound featureFlagOutbound;

	@Override
	public Page<UserInterestRecordRSMPaginationDTO> getLeads(Pageable pageable, RSMLeadRequest request) {
		return switch (request.getApplicationType()) {
			case AFFILIATE -> getLeadsAffiliate(pageable, request);
			case LEAD_GEN, REDIRECT -> getLeadsLeadGen(pageable, request);
			case PROPERTY -> getLeadsProperty(pageable, request);
			default -> throw new UnsupportedOperationException("application type not supported");
		};
	}

	private Page<UserInterestRecordRSMPaginationDTO> getLeadsAffiliate(Pageable pageable, RSMLeadRequest request) {
		if (!CollectionUtils.isEmpty(request.getProductCategory())) {
			List<String> productTypes = findProductTypeByCategories(request.getProductCategory());
			if (CollectionUtils.isEmpty(productTypes)) {
				return Page.empty();
			}
			request.setProductTypes(productTypes);
		}
		request.setEntityNames(EntityName.AFFILIATE_LINKS_REPORT_ENTITY_NAME);
		return proxyFeignClient.getAnalyticFeignClient().findRsmLead(pageable.getPageSize(), pageable.getPageNumber(),
				request);
	}

	private List<String> findProductTypeByCategories(List<String> categoriesId) {
		return proxyFeignClient.getAiFeignClient().findProductType(categoriesId, Set.of(), "ADMIN").stream()
				.map(ProductTypeDTO::getValue).toList();
	}

	private Page<UserInterestRecordRSMPaginationDTO> getLeadsLeadGen(Pageable pageable, RSMLeadRequest request) {
		if (!CollectionUtils.isEmpty(request.getProductCategory())) {
			List<String> productTypes = findProductTypeByCategories(request.getProductCategory());
			if (CollectionUtils.isEmpty(productTypes)) {
				return Page.empty();
			}
			request.setProductTypes(productTypes);
		}
		return proxyFeignClient.getBankFeignClient().findRsmLead(pageable.getPageSize(), pageable.getPageNumber(),
				request);
	}

	private Page<UserInterestRecordRSMPaginationDTO> getLeadsProperty(Pageable pageable, RSMLeadRequest request) {
		return proxyFeignClient.getPropertyFeignClient().findRsmLead(pageable.getPageSize(), pageable.getPageNumber(),
				request);
	}

	@Override
	public List<BizRSMCommissionListingResponse> getCommissions(String commissionId, String productId,
			String productProviderId) {

		return rsmHeaderService.findCommissions(commissionId, productId, productProviderId);
	}

	@Override
	public LeadDetailResponse getLeadDetail(ApplicationType applicationType, String applicationId) {
		LeadDetailResponse detailResponse = new LeadDetailResponse();

		List<LeadCommissionResponse> commissions = getCommission(applicationId);

		// get the full name for mapping createdBy and disbursedBy
		Map<String, UserFullNameResponse> refNoUserFullNameResponseMap = fetchUserNames(
				commissions.stream().flatMap(commission -> commission.getEarnings().stream())
						.flatMap(earning -> Stream.of(earning.getCreatedBy(), earning.getDisbursedBy()))
						.filter(Objects::nonNull).distinct().toList());

		// map createdBy and disbursedBy after fetching commissions
		List<LeadCommissionResponse> mappedCommissions = mapUserNamesToEarnings(commissions,
				refNoUserFullNameResponseMap);

		detailResponse.setCommissions(mappedCommissions);
		detailResponse.setDetail(MapStructConverter.MAPPER.toLeadDetailData(getDetail(applicationType, applicationId)));
		return detailResponse;
	}

	@Override
	public List<LeadCommissionResponse> getAttachedCommissions(String applicationId) {
		return getCommission(applicationId);
	}

	@Override
	public Map<String, LeadCommissionApplicationResponse> getLeadCommissionsInApplication(Set<String> applicationIds) {

		Map<String, List<RsmCapture>> capturesMap = rsmCaptureService.findByApplicationIdIn(applicationIds).stream()
				.collect(Collectors.groupingBy(RsmCapture::getApplicationId));

		List<String> headerIds = capturesMap.values().stream().flatMap(List::stream)
				.map(rsm -> rsm.getRsmHeader().getId()).toList();

		Map<String, List<PointEarning>> earningsMap = pointEarningService.findByApplicationIdIn(applicationIds).stream()
				.collect(Collectors.groupingBy(v -> v.getApplicationId() + v.getRsmHeader().getId()));

		Map<String, List<RsmDetail>> rsmDetailMap = rsmDetailService.findByHeader(headerIds).stream()
				.collect(Collectors.groupingBy(v -> v.getHeader().getId()));

		List<LeadCommissionApplicationResponse> response = new ArrayList<>();

		for (String applicationId : applicationIds) {

			LeadCommissionApplicationResponse lead = new LeadCommissionApplicationResponse();
			lead.setApplicationId(applicationId);

			List<RsmCapture> captures = capturesMap.getOrDefault(applicationId, List.of());

			List<LeadCommissionResponse> commissions = new ArrayList<>();

			for (RsmCapture capture : captures) {

				LeadCommissionResponse commission = MapStructConverter.MAPPER
						.toLeadCommissionResponse(capture.getRsmHeader());

				List<PointEarning> allEarnings = earningsMap
						.getOrDefault(applicationId + capture.getRsmHeader().getId(), List.of());

				commission.setHeaderId(capture.getRsmHeader().getId());
				commission.setRevenue(capture.getRevenue());

				if (!allEarnings.isEmpty())
					commission.setEarnings(
							allEarnings.stream().map(MapStructConverter.MAPPER::toLeadPointEarningData).toList());

				if (rsmDetailMap.containsKey(commission.getHeaderId())) {
					commission.setDetails(rsmDetailMap.get(commission.getHeaderId()).stream()
							.map(MapStructConverter.MAPPER::toLeadCommissionDetailData).toList());
				}
				commissions.add(commission);
			}

			if (!commissions.isEmpty()) {
				lead.setCommissions(commissions);
			}
			response.add(lead);
		}

		return response.stream().collect(Collectors.toMap(LeadCommissionApplicationResponse::getApplicationId, p -> p));
	}

	private UserInterestRecordRSMViewDTO getDetail(ApplicationType applicationType, String applicationId) {
		return switch (applicationType) {
			case LEAD_GEN, REDIRECT -> proxyFeignClient.getBankFeignClient().findUserInterestedDetailId(applicationId);
			case AFFILIATE -> proxyFeignClient.getAnalyticFeignClient().findUserInterestedDetailId(applicationId);
			case LEAD_GENERATION_FORM -> null;// this from Biz so no detail need to fetch
												// from app db
			case PROPERTY -> proxyFeignClient.getPropertyFeignClient().findRsmLeadDetail(applicationId);
		};
	}

	private UserInterestRecordRSMViewDTO getDetailFromBiz(LeadManagementBizDTO bizDto) {
		UserInterestRecordRSMViewDTO dto = new UserInterestRecordRSMViewDTO();

		dto.setId(bizDto.getId());
		dto.setUserId(bizDto.getUserId());
		dto.setFullName(bizDto.getFullName());
		dto.setRsmRelation(bizDto.getRsmScenario());
		dto.setCreatedDate(DateConvertUtil.toInstant(bizDto.getApplyDate(), DateConvertUtil.DATE_FORMAT_11, false));
		dto.setUserRefNo(bizDto.getUserRefNo());
		dto.setCompanyId(bizDto.getCompanyRefId());
		dto.setCompanyName(bizDto.getCompanyName());
		dto.setReferralCode(bizDto.getReferralCode());
		return dto;
	}

	private List<LeadCommissionResponse> getCommission(String applicationId) {
		List<RsmCapture> captures = rsmCaptureService.findByApplicationId(applicationId);
		List<String> headerIds = captures.stream().map(v -> v.getRsmHeader().getId()).toList();

		Map<String, List<PointEarning>> earningsMap = pointEarningService.findByApplicationId(applicationId).stream()
				.collect(Collectors.groupingBy(v -> v.getRsmHeader().getId()));
		Map<String, List<RsmDetail>> rsmDetailMap = rsmDetailService.findByHeader(headerIds).stream()
				.collect(Collectors.groupingBy(v -> v.getHeader().getId()));

		return captures.stream().map(capture -> {
			String headerId = capture.getRsmHeader().getId();

			LeadCommissionResponse commission = MapStructConverter.MAPPER
					.toLeadCommissionResponse(capture.getRsmHeader());
			if (rsmDetailMap.containsKey(headerId)) {
				commission.setDetails(rsmDetailMap.get(headerId).stream()
						.map(MapStructConverter.MAPPER::toLeadCommissionDetailData).toList());
			}
			List<PointEarning> earnings = earningsMap.getOrDefault(headerId, List.of());
			commission.setHeaderId(headerId);
			commission.setRevenue(capture.getRevenue());
			commission.setEarnings(earnings.stream().map(MapStructConverter.MAPPER::toLeadPointEarningData).toList());
			commission.setConfirmed(capture.isConfirmed());
			return commission;
		}).toList();
	}

	private Map<String, UserFullNameResponse> fetchUserNames(List<String> lastModifiedByRefNos) {
		if (lastModifiedByRefNos.isEmpty()) {
			return Collections.emptyMap();
		}

		List<UserFullNameResponse> userFullNameResponseList = proxyFeignClient.getAccountFeignClient()
				.getUserFullNameByUserRefNo(new UserRefNoListRequest(lastModifiedByRefNos));

		return userFullNameResponseList.stream().collect(Collectors.toMap(UserFullNameResponse::getRefNo,
				Function.identity(), (existingUser, newUser) -> existingUser));
	}

	private List<LeadCommissionResponse> mapUserNamesToEarnings(List<LeadCommissionResponse> commissions,
			Map<String, UserFullNameResponse> refNoUserFullNameResponseMap) {

		return commissions.stream().map(commission -> {
			List<LeadPointEarningData> updatedEarnings = commission.getEarnings().stream().map(earning -> {

				earning.setCreatedBy(Optional.ofNullable(earning.getCreatedBy()).map(refNoUserFullNameResponseMap::get)
						.map(UserFullNameResponse::getFullName).orElse(null));

				earning.setDisbursedBy(Optional.ofNullable(earning.getDisbursedBy())
						.map(refNoUserFullNameResponseMap::get).map(UserFullNameResponse::getFullName).orElse(null));

				return earning;
			}).toList();

			commission.setEarnings(updatedEarnings);
			return commission;
		}).toList();
	}

	@Override
	public void attachCommission(AttachCommissionRequest request) {
		UserInterestRecordRSMViewDTO leadDetail = getDetail(request.getApplicationType(), request.getApplicationId());
		attachCommission(request, SourceSystemEnum.MXAPP, leadDetail);
	}

	@Override
	public void attachCommissionBiz(AttachCommissionBizRequest bizDto) {
		UserInterestRecordRSMViewDTO leadDetail = getDetailFromBiz(bizDto.getLeadDto());
		attachCommission(bizDto.getAttachCommissionRequest(), SourceSystemEnum.MXBIZ, leadDetail);
	}

	private void attachCommission(AttachCommissionRequest request, SourceSystemEnum source,
			UserInterestRecordRSMViewDTO leadDetail) {
		if (rsmCaptureService.isRecordExist(request.getApplicationId(), request.getRsmHeaderId())) {
			throw new BusinessException(ErrorCodeEnum.DUPLICATE_ATTACHED_RSM);
		}

		RsmHeader rsmHeader = rsmHeaderService.findById(request.getRsmHeaderId());
		Map<RSMFocalType, RsmDetail> rsmDetailMap = rsmDetailService.findByHeader(List.of(rsmHeader.getId())).stream()
				.collect(Collectors.toMap(RsmDetail::getFocalType, v -> v));

		Map<RSMFocalType, UserShortInformationDTO> focalTypeUser = getFocalTypeUser(leadDetail);

		RsmCapture rsmCapture = new RsmCapture();
		rsmCapture.setRsmHeader(rsmHeader);
		rsmCapture.setRevenue(Optional.ofNullable(request.getRevenue()).orElse(rsmHeader.getRevenue()));
		rsmCapture.setApplicationId(request.getApplicationId());
		rsmCaptureService.save(rsmCapture);

		List<PointEarning> earnings = focalTypeUser.entrySet().stream().map(focal -> {
			RSMFocalType focalType = focal.getKey();
			UserShortInformationDTO user = focal.getValue();

			RsmDetail rsmDetail = rsmDetailMap.get(focalType);
			RSMRelationType rsmRelationType = Optional.ofNullable(leadDetail.getRsmRelation())
					.orElse(RSMRelationType.NON_REFERRAL);
			SourceSystemEnum pointEarningSource = source;

			Pair<BigDecimal, BigDecimal> point = RSMCalculationUtil.calculatePoint(rsmHeader.getCommissionType(),
					rsmDetail, rsmCapture.getRevenue(), rsmRelationType);

			PointEarning earningReferee = new PointEarning();
			if (Objects.nonNull(focal.getValue())) {
				earningReferee.setUserId(user.getId());
				earningReferee.setUserRefNo(user.getRefNo());
				earningReferee.setFullName(user.getFullName());
				earningReferee.setCompanyName(user.getCompanyName());
				earningReferee.setCompanyId(user.getCompanyId());
				pointEarningSource = Optional.ofNullable(user.getSource()).orElse(source);
			}
			String runningNumberModule = SourceSystemEnum.MXBIZ.equals(pointEarningSource)
					? RunningNumberModule.RSM_BIZ_POINT_EARNING_ID.name()
					: RunningNumberModule.RSM_POINT_EARNING_ID.name();
			String refNo = runningNumberUtil.getLatestRunningNumber(runningNumberModule);
			earningReferee.setRsmHeader(rsmHeader);
			earningReferee.setRsmDetail(rsmDetail);
			earningReferee.setPointAmount(point.getFirst());
			earningReferee.setPointPercentage(point.getSecond());
			earningReferee.setRsmDetailFocalType(focalType);
			earningReferee.setRefNo(refNo);
			earningReferee.setApplicationId(leadDetail.getId());
			earningReferee.setApplicationRefNo(leadDetail.getRefNo());
			earningReferee.setApplicationType(request.getApplicationType());
			earningReferee.setStatus(PointEarningStatus.PENDING);
			earningReferee.setSource(pointEarningSource);
			if (RSMFocalType.REFEREE.equals(focalType)) {
				String pointName = rsmHeader.getProductProviderName() + ": " + rsmHeader.getProductName() + " "
						+ APPLICATION_EARNINGS_POINT_NAME + " - " + rsmHeader.getRewardName();
				earningReferee.setPointName(pointName);
				earningReferee.setReferralCode(Optional.ofNullable(user).map(this::getReferralCodeUsed).orElse(null));
			}
			else {
				earningReferee.setPointName(REFERRAL_EARNINGS_POINT_NAME);
			}
			earningReferee.setApplicationDate(leadDetail.getCreatedDate());
			earningReferee.setRsmScenario(rsmRelationType);
			return earningReferee;
		}).toList();

		pointEarningService.saveAll(earnings);
	}

	private String getReferralCodeUsed(UserShortInformationDTO user) {
		ReferralCodeResponse referralCodeResponse = referralIntegrationService
				.getReferralCodeOfReferrerByReferredId(user.getId());
		return Optional.ofNullable(referralCodeResponse).map(ReferralCodeResponse::getCode).orElse(null);
	}

	private Map<RSMFocalType, UserShortInformationDTO> getFocalTypeUser(UserInterestRecordRSMViewDTO leadDetail) {
		Map<RSMFocalType, UserShortInformationDTO> focalType = new EnumMap<>(RSMFocalType.class);
		focalType.put(RSMFocalType.MONEYX, null);

		if (RSMRelationType.NON_REFERRAL.equals(leadDetail.getRsmRelation())) {
			UserShortInformationDTO userInformation = MapStructConverter.MAPPER.toUserShortInformationDTO(leadDetail);
			focalType.put(RSMFocalType.REFEREE, userInformation);
		}
		else {
			Referre referre = getReferre(leadDetail);
			if (Objects.isNull(referre)) {
				throw new BusinessException(ErrorCodeEnum.REFERRER_RELATION_NOT_EXISTS);
			}

			Map<String, UserShortInformationDTO> refererMap = findReferrer(referre);
			if (!refererMap.containsKey(leadDetail.getUserId())) {
				// scenario when user deleted account, we'll use from lead information
				UserShortInformationDTO userInformation = MapStructConverter.MAPPER
						.toUserShortInformationDTO(leadDetail);
				refererMap.put(leadDetail.getUserId(), userInformation);
			}

			int friendTreeLimit = featureFlagOutbound
					.isFeatureEnabled(GlobalSystemConfigurationEnum.RSM_B2B2C_LINKAGE_ENABLED)
					&& RSMRelationType.B2B2C.equals(leadDetail.getRsmRelation()) ? 3 : 2;

			List<String> userIds = Arrays.stream(referre.getFriendTree().split(";"))
					.collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
						Collections.reverse(list);
						return list.stream().limit(friendTreeLimit).toList();
					}));

			List<RSMFocalType> types = List.of(RSMFocalType.REFEREE, RSMFocalType.REFERRER_LEVEL_1,
					RSMFocalType.REFERRER_LEVEL_2);
			Map<RSMFocalType, UserShortInformationDTO> userMap = IntStream.range(0, userIds.size()).boxed()
					.collect(Collectors.toMap(types::get,
							v -> refererMap.getOrDefault(userIds.get(v), new UserShortInformationDTO())));
			focalType.putAll(userMap);
		}
		return focalType;
	}

	private Referre getReferre(UserInterestRecordRSMViewDTO leadDetail) {
		Referre referre = referreService.findByReferredId(leadDetail.getUserId());
		if (Objects.isNull(referre) && RSMRelationType.B2B.equals(leadDetail.getRsmRelation())) {
			return getReferreForBizInstitution(leadDetail);
		}
		return referre;
	}

	// for Biz (B2B), need to get referee by all user in the company
	// the reason is the lead application can be done by other user in the company
	private Referre getReferreForBizInstitution(UserInterestRecordRSMViewDTO leadDetail) {
		Set<String> allUserIdsInCompany = bizAccountClient.findUsersByInstitutionRefNo(leadDetail.getCompanyId())
				.stream().map(BizUserDTO::getId).collect(Collectors.toSet());
		return referreService.findFirstByReferredIdIn(allUserIdsInCompany);
	}

	private Map<String, UserShortInformationDTO> findReferrer(Referre referre) {
		List<String> userIds = Arrays.asList(referre.getFriendTree().split(";"));

		Map<String, UserShortInformationDTO> userMap = new HashMap<>();
		userMap.putAll(findMxBizUser(userIds));
		userMap.putAll(findMxAppUser(userIds));
		return userMap;
	}

	private Map<String, UserShortInformationDTO> findMxBizUser(List<String> userIds) {
		Map<String, UserShortInformationDTO> userMap = new HashMap<>();
		List<BizUserDTO> userBiz = bizAccountClient.findUsersByListOfIds(userIds, true);
		for (BizUserDTO bizUser : userBiz) {
			userMap.put(bizUser.getId(), MapStructConverter.MAPPER.toUserShortInformationDTO(bizUser));
		}
		return userMap;
	}

	private Map<String, UserShortInformationDTO> findMxAppUser(List<String> userIds) {
		Map<String, UserShortInformationDTO> userMap = new HashMap<>();
		List<UserFullNameResponse> userApp = proxyFeignClient.getAccountFeignClient()
				.getUserFullNameByUserId(new UserIdListRequest(userIds.stream().toList(), true));
		for (UserFullNameResponse user : userApp) {
			userMap.put(user.getId(), MapStructConverter.MAPPER.toUserShortInformationDTO(user));
		}
		return userMap;
	}

	@Override
	public void disburseCommission(DisburseCommissionRequest request) {
		UserInterestRecordRSMViewDTO leadDetail = getDetail(request.getApplicationType(), request.getApplicationId());
		Objects.requireNonNull(leadDetail);
		LeadRSMUpdateRequest updateRequest = LeadRSMUpdateRequest.builder().id(leadDetail.getId())
				.status(RSMStatus.PENDING).commissionAttached(true).build();

		updateInfo(request.getApplicationType(), updateRequest);
		List<PointEarning> userAwardedPoints = disburseCommission(request, leadDetail, SourceSystemEnum.MXAPP);

		if (pointEarningService.countByStatus(leadDetail.getId(), PointEarningStatus.PENDING) == 0) {
			updateRequest.setStatus(RSMStatus.SUCCESS);
			updateInfo(request.getApplicationType(), updateRequest);
		}

		if (!userAwardedPoints.isEmpty()) {
			deductPointEarningDeletedAccount(userAwardedPoints, SourceSystemEnum.MXAPP);
			sendAwardedNotification(userAwardedPoints);
		}
	}

	@Override
	public void disburseCommissionBiz(DisburseCommissionBizRequest request) {
		UserInterestRecordRSMViewDTO leadDetail = getDetailFromBiz(request.getLeadDto());
		List<PointEarning> userAwardedPoints = disburseCommission(request.getDisburseCommissionRequest(), leadDetail,
				SourceSystemEnum.MXBIZ);
		if (!userAwardedPoints.isEmpty()) {
			deductPointEarningDeletedAccount(userAwardedPoints, SourceSystemEnum.MXBIZ);
		}
	}

	private List<PointEarning> disburseCommission(DisburseCommissionRequest request,
			UserInterestRecordRSMViewDTO leadDetail, SourceSystemEnum source) {
		List<UpdateRevenueDTO> updateRevenues = request.getCommissions().stream()
				.map(v -> UpdateRevenueDTO.builder().applicationId(request.getApplicationId())
						.rsmHeaderId(v.getHeaderId()).amount(v.getRevenue()).relationType(leadDetail.getRsmRelation())
						.build())
				.toList();

		ChartOfAccount chartOfAccount = chartOfAccountService
				.findByAccountCode(ChartOfAccountType.RSM_PRODUCT_REVENUE.getCode());
		for (UpdateRevenueDTO data : updateRevenues) {
			rsmCaptureService.updateRevenue(data, chartOfAccount, source);
		}

		// MoneyX
		List<PointEarning> nonUserEarning = pointEarningService.findNonUserEarning(request.getApplicationId(),
				PointEarningStatus.PENDING);
		if (!nonUserEarning.isEmpty()) {
			pointEarningIntgService.disburse(nonUserEarning.stream().map(PointEarning::getId).toList(), source);
		}

		List<String> pointEarningIds = request.getCommissions().stream()
				.flatMap(commission -> commission.getEarnings().stream())
				.filter(earning -> PointEarningStatus.AWARDED.equals(earning.getStatus()))
				.map(DisburseCommissionRequest.EarningDisburse::getId).toList();

		List<PointEarning> userAwardedPoints = new ArrayList<>();
		if (!CollectionUtils.isEmpty(pointEarningIds)) {
			userAwardedPoints = pointEarningIntgService.disburse(pointEarningIds, source);
		}
		return userAwardedPoints;
	}

	private void sendAwardedNotification(List<PointEarning> pointEarnings) {
		for (PointEarning pointEarning : pointEarnings) {
			if (RSMFocalType.MONEYX.equals(pointEarning.getRsmDetailFocalType())
					|| pointEarning.getPointAmount().compareTo(BigDecimal.ZERO) <= 0) {
				continue;
			}

			Map<String, Object> dataMap = new HashMap<>();
			dataMap.put("pointAmount", currencyFormatterUtil.formatWithoutDecimal(pointEarning.getPointAmount()));

			if (RSMFocalType.REFEREE.equals(pointEarning.getRsmDetailFocalType())) {
				PushNotificationDataRequest notificationRequest = PushNotificationDataRequest.builder()
						.userId(pointEarning.getUserId()).code(MessageTemplateCodeEnum.APPLICATION_EARNING_UPDATE)
						.model(dataMap).channel(ChannelCodeEnum.APPLICATION).saveToInbox(true)
						.isRedirectToModuleFromInbox(true).build();
				kafkaTemplate.send(KafkaTopic.SEND_NOTIFICATION, jsonUtil.convertToString(notificationRequest));
			}
			else {
				PushNotificationDataRequest notificationRequest = PushNotificationDataRequest.builder()
						.userId(pointEarning.getUserId()).code(MessageTemplateCodeEnum.REFERRAL_EARNING_UPDATE)
						.model(dataMap).channel(ChannelCodeEnum.APPLICATION).saveToInbox(true)
						.isRedirectToModuleFromInbox(true).build();
				kafkaTemplate.send(KafkaTopic.SEND_NOTIFICATION, jsonUtil.convertToString(notificationRequest));
			}
		}
	}

	@Override
	public void deleteCommission(DeleteCommissionRequest request) {
		RsmCapture rsmCapture = rsmCaptureService.findByApplicationIdAndRsmHeaderId(request.getApplicationId(),
				request.getHeaderId());
		if (rsmCapture.isConfirmed()) {
			throw new BusinessException(ErrorCodeEnum.RSM_COMMISSION_CONFIRMED);
		}
		rsmCaptureService.delete(rsmCapture);
	}

	@Override
	public void deleteCommission(BulkDeleteCommissionRequest request) {
		List<RsmCapture> rsmCaptures;
		if (CollectionUtils.isEmpty(request.getHeaderId())) {
			rsmCaptures = rsmCaptureService.findByApplicationId(request.getApplicationId());
		}
		else {
			rsmCaptures = rsmCaptureService.findByApplicationIdAndRsmHeaderId(request.getApplicationId(),
					request.getHeaderId());
		}

		for (RsmCapture rsmCapture : rsmCaptures) {
			if (rsmCapture.isConfirmed()) {
				log.warn("delete confirmed commission is prohibit");
				continue;
			}
			rsmCaptureService.delete(rsmCapture);
		}
	}

	@Override
	public void updateStatus(UpdateRSMInfoRequest request) {
		UserInterestRecordRSMViewDTO userInterestedRecord = getDetail(request.getApplicationType(),
				request.getApplicationId());
		Objects.requireNonNull(userInterestedRecord);
		if (userInterestedRecord.isRsmCommissionAttached()) {
			throw new BusinessException(ErrorCodeEnum.RSM_MODIFY_STATUS_NOT_ALLOWED);
		}
		updateInfo(request.getApplicationType(),
				LeadRSMUpdateRequest.builder().id(userInterestedRecord.getId()).status(request.getStatus())
						.commissionAttached(userInterestedRecord.isRsmCommissionAttached()).build());
	}

	private void updateInfo(ApplicationType applicationType, LeadRSMUpdateRequest request) {
		switch (applicationType) {
			case LEAD_GEN, REDIRECT -> proxyFeignClient.getBankFeignClient().updateUserInterestedRSMInfo(request);
			case AFFILIATE -> proxyFeignClient.getAnalyticFeignClient().updateUserInterestedRSMInfo(request);
			case PROPERTY -> proxyFeignClient.getPropertyFeignClient().updatePurchasePropertyRSMInfo(request);
			default -> throw new IllegalArgumentException("Application type " + applicationType + " is not supported");
		}
	}

	@Override
	public void deleteDraftCommission(String applicationId) {

		List<RsmCapture> toBeDelete = rsmCaptureService.rsmListingDelete(applicationId);

		List<String> rsmHeaderIds = toBeDelete.stream().map(capture -> capture.getRsmHeader().getId()).toList();

		rsmCaptureService.deleteDraftCaptures(applicationId, rsmHeaderIds);

	}

	@Override
	public void bulkUpdateStatus(BulkUpdateRsmInfoRequest request) {
		updateInfo(request.getApplicationType(),
				LeadRSMUpdateRequest.builder().ids(request.getApplicationIds()).status(request.getStatus()).build());
	}

	private void deductPointEarningDeletedAccount(List<PointEarning> userAwardedPoints, SourceSystemEnum source) {
		List<String> userIds = userAwardedPoints.stream().map(PointEarning::getUserId).filter(StringUtils::isNotBlank)
				.toList();

		Map<String, UserShortInformationDTO> userMap = new HashMap<>();
		userMap.putAll(findMxBizUser(userIds));
		userMap.putAll(findMxAppUser(userIds));

		// full-name blank indicating that its deleted user
		Set<String> deletedUserIds = userIds.stream()
				.filter(userId -> userMap.containsKey(userId) && StringUtils.isBlank(userMap.get(userId).getFullName()))
				.collect(Collectors.toSet());
		if (!deletedUserIds.isEmpty()) {
			Map<String, BigDecimal> userIdEarningMap = userAwardedPoints.stream()
					.filter(pointEarning -> StringUtils.isNotBlank(pointEarning.getUserId())
							&& deletedUserIds.contains(pointEarning.getUserId()))
					.collect(Collectors.toMap(PointEarning::getUserId, PointEarning::getPointAmount, BigDecimal::add));
			pointTransactionIntgService.handleAccountDeletion(userIdEarningMap, userMap, source);
		}

	}

}
