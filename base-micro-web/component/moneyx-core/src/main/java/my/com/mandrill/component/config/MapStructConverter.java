package my.com.mandrill.component.config;

import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.projection.*;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.response.RsmClosingBalanceResponse;
import my.com.mandrill.utilities.general.service.LazyLoadingAwareMapper;
import my.com.mandrill.utilities.general.util.DateUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter extends LazyLoadingAwareMapper {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	ReferralCode toReferralCode(CreateReferralCodeRequest data);

	@Mapping(source = "createdDate", target = "dateOfApplication")
	ReferralCodeResponse toReferralCodeResponse(ReferralCode referralCode);

	@Mapping(source = "lastModifiedDate", target = "confirmedOn")
	@Mapping(source = "pointAmount", target = "point")
	PointEarningData toPointEarningData(PointEarning data);

	default String mapPointEarningDisbursedBy(PointEarning pointEarning) {
		return PointEarningStatus.AWARDED.equals(pointEarning.getStatus()) ? pointEarning.getLastModifiedBy() : null;
	}

	default Instant mapPointEarningDisbursedDate(PointEarning pointEarning) {
		return PointEarningStatus.AWARDED.equals(pointEarning.getStatus()) ? pointEarning.getLastModifiedDate() : null;
	}

	@Mapping(target = "disbursedBy", expression = "java(mapPointEarningDisbursedBy(data))")
	@Mapping(target = "disbursedDate", expression = "java(mapPointEarningDisbursedDate(data))")
	@Mapping(source = "refNo", target = "transactionId") // backward compatibility: we're
															// moving the transactionId to
															// refNo
	LeadPointEarningData toLeadPointEarningData(PointEarning data);

	LeadCommissionDetailData toLeadCommissionDetailData(RsmDetail data);

	LeadCommissionResponse toLeadCommissionResponse(RsmHeader data);

	@Mapping(source = "refNo", target = "applicationRefNo")
	@Mapping(source = "createdDate", target = "applicationDate")
	@Mapping(source = "rsmStatus", target = "status")
	@Mapping(source = "rsmRelation", target = "relationType")
	LeadDetailData toLeadDetailData(UserInterestRecordRSMViewDTO data);

	PointWithdrawal toPointWithdrawal(CreatePointWithdrawalRequest request);

	@Mapping(target = "applicationId", source = "refNo") // backward compatibility: we're
															// moving applicationId to
															// refNo
	@Mapping(target = "paymentInfoStatus", source = "paymentInfoStatus.name")
	@Mapping(target = "status", source = "status.name")
	PointWithdrawalResponse toPointWithdrawalResponse(PointWithdrawal pointWithdrawal);

	@Mapping(source = "confirmedOn", target = "confirmedOn", qualifiedByName = "instantToLocalDate")
	PointEarningResponse toPointEarningResponse(PointEarningData pointEarningData);

	RefereeEarningResponse toRefereeEarningResponse(RefereeEarningData refereeEarningData);

	@Named("instantToLocalDate")
	default LocalDate instantToLocalDate(Instant instant) {
		return DateUtil.instantToLocalDate(instant);
	}

	@Mapping(source = "productProviderId", target = "partnerId")
	BizRSMCommissionListingResponse toBizRSMCommissionListingResponse(
			BizRSMHeaderListingProjection bizRSMHeaderListingProjection);

	@Mapping(source = "productProviderId", target = "partnerId")
	@Mapping(source = "isActive", target = "active")
	BizRSMCommissionDetailResponse toBizRSMCommissionDetailResponse(RsmHeader byId);

	@Mapping(source = "isActive", target = "active")
	BizRSMCommissionDetailResponse toBizRSMCommissionDetailResponse(BizRSMHeaderWithDetailProjection projection);

	RsmClosingBalanceResponse toRsmClosingBalanceResponse(ClosingBalanceProjection rsmClosingBalance);

	@Mapping(target = "commissionId", ignore = true)
	@Mapping(target = "revenue", source = "product.productRevenue")
	@Mapping(target = "productCategoryId", source = "product.productCategoryId")
	@Mapping(target = "productCategoryName", source = "product.productCategoryName")
	@Mapping(target = "productTypeId", source = "product.productTypeId")
	@Mapping(target = "productTypeName", source = "product.productTypeName")
	@Mapping(target = "productProviderId", source = "product.productProviderId")
	@Mapping(target = "productProviderName", source = "product.productProviderName")
	@Mapping(target = "productId", source = "product.productId")
	@Mapping(target = "productName", source = "product.productName")
	RsmHeader toRsmHeader(RsmHeaderRequest request);

	@Mapping(source = "partnerId", target = "productProviderId")
	@Mapping(source = "partnerName", target = "productProviderName")
	@Mapping(source = "productRevenue", target = "revenue")
	@Mapping(source = "active", target = "isActive")
	RsmHeader toRsmHeader(BizCreateCommissionRequest request);

	@Mapping(target = "createdDate", source = "createdDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "product.productCategoryId", source = "productCategoryId")
	@Mapping(target = "product.productCategoryName", expression = "java(mapProductCategoryName(rsmHeader))")
	@Mapping(target = "product.productTypeId", source = "productTypeId")
	@Mapping(target = "product.productTypeName", expression = "java(mapProductTypeName(rsmHeader))")
	@Mapping(target = "product.productProviderId", source = "productProviderId")
	@Mapping(target = "product.productProviderName", source = "productProviderName")
	@Mapping(target = "product.productName", source = "productName")
	@Mapping(target = "product.productRevenue", source = "revenue")
	@Mapping(target = "startDate", source = "startDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "endDate", source = "endDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "externalStartDate", source = "externalStartDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "externalEndDate", source = "externalEndDate", qualifiedByName = "mapInstantToLocalDate")
	RsmHeaderResponse toRsmHeaderResponse(RsmHeader header);

	@Mapping(target = "createdDate", source = "createdDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "product.productCategoryId", source = "productCategoryId")
	@Mapping(target = "product.productCategoryName", source = "productCategoryName")
	@Mapping(target = "product.productTypeId", source = "productTypeId")
	@Mapping(target = "product.productTypeName", source = "productTypeName")
	@Mapping(target = "product.productProviderId", source = "productProviderId")
	@Mapping(target = "product.productProviderName", source = "productProviderName")
	@Mapping(target = "product.productName", source = "productName")
	@Mapping(target = "product.productRevenue", source = "revenue")
	@Mapping(target = "product.productId", source = "productId")
	@Mapping(target = "startDate", source = "startDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "endDate", source = "endDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "externalStartDate", source = "externalStartDate", qualifiedByName = "mapInstantToLocalDate")
	@Mapping(target = "externalEndDate", source = "externalEndDate", qualifiedByName = "mapInstantToLocalDate")
	SearchRsmHeaderByProductResponse toSearchRsmHeaderByProductResponse(RsmHeader header);

	@Named("mapInstantToLocalDate")
	default LocalDate mapInstantToLocalDate(Instant instant) {
		if (instant == null) {
			return null;
		}
		return instant.atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toLocalDate();
	}

	@Mapping(target = "productRevenue", source = "revenue")
	@Mapping(target = "productCategoryName", expression = "java(mapProductCategoryName(header))")
	@Mapping(target = "productTypeName", expression = "java(mapProductTypeName(header))")
	RsmHeaderProductDTO toRsmHeaderProductDTO(RsmHeader header);

	@Mapping(target = "transactionId", source = "refNo") // backward compatible: we're
															// moving to refNo
	RsmPointTransactionHistoryResponse toRsmPointTransactionHistoryResponse(
			PointTransactionHistoriesProjection pointTransaction);

	@Mapping(target = "name", source = "pointName")
	@Mapping(target = "status", ignore = true)
	PointTransaction toPointTransaction(PointEarning pointEarning);

	@Mapping(target = "pointsAwarded", source = "pointAwarded")
	@Mapping(target = "pointsSpent", source = "pointSpent")
	@Mapping(target = "date", source = "closingDate")
	BizPointSummaryResponse toBizPointSummaryResponse(ClosingBalanceProjection closingBalance);

	default String mapPointEarningStatus(PointEarningStatus status) {
		return status != null ? status.getName() : null;
	}

	default String mapApplicationType(ApplicationType applicationType) {
		return applicationType != null ? applicationType.getName() : null;
	}

	default String mapRsmFocalType(RSMFocalType rsmDetailFocalType) {
		return rsmDetailFocalType != null ? rsmDetailFocalType.getName() : null;
	}

	default String mapProductCategoryName(RsmHeader rsmHeader) {
		return rsmHeader != null && rsmHeader.getProductCategoryName() != null
				? ProductGroupEnum.getNameByValue(rsmHeader.getProductCategoryName()) : null;
	}

	default String mapProductTypeName(RsmHeader rsmHeader) {
		return rsmHeader != null && rsmHeader.getProductTypeName() != null
				? UserInterestProductTypeEnum.getNameByCode(rsmHeader.getProductTypeName()) : null;
	}

	@Mapping(target = "transactionId", source = "refNo") // backward compatibility: we're
															// moving transactionId to
															// refNo
	@Mapping(target = "productCategoryName", expression = "java(mapProductCategoryName(pointEarning.getRsmHeader()))")
	@Mapping(target = "productProviderName", source = "rsmHeader.productProviderName")
	@Mapping(target = "productName", source = "rsmHeader.productName")
	@Mapping(target = "rewardName", source = "rsmHeader.rewardName")
	@Mapping(target = "status", expression = "java(mapPointEarningStatus(pointEarning.getStatus()))")
	@Mapping(target = "applicationType", expression = "java(mapApplicationType(pointEarning.getApplicationType()))")
	@Mapping(target = "rsmDetailFocalType", expression = "java(mapRsmFocalType(pointEarning.getRsmDetailFocalType()))")
	PointDisbursementResponse toPointDisbursementResponse(PointEarning pointEarning);

	@Mapping(target = "transactionId", source = "refNo") // backward compatibility: we're
															// moving transactionId to
															// refNo
	@Mapping(target = "productCategoryName", expression = "java(mapProductCategoryName(pointEarning.getRsmHeader()))")
	@Mapping(target = "productProviderName", source = "rsmHeader.productProviderName")
	@Mapping(target = "productName", source = "rsmHeader.productName")
	@Mapping(target = "rewardName", source = "rsmHeader.rewardName")
	@Mapping(target = "status", expression = "java(mapPointEarningStatus(pointEarning.getStatus()))")
	@Mapping(target = "applicationType", expression = "java(mapApplicationType(pointEarning.getApplicationType()))")
	@Mapping(target = "rsmDetailFocalType", expression = "java(mapRsmFocalType(pointEarning.getRsmDetailFocalType()))")
	BizPointDisbursementResponse toBizPointDisbursementResponse(PointEarning pointEarning);

	@Mapping(target = "pointDisbursementPointsEarningDetailResponse.pointsEarningId", source = "refNo")
	@Mapping(target = "pointDisbursementPointsEarningDetailResponse.pointsName", source = "pointName")
	@Mapping(target = "pointDisbursementPointsEarningDetailResponse.pointsEarningStatus", source = "status")
	@Mapping(target = "pointDisbursementPointsEarningDetailResponse.pointsAmount", source = "pointAmount")
	@Mapping(target = "pointDisbursementPointsEarningDetailResponse.pointsDisbursementDate",
			source = "pointDisbursementDate")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.applicationId", source = "applicationId")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.applicationRefNo", source = "applicationRefNo")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.applicationDate", source = "applicationDate")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.referralCode", source = "referralCode")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.applicationType", source = "applicationType")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.rsmScenario", source = "rsmScenario")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.rsmFocalType", source = "rsmDetailFocalType")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.productCategory",
			source = "rsmHeader.productCategoryName")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.provider", source = "rsmHeader.productProviderName")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.productName", source = "rsmHeader.productName")
	@Mapping(target = "pointDisbursementApplicationDetailResponse.rsmRewardName", source = "rsmHeader.rewardName")
	PointDisbursementDetailResponse toPointDisbursementDetailResponse(PointEarning pointEarning);

	@Mapping(target = "companyName", source = "institution.name")
	@Mapping(target = "companyId", source = "institution.refNo")
	@Mapping(target = "referralCode", source = "referralCodeUsed")
	@Mapping(target = "source", constant = "MXBIZ")
	UserShortInformationDTO toUserShortInformationDTO(BizUserDTO data);

	@Mapping(target = "source", constant = "MXAPP")
	UserShortInformationDTO toUserShortInformationDTO(UserFullNameResponse data);

	@Mapping(source = "userId", target = "id")
	@Mapping(source = "userRefNo", target = "refNo")
	UserShortInformationDTO toUserShortInformationDTO(UserInterestRecordRSMViewDTO data);

	@Mapping(target = "status", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "createdDate", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "lastModifiedBy", ignore = true)
	@Mapping(target = "lastModifiedDate", ignore = true)
	PointTransaction toPointTransaction(PointWithdrawal pointWithdrawal);

	PointWithdrawal toPointWithdrawal(CreateBizPointWithdrawalRequest request);

	BizPointHistoryResponse toBizPointHistoryResponse(BizPointHistoryProjection bizPointHistoryProjection);

	ChartOfAccountData toChartOfAccountData(ChartOfAccount chartOfAccount);

}