package my.com.mandrill.component.repository;

import feign.Param;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.dto.projection.PointEarningAllocationProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.List;

public interface PointAllocationRepository extends JpaRepository<PointAllocation, String> {

	@Query("""
			    SELECT
			        pt.id as transactionId,
			        pt.pointAmount as totalPoints,
			        COALESCE(SUM(pa.pointsAllocated), 0) as allocatedPoints,
			        (pt.pointAmount - COALESCE(SUM(pa.pointsAllocated), 0)) as remainingPoints,
			        pt.approvedDate as awardedDate,
			        pt.expiredDue as expiryDate
			    FROM PointTransaction pt
			    LEFT JOIN PointAllocation pa ON pt.id = pa.originTxId
			    WHERE pt.userId = :userId
			      AND pt.type = :type
			      AND pt.expiredDue > CURRENT_TIMESTAMP
			    GROUP BY pt.id, pt.pointAmount, pt.approvedDate, pt.expiredDue
			    HAVING (pt.pointAmount - COALESCE(SUM(pa.pointsAllocated), 0)) > 0
			    ORDER BY pt.id ASC
			""")
	List<PointEarningAllocationProjection> findAvailablePointEarnings(@Param("userId") String userId,
																	  @Param("type") PointTransactionType type);

	@Query("""
			    SELECT
			        pt.id as transactionId,
			        pt.userId as userId,
			        pt.pointAmount as totalPoints,
			        COALESCE(SUM(pa.pointsAllocated), 0) as allocatedPoints,
			        (pt.pointAmount - COALESCE(SUM(pa.pointsAllocated), 0)) as remainingPoints,
			        pt.approvedDate as awardedDate,
			        pt.expiredDue as expiryDate
			    FROM PointTransaction pt
			    LEFT JOIN PointAllocation pa ON pt.id = pa.originTxId
			    WHERE pt.type = :type
			      AND pt.expiredDue IS NOT NULL
			      AND pt.expiredDue BETWEEN :dateFrom AND :dateTo
			      AND pt.userId IS NOT NULL
			    GROUP BY pt.id, pt.userId, pt.pointAmount, pt.approvedDate, pt.expiredDue
			    HAVING (pt.pointAmount - COALESCE(SUM(pa.pointsAllocated), 0)) > 0
			    ORDER BY pt.expiredDue ASC, pt.approvedDate ASC
			""")
	List<PointEarningAllocationProjection> findExpiredPointEarnings(Instant dateFrom, Instant dateTo,
																	PointTransactionType type);

}
