package my.com.mandrill.component.repository;

import feign.Param;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.dto.projection.AvailablePointEarningProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PointAllocationRepository extends JpaRepository<PointAllocation, String> {

	@Query("""
			    SELECT
			        pt.id as transactionId,
			        pt.pointAmount as totalPoints,
			        COALESCE(SUM(pa.pointsAllocated), 0) as allocatedPoints,
			        (pt.pointAmount - COALESCE(SUM(pa.pointsAllocated), 0)) as availablePoints,
			        pt.approvedDate as awardedDate,
			        pt.expiredDue as expiryDate
			    FROM PointTransaction pt
			    LEFT JOIN PointAllocation pa ON pt.id = pa.originTxId
			    WHERE pt.userId = :userId
			      AND pt.type = :type
			      AND pt.expiredDue > CURRENT_TIMESTAMP
			    GROUP BY pt.id, pt.pointAmount, pt.approvedDate, pt.expiredDue
			    HAVING (pt.pointAmount - COALESCE(SUM(pa.pointsAllocated), 0)) > 0
			    ORDER BY pt.approvedDate ASC
			""")
	List<AvailablePointEarningProjection> findAvailablePointEarnings(@Param("userId") String userId,
			@Param("type") PointTransactionType type);

}
