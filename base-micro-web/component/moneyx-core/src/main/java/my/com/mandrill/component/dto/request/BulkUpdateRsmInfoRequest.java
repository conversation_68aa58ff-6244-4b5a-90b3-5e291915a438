package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;

import java.io.Serializable;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BulkUpdateRsmInfoRequest implements Serializable {

	@NotNull
	private ApplicationType applicationType;

	@NotNull
	private Set<String> applicationIds;

	@NotNull
	private RSMStatus status;

}
