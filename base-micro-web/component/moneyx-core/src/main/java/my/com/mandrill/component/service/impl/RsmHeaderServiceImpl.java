package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.projection.BizRSMHeaderWithDetailProjection;
import my.com.mandrill.component.dto.request.RSMHeaderSearchRequest;
import my.com.mandrill.component.dto.response.BizRSMCommissionListingResponse;
import my.com.mandrill.component.dto.response.RsmHeaderPublicResponse;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.RsmDetailRepository;
import my.com.mandrill.component.repository.RsmHeaderRepository;
import my.com.mandrill.component.repository.specification.RSMHeaderSpecification;
import my.com.mandrill.component.service.RsmHeaderService;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class RsmHeaderServiceImpl implements RsmHeaderService {

	private final RsmHeaderRepository headerRepository;

	private final RsmDetailRepository detailRepository;

	@Override
	public RsmHeader findById(String id) {
		return headerRepository.findById(id).orElseThrow(ExceptionPredicate.rsmHeaderNotFoundById(id));
	}

	@Override
	@Transactional
	public void save(RsmHeader header) {
		headerRepository.save(header);
	}

	@Override
	@Transactional
	public void create(RsmHeader header, List<RsmDetail> details) {
		headerRepository.save(header);
		detailRepository.saveAll(details);
	}

	@Override
	public Page<RsmHeader> findAll(RSMHeaderSearchRequest request, Pageable pageable) {
		return headerRepository.findAll(RSMHeaderSpecification.withFilters(request), pageable);
	}

	// Biz
	@Override
	public Page<BizRSMCommissionListingResponse> getPaginatedCommissionListing(@NonNull Pageable pageable,
			String globalSearch, String productCategoryId, String partnerId, String productId, Boolean status) {
		String idOrRewardName = globalSearch;
		if (StringUtils.isEmpty(idOrRewardName)) {
			// Sanitise for query
			idOrRewardName = null;
		}

		return headerRepository.findProjectedBySource(pageable, SourceSystemEnum.MXBIZ, idOrRewardName,
				productCategoryId, partnerId, productId, status)
				.map(MapStructConverter.MAPPER::toBizRSMCommissionListingResponse);
	}

	@Override
	public List<BizRSMHeaderWithDetailProjection> getCommissionListWithDetail(String globalSearch,
			String productCategoryId, String partnerId, String productId, Boolean status) {
		return headerRepository.findProjectedWithDetail(SourceSystemEnum.MXBIZ, globalSearch, productCategoryId,
				partnerId, productId, status);
	}

	@Override
	public List<RsmHeader> findAll(RSMHeaderSearchRequest request) {
		return headerRepository.findAll(RSMHeaderSpecification.withFilters(request));
	}

	@Override
	public List<BizRSMCommissionListingResponse> findCommissions(String commissionId, String productId,
			String productProviderId) {

		return headerRepository
				.findProjectedLeadManagement(commissionId, productId, productProviderId, SourceSystemEnum.MXBIZ)
				.stream().map(MapStructConverter.MAPPER::toBizRSMCommissionListingResponse).toList();

	}

	@Override
	public Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source) {
		return headerRepository.getProductIdsWithActiveCommission(source);
	}

	@Override
	public List<String> findProductTypeIdsWithActiveCommission(@NonNull SourceSystemEnum source) {
		return headerRepository.findProductTypeIdsWithActiveCommission(source);
	}

	@Override
	public Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source, String productTypeName) {
		return headerRepository.getProductIdsWithActiveCommission(source, productTypeName);
	}

	@Override
	public Set<String> findAllProviderIdsWithActiveCommission(@NonNull SourceSystemEnum source,
			Collection<String> productTypeNames) {
		return headerRepository.findAllProviderIdsWithActiveCommission(source, productTypeNames);
	}

	@Override
	public List<RsmHeaderPublicResponse> getExternalRsmHeaderList() {
		return headerRepository.findAllExternalRsmHeader();
	}

}
