package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.BizAccountClient;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.PointTransactionStatus;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.domain.PointTransaction;
import my.com.mandrill.component.dto.model.BizUserDTO;
import my.com.mandrill.component.dto.projection.ClosingBalanceProjection;
import my.com.mandrill.component.dto.response.BizPointHistoryResponse;
import my.com.mandrill.component.dto.response.RsmPointTransactionHistoryResponse;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.PointWithdrawalRepository;
import my.com.mandrill.component.service.PointTransactionService;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PointTransactionServiceImpl implements PointTransactionService {

	private final PointTransactionRepository pointTransactionRepository;

	private final PointWithdrawalRepository pointWithdrawalRepository;

	private final BizAccountClient bizAccountClient;

	@Transactional
	@Override
	public void save(PointTransaction pointTransaction) {
		pointTransactionRepository.save(pointTransaction);
	}

	@Transactional
	@Override
	public List<PointTransaction> saveAll(List<PointTransaction> pointTransactionList) {
		return pointTransactionRepository.saveAll(pointTransactionList);
	}

	@Override
	public ClosingBalanceProjection calculatePointSpentByApprovedDate(LocalDate date,
			SourceSystemEnum sourceSystemEnum) {
		Instant startDate = date.atStartOfDay().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toInstant();
		Instant endDate = date.plusDays(1).atStartOfDay().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toInstant();
		return pointTransactionRepository.getPointSpentByDate(
				Arrays.asList(PointTransactionType.ADMIN_FEE_WITHDRAWAL, PointTransactionType.CASH_WITHDRAWAL),
				PointTransactionType.REFERRAL_EARNING, startDate, endDate, PointTransactionStatus.SUCCESS,
				sourceSystemEnum);
	}

	@Override
	public List<RsmPointTransactionHistoryResponse> findTransactionHistory(String userId, String cursor,
			Integer limit) {
		return pointTransactionRepository.findTransactionHistoriesByCursor(userId, cursor, limit).stream()
				.map(MapStructConverter.MAPPER::toRsmPointTransactionHistoryResponse).toList();
	}

	@Override
	public BigDecimal getAvailableBalance(Collection<String> userIds) {
		if (userIds.isEmpty())
			return BigDecimal.ZERO;

		BigDecimal balanceIn = Optional.ofNullable(pointTransactionRepository.getSummaryPointAmount(userIds,
				Set.of(PointTransactionType.REFERRAL_EARNING))).orElse(BigDecimal.ZERO);

		BigDecimal balanceOut = Optional.ofNullable(
				pointTransactionRepository.getSummaryPointAmount(userIds, Set.of(PointTransactionType.CASH_WITHDRAWAL,
						PointTransactionType.ADMIN_FEE_WITHDRAWAL, PointTransactionType.POINT_EXPIRY)))
				.orElse(BigDecimal.ZERO);
		return balanceIn.subtract(balanceOut);
	}

	@Override
	public BigDecimal getAvailableBalance(String userId) {
		return this.getAvailableBalance(Set.of(userId));
	}

	@Override
	public Page<BizPointHistoryResponse> findBizPointHistory(@NonNull String institutionId,
			@NonNull Pageable pageable) {
		List<String> userIds = bizAccountClient.findUsersByInstitutionId(institutionId).stream().map(BizUserDTO::getId)
				.toList();

		if (userIds.isEmpty())
			return Page.empty();

		return pointTransactionRepository.findBizPointHistoryForUserIdIn(userIds, pageable)
				.map(MapStructConverter.MAPPER::toBizPointHistoryResponse);
	}

}
