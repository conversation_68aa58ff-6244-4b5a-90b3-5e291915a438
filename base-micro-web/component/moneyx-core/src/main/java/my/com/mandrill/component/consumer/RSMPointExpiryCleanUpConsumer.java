package my.com.mandrill.component.consumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Slf4j
@Service
@RequiredArgsConstructor
public class RSMPointExpiryCleanUpConsumer {

	@KafkaListener(topics = KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC)
	public void consume(String message) {
		try {
			LocalDate executionDate = LocalDate.now().atStartOfDay().toLocalDate();
			log.info("feature=point-expiry-clean-up|message=point expiry is running for date : {}", executionDate);




		}
		catch (Exception e) {
			log.warn("feature=point-expiry-clean-up|error={}", e.getMessage(), e);
		}
	}

}
