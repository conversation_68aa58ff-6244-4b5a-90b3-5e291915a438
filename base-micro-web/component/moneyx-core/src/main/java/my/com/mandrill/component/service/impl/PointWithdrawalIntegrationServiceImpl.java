package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.BizAccountClient;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.*;
import my.com.mandrill.component.domain.ChartOfAccount;
import my.com.mandrill.component.domain.PointTransaction;
import my.com.mandrill.component.domain.PointWithdrawal;
import my.com.mandrill.component.dto.model.BizUserDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.PointWithdrawalResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.GlobalSystemConfigurationDTO;
import my.com.mandrill.utilities.feign.dto.request.PushNotificationDataRequest;
import my.com.mandrill.utilities.feign.dto.request.UserRefNoListRequest;
import my.com.mandrill.utilities.feign.dto.response.PaymentAccountDetailResponse;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointWithdrawalIntegrationServiceImpl implements PointWithdrawalIntegrationService {

	private final PointWithdrawalService pointWithdrawalService;

	private final PointTransactionService pointTransactionService;

	private final PointEarningService pointEarningService;

	private final RedisService redisService;

	private final RunningNumberUtil runningNumberUtil;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	private final FeatureFlagOutbound featureFlagOutbound;

	private final ProxyFeignClient proxyFeignClient;

	private final ChartOfAccountService chartOfAccountService;

	private final ReferralIntegrationService referralIntegrationService;

	private final ClosingBalanceIntgService closingBalanceIntgService;

	private final BizAccountClient bizAccountClient;

	private final PointAllocationIntgService pointAllocationIntgService;

	@Override
	public String processPointWithdrawal(CreatePointWithdrawalRequest request, String uniqueProcessId) {
		return redisService.executeSingleProcess(
				String.format(CacheKey.RSM_WITHDRAWAL_TRANSACTION_CACHE_KEY, uniqueProcessId), uniqueProcessId, () -> {
					CurrentUserIdDTO userDTO = proxyFeignClient.getAccountFeignClient().getCurrentUserId();
					this.validateRequest(request, userDTO);
					PaymentAccountDetailResponse paymentAccountDetail = proxyFeignClient.getBankFeignClient()
							.getPaymentAccount(userDTO.getId());
					BigDecimal withdrawalAdminFee = getAdminFee();
					PointWithdrawal pointWithdrawal = buildPointWithdrawal(request, userDTO, paymentAccountDetail,
							withdrawalAdminFee);

					pointWithdrawalService.save(pointWithdrawal);
					return "success";
				}, ErrorCodeGlobalEnum.FAILED_TO_PROCESS_WITHDRAWAL, TimeUnit.HOURS);
	}

	private BigDecimal getAdminFee() {
		if (featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.RSM_WITHDRAWAL_ADMIN_FEE_ENABLED)) {
			return new BigDecimal(proxyFeignClient.getCommonFeignClient().getIntegrationGlobalSystemConfigurationByCode(
					GlobalSystemConfigurationEnum.RSM_WITHDRAWAL_ADMIN_FEE.getCode()).getValue());
		}
		return BigDecimal.ZERO;
	}

	private void validateRequest(CreatePointWithdrawalRequest request, CurrentUserIdDTO currentUser) {
		if (!EkycStatus.SUCCESS.equals(currentUser.getEkycVerificationStatus())) {
			throw new BusinessException(ErrorCodeGlobalEnum.EKYC_NOT_SUCCESS_YET);
		}

		if (!PaymentAccountStatus.APPROVED.equals(currentUser.getPaymentInfoStatus())) {
			throw new BusinessException(ErrorCodeGlobalEnum.PAYMENT_ACCOUNT_NOT_APPROVED_YET);
		}

		BigDecimal currentTotalPoint = referralIntegrationService.getTotalAvailablePoint(currentUser.getId());
		GlobalSystemConfigurationDTO minPointWithdrawal = proxyFeignClient.getCommonFeignClient()
				.getIntegrationGlobalSystemConfigurationByCode(
						GlobalSystemConfigurationEnum.RSM_WITHDRAWAL_MIN_POINT.name());
		if (currentTotalPoint.compareTo(new BigDecimal(minPointWithdrawal.getValue())) < 0) {
			throw new BusinessException(
					ErrorCodeGlobalEnum.BALANCE_NOT_REACH_MINIMUM_REQUIREMENT_FOR_WITHDRAWAL_PROCESS);
		}

		if (currentTotalPoint.subtract(request.getRequestedAmount()).compareTo(BigDecimal.ZERO) < 0) {
			throw new BusinessException(ErrorCodeGlobalEnum.POINT_BALANCE_NOT_ENOUGH);
		}

	}

	@Override
	public boolean isWithdrawalProcessExist(String userId) {
		return pointWithdrawalService.isInProgressProcessExist(userId);
	}

	@Override
	public Page<PointWithdrawalResponse> getAllPointWithdrawal(Pageable pageable,
			PointWithdrawalSearchRequest request) {
		Page<PointWithdrawal> pointWithdrawals = pointWithdrawalService.findAllPointWithdrawal(pageable, request);
		Map<String, UserFullNameResponse> refNoUserFullNameResponseMap = fetchUserNames(
				pointWithdrawals.stream().map(PointWithdrawal::getLastModifiedBy).collect(Collectors.toList()));

		return pointWithdrawals
				.map(pointWithdrawal -> mapToPointWithdrawalResponse(pointWithdrawal, refNoUserFullNameResponseMap));
	}

	@Override
	public List<PointWithdrawalResponse> getAllPointWithdrawal(PointWithdrawalSearchRequest request) {
		List<PointWithdrawal> pointWithdrawals = pointWithdrawalService.findAllPointWithdrawal(request);
		Map<String, UserFullNameResponse> refNoUserFullNameResponseMap = fetchUserNames(
				pointWithdrawals.stream().map(PointWithdrawal::getLastModifiedBy).collect(Collectors.toList()));

		return pointWithdrawals.stream()
				.map(pointWithdrawal -> mapToPointWithdrawalResponse(pointWithdrawal, refNoUserFullNameResponseMap))
				.collect(Collectors.toList());
	}

	@Override
	public List<PointWithdrawalResponse> getAllPointWithdrawalBiz(PointWithdrawalSearchRequest request) {
		List<PointWithdrawal> pointWithdrawals = pointWithdrawalService.findAllPointWithdrawal(request);

		return pointWithdrawals.stream().map(MapStructConverter.MAPPER::toPointWithdrawalResponse)
				.collect(Collectors.toList());
	}

	@Override
	public List<PointWithdrawalResponse> getAllPointWithdrawalInRefNo(@NonNull List<String> refNo) {
		List<PointWithdrawal> pointWithdrawals = pointWithdrawalService.findByRefNo(refNo);

		return pointWithdrawals.stream().map(MapStructConverter.MAPPER::toPointWithdrawalResponse)
				.collect(Collectors.toList());
	}

	@Override
	public PointWithdrawal getPointWithdrawalByRefNo(String refNo) {
		return pointWithdrawalService.findByRefNo(refNo);
	}

	@Override
	public void updateWithdrawalStatusByRefNoAndSendPushNotification(String refNo,
			UpdateWithdrawalStatusRequest updateWithdrawalStatusRequest) {
		if (!List.of(PointWithdrawalStatus.DISBURSED, PointWithdrawalStatus.FAILED)
				.contains(updateWithdrawalStatusRequest.getStatus())) {
			throw new BusinessException(ErrorCodeGlobalEnum.NOT_VALID_STATUS_FOR_WITHDRAWAL_UPDATE);
		}

		if (PointWithdrawalStatus.FAILED.equals(updateWithdrawalStatusRequest.getStatus())
				&& (StringUtils.isBlank(updateWithdrawalStatusRequest.getReason()))) {
			throw new BusinessException(ErrorCodeGlobalEnum.MISSING_REASON_FOR_FAILED_WITHDRAWAL);
		}

		PointWithdrawal pointWithdrawal = pointWithdrawalService.findByRefNo(refNo);

		if (!PointWithdrawalStatus.PENDING.equals(pointWithdrawal.getStatus())) {
			throw new BusinessException(ErrorCodeGlobalEnum.CAN_ONLY_UPDATE_STATUS_FOR_PENDING_WITHDRAWAL);
		}

		pointWithdrawal.setStatus(updateWithdrawalStatusRequest.getStatus());
		if (PointWithdrawalStatus.DISBURSED.equals(updateWithdrawalStatusRequest.getStatus())) {
			pointWithdrawal.setCashDisbursementDate(Instant.now());
			calculateRealtimeClosingBalanceForBiz(pointWithdrawal);
		}
		else if (PointWithdrawalStatus.FAILED.equals(updateWithdrawalStatusRequest.getStatus())) {
			pointWithdrawal.setFailureReason(updateWithdrawalStatusRequest.getReason());
		}

		pointWithdrawal = pointWithdrawalService.save(pointWithdrawal);
		if (PointWithdrawalStatus.DISBURSED.equals(updateWithdrawalStatusRequest.getStatus())) {
			List<PointTransaction> pointTransactions = this.populateWithdrawalTransactions(pointWithdrawal);
			List<PointTransaction> withdrawalTransactions = pointTransactionService.saveAll(pointTransactions);

			processPointAllocation(withdrawalTransactions);

		}
		sendWithdrawalStatusNotification(pointWithdrawal.getUserId(), updateWithdrawalStatusRequest.getStatus());
	}

	private void calculateRealtimeClosingBalanceForBiz(PointWithdrawal pointWithdrawal) {
		if (SourceSystemEnum.MXBIZ.equals(pointWithdrawal.getSource())) {
			closingBalanceIntgService.calculateRealTimeClosingBalance(null, pointWithdrawal.getReceivableAmount());
		}
	}

	@Override
	public void bulkUpdateWithdrawalStatusByRefNoAndSendPushNotification(
			BulkUpdateWithdrawalStatusRequest bulkUpdateWithdrawalStatusRequest) {
		if (!List.of(PointWithdrawalStatus.DISBURSED, PointWithdrawalStatus.FAILED)
				.contains(bulkUpdateWithdrawalStatusRequest.getStatus())) {
			throw new BusinessException(ErrorCodeGlobalEnum.NOT_VALID_STATUS_FOR_WITHDRAWAL_UPDATE);
		}

		if (PointWithdrawalStatus.FAILED.equals(bulkUpdateWithdrawalStatusRequest.getStatus())
				&& (StringUtils.isBlank(bulkUpdateWithdrawalStatusRequest.getReason()))) {
			throw new BusinessException(ErrorCodeGlobalEnum.MISSING_REASON_FOR_FAILED_WITHDRAWAL);
		}

		List<PointWithdrawal> pointWithdrawalList = pointWithdrawalService
				.findByRefNo(bulkUpdateWithdrawalStatusRequest.getApplicationIds());

		for (PointWithdrawal pointWithdrawal : pointWithdrawalList) {
			if (!PointWithdrawalStatus.PENDING.equals(pointWithdrawal.getStatus())) {
				throw new BusinessException(ErrorCodeGlobalEnum.CAN_ONLY_UPDATE_STATUS_FOR_PENDING_WITHDRAWAL);
			}
		}

		Map<String, PointWithdrawal> pointWithdrawalMap = pointWithdrawalList.stream()
				.collect(Collectors.toMap(PointWithdrawal::getRefNo, pointWithdrawal -> pointWithdrawal));
		for (String refNo : bulkUpdateWithdrawalStatusRequest.getApplicationIds()) {
			PointWithdrawal pointWithdrawal = pointWithdrawalMap.get(refNo);

			if (Objects.isNull(pointWithdrawal)) {
				continue;
			}

			pointWithdrawal.setStatus(bulkUpdateWithdrawalStatusRequest.getStatus());

			if (PointWithdrawalStatus.DISBURSED.equals(bulkUpdateWithdrawalStatusRequest.getStatus())) {
				pointWithdrawal.setCashDisbursementDate(Instant.now());
				calculateRealtimeClosingBalanceForBiz(pointWithdrawal);
			}
			else if (PointWithdrawalStatus.FAILED.equals(bulkUpdateWithdrawalStatusRequest.getStatus())) {
				pointWithdrawal.setFailureReason(bulkUpdateWithdrawalStatusRequest.getReason());
			}

			pointWithdrawal = pointWithdrawalService.save(pointWithdrawal);
			if (PointWithdrawalStatus.DISBURSED.equals(bulkUpdateWithdrawalStatusRequest.getStatus())) {
				List<PointTransaction> pointTransactions = this.populateWithdrawalTransactions(pointWithdrawal);
				List<PointTransaction> withdrawalTransactions = pointTransactionService.saveAll(pointTransactions);

				processPointAllocation(withdrawalTransactions);

			}
			sendWithdrawalStatusNotification(pointWithdrawal.getUserId(),
					bulkUpdateWithdrawalStatusRequest.getStatus());
		}
	}

	@Override
	public void validateWithdrawal(@NonNull String institutionId, @NonNull BigDecimal amount) {
		List<String> bizUserIds = bizAccountClient.findUsersByInstitutionId(institutionId).stream()
				.map(BizUserDTO::getId).toList();
		BigDecimal currentTotalPoint = referralIntegrationService.getTotalAvailablePoint(bizUserIds);
		BigDecimal minPointWithdrawal = new BigDecimal(
				proxyFeignClient.getCommonFeignClient().getIntegrationGlobalSystemConfigurationByCode(
						GlobalSystemConfigurationEnum.RSM_WITHDRAWAL_MIN_POINT.name()).getValue());
		BigDecimal adminFee = this.getAdminFee();

		minPointWithdrawal = minPointWithdrawal.add(adminFee);

		if (currentTotalPoint.compareTo(minPointWithdrawal) < 0) {
			throw new BusinessException(
					ErrorCodeGlobalEnum.BALANCE_NOT_REACH_MINIMUM_REQUIREMENT_FOR_WITHDRAWAL_PROCESS);
		}

		BigDecimal withdrawalAmount = amount.add(adminFee);
		if (currentTotalPoint.compareTo(withdrawalAmount) < 0) {
			throw new BusinessException(ErrorCodeGlobalEnum.POINT_BALANCE_NOT_ENOUGH);
		}
	}

	@Override
	public boolean hasPendingWithdrawalForUserIdIn(@NonNull Collection<String> userIds) {
		return pointWithdrawalService.countByUserIdInAndStatusIn(userIds, Set.of(PointWithdrawalStatus.PENDING)) > 0;
	}

	@Override
	public void processBizPointWithdrawal(@NonNull CreateBizPointWithdrawalRequest request) {
		String transactionId = request.getTransactionId();
		this.validateWithdrawal(request.getInstitutionId(), request.getRequestedAmount());

		redisService.executeSingleProcess(String.format(CacheKey.RSM_WITHDRAWAL_TRANSACTION_CACHE_KEY, transactionId),
				transactionId, () -> {
					PointWithdrawal pointWithdrawal = MapStructConverter.MAPPER.toPointWithdrawal(request);
					pointWithdrawal.setSource(SourceSystemEnum.MXBIZ);
					pointWithdrawal.setRefNo(runningNumberUtil
							.getLatestRunningNumberWithoutDate(RunningNumberModule.RSM_BIZ_WITHDRAWAL_ID.name()));

					pointWithdrawalService.save(pointWithdrawal);
					return "success";
				}, ErrorCodeGlobalEnum.FAILED_TO_PROCESS_WITHDRAWAL, TimeUnit.HOURS);
	}

	private PointWithdrawal buildPointWithdrawal(CreatePointWithdrawalRequest request, CurrentUserIdDTO userDTO,
			PaymentAccountDetailResponse paymentAccountDetail, BigDecimal withdrawalAdminFee) {

		PointWithdrawal pointWithdrawal = MapStructConverter.MAPPER.toPointWithdrawal(request);
		pointWithdrawal.setRefNo(
				runningNumberUtil.getLatestRunningNumberWithoutDate(RunningNumberModule.RSM_WITHDRAWAL_ID.name()));
		pointWithdrawal.setUserId(userDTO.getId());
		pointWithdrawal.setUserRefNo(userDTO.getRefNo());
		pointWithdrawal.setFullName(userDTO.getFullName());
		pointWithdrawal.setEmail(userDTO.getEmail());
		pointWithdrawal.setStatus(PointWithdrawalStatus.PENDING);
		pointWithdrawal.setSource(SourceSystemEnum.MXAPP);
		pointWithdrawal.setBankName(paymentAccountDetail.getBankName());
		pointWithdrawal.setAccountHolderName(paymentAccountDetail.getAccountHolderName());
		pointWithdrawal.setBankAccountNo(paymentAccountDetail.getAccountNumber());
		pointWithdrawal.setPaymentInfoStatus(paymentAccountDetail.getStatus());
		pointWithdrawal.setAdminFee(withdrawalAdminFee);
		pointWithdrawal.setReceivableAmount(request.getRequestedAmount().subtract(withdrawalAdminFee));

		return pointWithdrawal;
	}

	private List<PointTransaction> populateWithdrawalTransactions(PointWithdrawal pointWithdrawal) {
		List<PointTransaction> pointTransactionList = new ArrayList<>();
		ChartOfAccount chartOfAccount = chartOfAccountService
				.findByAccountCode(ChartOfAccountType.RSM_POINT_WITHDRAW.getCode());
		pointTransactionList
				.add(createPointTransaction(pointWithdrawal, PointTransactionType.CASH_WITHDRAWAL, chartOfAccount));
		if (featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.RSM_WITHDRAWAL_ADMIN_FEE_ENABLED)) {
			pointTransactionList.add(
					createPointTransaction(pointWithdrawal, PointTransactionType.ADMIN_FEE_WITHDRAWAL, chartOfAccount));
		}
		return pointTransactionList;
	}

	private PointTransaction createPointTransaction(PointWithdrawal pointWithdrawal, PointTransactionType type,
			ChartOfAccount chartOfAccount) {
		PointTransaction pointTransaction = MapStructConverter.MAPPER.toPointTransaction(pointWithdrawal);
		pointTransaction.setStatus(PointTransactionStatus.SUCCESS);
		pointTransaction.setPointAmount(PointTransactionType.ADMIN_FEE_WITHDRAWAL.equals(type)
				? pointWithdrawal.getAdminFee() : pointWithdrawal.getReceivableAmount());
		pointTransaction.setRefNo(pointWithdrawal.getRefNo());
		pointTransaction.setType(type);
		pointTransaction.setName(type.getName());
		pointTransaction.setChartOfAccount(chartOfAccount);
		pointTransaction.setApprovedDate(pointWithdrawal.getCashDisbursementDate());
		return pointTransaction;
	}

	private void sendWithdrawalStatusNotification(String userId, PointWithdrawalStatus status) {
		PushNotificationDataRequest notificationRequest = PushNotificationDataRequest.builder().userId(userId)
				.code(PointWithdrawalStatus.DISBURSED.equals(status)
						? MessageTemplateCodeEnum.POINTS_WITHDRAWAL_STATUS_SUCCESS
						: MessageTemplateCodeEnum.POINTS_WITHDRAWAL_STATUS_FAILED)
				.model(new HashMap<>()).channel(ChannelCodeEnum.APPLICATION).saveToInbox(true)
				.isRedirectToModuleFromInbox(true).build();
		kafkaTemplate.send(KafkaTopic.SEND_NOTIFICATION, jsonUtil.convertToString(notificationRequest));
	}

	private PointWithdrawalResponse fillNullValues(PointWithdrawalResponse response) {
		response.setRefNo(defaultIfNull(response.getRefNo()));
		response.setApplicationId(defaultIfNull(response.getRefNo()));
		response.setUserRefNo(defaultIfNull(response.getUserRefNo()));
		response.setFullName(defaultIfNull(response.getFullName()));
		response.setEmail(defaultIfNull(response.getEmail()));
		response.setBankName(defaultIfNull(response.getBankName()));
		response.setAccountHolderName(defaultIfNull(response.getAccountHolderName()));
		response.setBankAccountNo(defaultIfNull(response.getBankAccountNo()));
		response.setPaymentInfoStatus(defaultIfNull(response.getPaymentInfoStatus()));
		response.setStatus(defaultIfNull(response.getStatus()));
		response.setFailureReason(defaultIfNull(response.getFailureReason()));
		response.setLastModifiedBy(defaultIfNull(response.getLastModifiedBy()));
		return response;
	}

	private String defaultIfNull(String value) {
		return StringUtils.isNotBlank(value) ? value : "-";
	}

	private Map<String, UserFullNameResponse> fetchUserNames(List<String> lastModifiedByRefNos) {
		if (lastModifiedByRefNos.isEmpty()) {
			return Collections.emptyMap();
		}

		List<UserFullNameResponse> userFullNameResponseList = proxyFeignClient.getAccountFeignClient()
				.getUserFullNameByUserRefNo(new UserRefNoListRequest(lastModifiedByRefNos));

		return userFullNameResponseList.stream()
				.collect(Collectors.toMap(UserFullNameResponse::getRefNo, Function.identity(), (a, b) -> a));
	}

	private PointWithdrawalResponse mapToPointWithdrawalResponse(PointWithdrawal pointWithdrawal,
			Map<String, UserFullNameResponse> refNoUserFullNameResponseMap) {
		PointWithdrawalResponse response = MapStructConverter.MAPPER.toPointWithdrawalResponse(pointWithdrawal);

		response.setLastModifiedBy(refNoUserFullNameResponseMap.get(pointWithdrawal.getLastModifiedBy()) != null
				? refNoUserFullNameResponseMap.get(pointWithdrawal.getLastModifiedBy()).getFullName() : null);
		return fillNullValues(response);
	}

	private void processPointAllocation(List<PointTransaction> withdrawalTransactions) {

		for (PointTransaction transaction : withdrawalTransactions) {
			if (isDebitTransaction(transaction.getType())) {
				pointAllocationIntgService.processPointAllocation(transaction.getUserId(), transaction.getPointAmount(),
						transaction.getId());
			}
		}

	}

	private boolean isDebitTransaction(PointTransactionType type) {
		return PointTransactionType.CASH_WITHDRAWAL.equals(type)
				|| PointTransactionType.ADMIN_FEE_WITHDRAWAL.equals(type);
	}

}