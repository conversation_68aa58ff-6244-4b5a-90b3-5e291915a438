package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.BizAccountClient;
import my.com.mandrill.component.client.BizProductClient;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.constant.RSMReferralCodeStatus;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.PointEarning;
import my.com.mandrill.component.domain.ReferralCode;
import my.com.mandrill.component.domain.Referre;
import my.com.mandrill.component.dto.model.BizUserDTO;
import my.com.mandrill.component.dto.model.PointEarningData;
import my.com.mandrill.component.dto.model.RefereeEarningData;
import my.com.mandrill.component.dto.projection.BizPointEarningProjection;
import my.com.mandrill.component.dto.projection.RefereeEarningProjection;
import my.com.mandrill.component.dto.projection.TotalPointEarningProjection;
import my.com.mandrill.component.dto.request.CreateReferralCodeRequest;
import my.com.mandrill.component.dto.request.UpdateCustomerReferralRequest;
import my.com.mandrill.component.dto.request.UpdateReferralCodeRequest;
import my.com.mandrill.component.dto.response.BizReferralSummaryResponse;
import my.com.mandrill.component.dto.response.ReferralCodeResponse;
import my.com.mandrill.component.dto.response.ReferralSummaryResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.RSMRelationUtil;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.request.FindRelationTypeRequest;
import my.com.mandrill.utilities.feign.dto.request.PushNotificationDataRequest;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.feign.dto.request.UserIdListRequest;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import my.com.mandrill.utilities.general.util.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ReferralIntegrationServiceImpl implements ReferralIntegrationService {

	private final ReferreService referreService;

	private final ReferralCodeService referralCodeService;

	private final PointEarningService pointEarningService;

	private final ProxyFeignClient proxyFeignClient;

	private final BizAccountClient bizAccountClient;

	private final BizProductClient bizProductClient;

	private final AccountFeignClient accountFeignClient;

	private final FeatureFlagOutbound featureFlagOutbound;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final RunningNumberUtil runningNumberUtil;

	private final JSONUtil jsonUtil;

	private final PointWithdrawalService pointWithdrawalService;

	// TODO: Move this to RSM GlobalConfig
	private static final double POINTS_TO_MONEY_RATE = 0.01;

	private final PointTransactionService pointTransactionService;

	@Override
	public ReferralSummaryResponse getSummary(String userId) {
		Map<String, BigDecimal> totalEarningMap = this.getUserEarning(List.of(userId));

		int sizeCode = featureFlagOutbound.getIntegerValue(GlobalSystemConfigurationEnum.REFERRAL_CODE_SIZE);
		ReferralCode referralCode = referralCodeService.findOrCreate(userId, SourceSystemEnum.MXAPP, sizeCode);

		Pageable pageable = PageRequest.of(0, 5, Sort.by("id").descending());
		Page<PointEarning> earnings = pointEarningService.getPageableByUser(userId, SourceSystemEnum.MXAPP,
				PointEarningStatus.AWARDED, pageable);

		List<RefereeEarningData> referredFriends = this.getFriendEarningData(userId);
		return ReferralSummaryResponse.builder().totalFriends(referredFriends.size())
				.totalEarnings(totalEarningMap.getOrDefault(userId, BigDecimal.ZERO).longValue())
				.earnings(withRefereeAsName(earnings, PointEarning::getFullName).getContent())
				.referralCode(referralCode.getCode()).referredFriends(referredFriends).build();
	}

	private Page<PointEarningData> withRefereeAsName(Page<PointEarning> earnings,
			Function<PointEarning, String> nameGetter) {
		List<String> applicationIds = earnings.stream().map(PointEarning::getApplicationId).toList();
		Map<String, String> pointEarningMap = new HashMap<>();
		if (!applicationIds.isEmpty()) {
			List<PointEarning> pointEarnings = pointEarningService.findByApplicantIdInAndFocalType(applicationIds,
					RSMFocalType.REFEREE);
			pointEarningMap.putAll(pointEarnings.stream()
					.collect(Collectors.toMap(PointEarning::getApplicationId, nameGetter, (v2, v1) -> v1)));
		}
		return earnings.map(pointEarning -> {
			var data = MapStructConverter.MAPPER.toPointEarningData(pointEarning);
			data.setFullName(MaskingUtil
					.maskAllButFirstLetter(pointEarningMap.getOrDefault(pointEarning.getApplicationId(), "")));
			return data;
		});
	}

	@Override
	public BizReferralSummaryResponse getBizSummary(@NonNull String institutionId, @NonNull SourceSystemEnum source,
			Boolean shouldCheckReferralCode) {
		if (Boolean.FALSE.equals(shouldCheckReferralCode) && !hasValidBizReferralCodeStatus(institutionId)) {
			return BizReferralSummaryResponse.builder().amount(0).points(0).totalReferred(0).build();
		}

		List<String> userIds = bizAccountClient.findUsersByInstitutionId(institutionId).stream().map(BizUserDTO::getId)
				.toList();
		List<Referre> referees = referreService.findRefereesByUserIdIn(userIds, source);
		RSMRelationType scenario = switch (source) {
			case MXAPP -> RSMRelationType.B2C;
			case MXBIZ -> RSMRelationType.B2B;
		};

		Map<String, BigDecimal> totalEarningMap = this.getBizUserEarning(userIds, scenario);
		long points = totalEarningMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add).longValue();

		return BizReferralSummaryResponse.builder().totalReferred(referees.size()).points(points)
				.amount(points * POINTS_TO_MONEY_RATE).build();
	}

	private Map<String, BigDecimal> getUserEarning(List<String> userIds) {
		List<TotalPointEarningProjection> totalEarning = pointEarningService.getTotalPointEarning(userIds);
		return totalEarning.stream().collect(
				Collectors.toMap(TotalPointEarningProjection::getUserId, TotalPointEarningProjection::getTotal));
	}

	private Map<String, BigDecimal> getBizUserEarning(List<String> userIds, RSMRelationType scenario) {
		if (userIds.isEmpty()) {
			return Map.of();
		}

		// FIXME: This is urgent. Couldn't get this working using an IN clause, so we're
		// doing this manually as per Andy's suggestion.
		List<TotalPointEarningProjection> totalB2B2CEarnings = pointEarningService.getTotalBizPointEarning(userIds,
				RSMRelationType.B2B2C);
		List<TotalPointEarningProjection> totalEarning = pointEarningService.getTotalBizPointEarning(userIds, scenario);

		Map<String, BigDecimal> earnings = new HashMap<>(totalB2B2CEarnings.stream().collect(
				Collectors.toMap(TotalPointEarningProjection::getUserId, TotalPointEarningProjection::getTotal)));
		for (TotalPointEarningProjection earning : totalEarning) {
			BigDecimal amount = ObjectUtils.defaultIfNull(earnings.get(earning.getUserId()), BigDecimal.ZERO);
			amount = amount.add(earning.getTotal());

			earnings.put(earning.getUserId(), amount);
		}

		return earnings;
	}

	private Map<String, String> getUserFullNameMap(List<String> userIds) {
		return this.getAppUserList(userIds).stream()
				.collect(Collectors.toMap(UserFullNameResponse::getId, UserFullNameResponse::getFullName));
	}

	private Map<String, UserFullNameResponse> getAppUserMap(List<String> userIds) {
		List<UserFullNameResponse> fullNameResponses = getAppUserListIncludeDeleted(userIds);
		return fullNameResponses.stream().collect(Collectors.toMap(UserFullNameResponse::getId, Function.identity()));
	}

	private List<UserFullNameResponse> getAppUserListIncludeDeleted(List<String> userIds) {
		UserIdListRequest userIdListRequest = new UserIdListRequest();
		userIdListRequest.setUserId(userIds);
		userIdListRequest.setIncludeDeleted(true);
		return proxyFeignClient.getAccountFeignClient().getUserFullNameByUserId(userIdListRequest);
	}

	private List<UserFullNameResponse> getAppUserList(List<String> userIds) {
		UserIdListRequest userIdListRequest = new UserIdListRequest();
		userIdListRequest.setUserId(userIds);
		return proxyFeignClient.getAccountFeignClient().getUserFullNameByUserId(userIdListRequest);
	}

	private Map<String, BizUserDTO> getBizUsers(List<String> userIds) {
		return bizAccountClient.findUsersByListOfIds(userIds, true).stream()
				.collect(Collectors.toMap(BizUserDTO::getId, bizUserDTO -> bizUserDTO));
	}

	@Override
	public Page<PointEarningData> getEarningHistory(@NonNull String userId, SourceSystemEnum source,
			@NonNull Pageable pageable) {
		Page<PointEarning> earnings = pointEarningService.getPageableByUser(userId, source, PointEarningStatus.AWARDED,
				pageable);

		return withRefereeAsName(earnings, PointEarning::getFullName);
	}

	@Override
	public Page<PointEarningData> getBizEarningHistory(@NonNull String institutionId, @NonNull SourceSystemEnum source,
			@NonNull Pageable pageable) {
		if (!hasValidBizReferralCodeStatus(institutionId)) {
			return Page.empty(pageable);
		}

		List<String> userIds = bizAccountClient.findUsersByInstitutionId(institutionId).stream().map(BizUserDTO::getId)
				.toList();

		List<RSMRelationType> scenarios = switch (source) {
			case MXAPP -> List.of(RSMRelationType.B2C, RSMRelationType.B2B2C);
			case MXBIZ -> List.of(RSMRelationType.B2B, RSMRelationType.B2B2C);
		};

		List<BizPointEarningProjection> earningsIncludingSelf = pointEarningService
				.getPageableForUserIdIn(pageable, userIds, source, PointEarningStatus.AWARDED, scenarios).getContent();
		List<String> applicationIds = this.findApplicationIdsExcludingUserIdIn(earningsIncludingSelf, userIds);

		Page<BizPointEarningProjection> earnings = pointEarningService.getPageableForUserIdIn(pageable, userIds,
				applicationIds, source, PointEarningStatus.AWARDED, scenarios);
		List<String> refereeIds = earnings.stream().map(BizPointEarningProjection::getUserId).toList();

		Map<String, BizUserDTO> userMap = refereeIds.isEmpty() ? Map.of() : this.getBizUsers(refereeIds);
		Map<String, UserFullNameResponse> appFullNameMap = refereeIds.isEmpty() ? Map.of()
				: this.getAppUserMap(refereeIds);

		return earnings.map(e -> {
			String fullName = this.mapToBizUserOrCompanyFullName(e, userMap, appFullNameMap, source,
					BizPointEarningProjection::getUserId, BizPointEarningProjection::getFullName,
					BizPointEarningProjection::getCompanyName);
			return PointEarningData.builder().fullName(fullName).point(e.getPoints().longValue())
					.confirmedOn(e.getConfirmedOn()).build();
		});
	}

	private <E> String mapToBizUserOrCompanyFullName(E data, Map<String, BizUserDTO> bizUserMap,
			Map<String, UserFullNameResponse> appUserMap, SourceSystemEnum source, Function<E, String> userIdGetter,
			Function<E, String> fullNameGetter, Function<E, String> companyNameGetter) {
		BizUserDTO user = bizUserMap.get(userIdGetter.apply(data));
		String fullName;

		if (user != null) {
			fullName = switch (source) {
				case MXAPP -> user.isActive() ? user.getFullName() : user.getRefNo();
				case MXBIZ -> {
					// loongyeat: We already filter off null institutions, so this
					// can never be null.
					BizUserDTO.Institution institution = user.getInstitution();

					String refNo = institution.getRefNo();
					String name = institution.getName();

					if (institution.isActive()) {
						// loongyeat: Requested by product.
						yield StringUtils.equals(refNo, name) ? user.getFullName()
								: ObjectUtils.firstNonNull(name, user.getFullName());
					}
					else {
						yield institution.getRefNo();
					}
				}
			};
		}
		else {
			fullName = switch (source) {
				case MXAPP -> Optional.ofNullable(appUserMap.get(userIdGetter.apply(data)))
						.map(appUser -> ObjectUtils.firstNonNull(appUser.getFullName(), appUser.getRefNo()))
						.orElse(fullNameGetter.apply(data));
				case MXBIZ -> companyNameGetter.apply(data);
			};
		}

		return MaskingUtil.maskAllButFirstLetter(fullName);
	}

	private List<String> findApplicationIdsExcludingUserIdIn(List<BizPointEarningProjection> earningsIncludingSelf,
			List<String> userIds) {
		Set<String> userIdSet = new HashSet<>(userIds);
		Map<ApplicationType, Set<String>> typeToApplicationIdMapping = earningsIncludingSelf.stream()
				.collect(Collectors.groupingBy(BizPointEarningProjection::getApplicationType)).entrySet().stream()
				.collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().stream()
						.map(BizPointEarningProjection::getApplicationId).collect(Collectors.toSet())));

		List<String> allApplicationIds = new ArrayList<>();

		for (Map.Entry<ApplicationType, Set<String>> entry : typeToApplicationIdMapping.entrySet()) {
			ApplicationType type = entry.getKey();
			Set<String> applicationIds = entry.getValue();

			Set<String> applicationIdsWithoutSelf = switch (type) {
				case LEAD_GEN, REDIRECT -> proxyFeignClient.getBankFeignClient()
						.findAllApplicationIdsExcludingUserIdsIn(applicationIds, userIdSet);
				case AFFILIATE -> proxyFeignClient.getAnalyticFeignClient()
						.findAllApplicationIdsExcludingUserIdsIn(applicationIds, userIdSet);
				case PROPERTY -> proxyFeignClient.getPropertyFeignClient()
						.findAllApplicationIdsExcludingUserIdsIn(applicationIds, userIdSet);
				case LEAD_GENERATION_FORM ->
					bizProductClient.findAllApplicationCodesExcludingUserIdsIn(applicationIds, userIdSet);
			};

			allApplicationIds.addAll(applicationIdsWithoutSelf);
		}

		return allApplicationIds;
	}

	@Override
	public Page<RefereeEarningData> getBizTopReferees(@NonNull String institutionId, @NonNull SourceSystemEnum source,
			@NonNull Pageable pageable) {
		if (!hasValidBizReferralCodeStatus(institutionId)) {
			return Page.empty(pageable);
		}

		List<String> userIds = bizAccountClient.findUsersByInstitutionId(institutionId, true).stream()
				.map(BizUserDTO::getId).toList();

		List<String> allReferredIds = referreService.findRefereesByUserIdIn(userIds, source).stream()
				.map(Referre::getReferredId).toList();
		List<String> referredIdsForFilter = getBizReferredIdsForFilter(allReferredIds, source);

		Page<RefereeEarningProjection> referees = referreService.findTopBizRefereesByUserIdIn(pageable, userIds,
				referredIdsForFilter, source);
		List<String> refereeIds = referees.map(RefereeEarningProjection::getUserId).toList();

		Map<String, BizUserDTO> userMap = refereeIds.isEmpty() ? Map.of() : this.getBizUsers(refereeIds);
		Map<String, UserFullNameResponse> appFullNameMap = refereeIds.isEmpty() ? Map.of()
				: this.getAppUserMap(allReferredIds);

		return referees.map(r -> {
			String fullName = this.mapToBizUserOrCompanyFullName(r, userMap, appFullNameMap, source,
					RefereeEarningProjection::getUserId, RefereeEarningProjection::getUserFullName,
					RefereeEarningProjection::getCompanyName);

			BizUserDTO user = userMap.get(r.getUserId());
			LocalDate signUpDate = user != null ? DateUtil.instantToLocalDate(user.getCreatedDate()) : r.getSignUpOn();

			return RefereeEarningData.builder().fullName(fullName).point(r.getPoint()).signUpOn(signUpDate).build();
		});
	}

	private List<String> getBizReferredIdsForFilter(List<String> allReferredIds, SourceSystemEnum source) {
		// fadhlan: for app users no need to filter by institution
		if (SourceSystemEnum.MXAPP.equals(source)) {
			return allReferredIds;
		}
		// fadhlan: for biz users, filter only user with institution
		return CollectionUtils.isEmpty(allReferredIds) ? List.of()
				: bizAccountClient.findUsersByListOfIds(allReferredIds, true).stream()
						.filter(u -> Objects.nonNull(u.getInstitution())).map(BizUserDTO::getId).toList();
	}

	private boolean hasValidBizReferralCodeStatus(@NonNull String institutionId) {
		ReferralCode referralCode = referralCodeService
				.findAllByCompanyIdSetAndSource(Set.of(institutionId), SourceSystemEnum.MXBIZ).stream().findFirst()
				.orElse(null);
		return referralCode != null && RSMReferralCodeStatus.ACTIVE.equals(referralCode.getStatus());
	}

	@Override
	public void updateRefereeFriend(UserRefereeUpdateRequest request) {
		Referre newReferee = createNewFriend(request);
		sendNewRefereeFriendNotification(newReferee);
		sendNewReferredRelation(newReferee);
	}

	private void sendNewReferredRelation(Referre newReferee) {
		kafkaTemplate.send(KafkaTopic.UPDATE_USER_TOPIC,
				jsonUtil.convertToString(UpdateUserKafkaRequest.builder().id(newReferee.getReferredId())
						.rsmRelationType(RSMRelationUtil.findRelationType(newReferee).name()).build()));
	}

	@Override
	public ReferralCodeResponse getReferralCode(String referralCode, SourceSystemEnum source) {
		return MapStructConverter.MAPPER
				.toReferralCodeResponse(referralCodeService.findByReferralCodeAndSource(referralCode, source));
	}

	@Override
	public List<RefereeEarningData> getFriendEarningData(String userId) {
		List<Referre> friends = referreService.findRefereesByUser(userId, SourceSystemEnum.MXAPP);
		Map<String, RefereeEarningData> earningSourceUser = referreService.findRefereeEarningDataByUser(userId).stream()
				.collect(Collectors.toMap(RefereeEarningProjection::getUserId,
						projection -> RefereeEarningData.builder()
								.fullName(MaskingUtil.maskAllButFirstLetter(projection.getUserFullName()))
								.signUpOn(projection.getSignUpOn()).point(projection.getPoint()).build(),
						(v1, v2) -> v1));
		List<Referre> userWithoutPoint = friends.stream()
				.filter(referre -> !earningSourceUser.containsKey(referre.getReferredId())).toList();

		if (!CollectionUtils.isEmpty(userWithoutPoint)) {
			Map<String, String> fullNames = getUserFullNameMap(
					userWithoutPoint.stream().map(Referre::getReferredId).toList());
			for (Referre referre : userWithoutPoint) {
				if (!fullNames.containsKey(referre.getReferredId())) {
					continue;
				}
				earningSourceUser.put(referre.getReferredId(),
						RefereeEarningData.builder()
								.fullName(MaskingUtil.maskAllButFirstLetter(fullNames.get(referre.getReferredId())))
								.signUpOn(DateUtil.instantToLocalDate(referre.getConfirmationDate()))
								.point(BigDecimal.ZERO).build());
			}
		}
		return friends.stream().map(Referre::getReferredId).filter(earningSourceUser::containsKey)
				.map(earningSourceUser::get).toList();
	}

	@Override
	public ReferralCodeResponse createReferralCode(CreateReferralCodeRequest request) {
		ReferralCode referral = MapStructConverter.MAPPER.toReferralCode(request);
		int sizeCode = featureFlagOutbound.getIntegerValue(GlobalSystemConfigurationEnum.REFERRAL_CODE_SIZE);

		referral.setCode(RandomUtil.generateGuaranteedAlphanumeric(sizeCode));
		referral.setReferralRequestId(
				runningNumberUtil.getLatestRunningNumberWithoutDate(RunningNumberModule.REFERRAL_REQUEST_ID.name()));
		referral.setStatus(RSMReferralCodeStatus.PENDING);
		ReferralCode updatedReferralCode = referralCodeService.save(referral);

		return MapStructConverter.MAPPER.toReferralCodeResponse(updatedReferralCode);
	}

	@Override
	public BigDecimal getTotalAvailablePoint(Collection<String> userIds) {
		if (userIds.isEmpty())
			return BigDecimal.ZERO;

		BigDecimal availableBalance = pointTransactionService.getAvailableBalance(userIds);
		BigDecimal pendingOutBalance = pointWithdrawalService.calculatePendingWithdrawal(userIds);
		BigDecimal total = availableBalance.subtract(pendingOutBalance);
		return total.compareTo(BigDecimal.ZERO) < 1 ? BigDecimal.ZERO : total;
	}

	@Override
	public BigDecimal getTotalAvailablePoint(String userId) {
		return this.getTotalAvailablePoint(Set.of(userId));
	}

	@Override
	public Map<String, ReferralCodeResponse> getReferralCodeForUserIn(@NonNull Set<String> userIds) {
		if (userIds.isEmpty())
			return Map.of();

		List<ReferralCode> referrals = referralCodeService.findAllByUserIdIn(userIds);
		return referrals.stream()
				.collect(Collectors.toMap(ReferralCode::getUserId, MapStructConverter.MAPPER::toReferralCodeResponse));
	}

	@Override
	public Map<String, ReferralCodeResponse> getReferralCodeForCompanyIdSet(@NonNull Set<String> companyIds,
			@NonNull SourceSystemEnum source) {
		List<ReferralCode> referrals = referralCodeService.findAllByCompanyIdSetAndSource(companyIds, source);
		return referrals.stream().collect(
				Collectors.toMap(ReferralCode::getCompanyId, MapStructConverter.MAPPER::toReferralCodeResponse));
	}

	@Override
	public Map<String, ReferralCodeResponse> getReferralCodeForCompanyId(@NonNull String institutionId,
			@NonNull SourceSystemEnum source) {

		List<ReferralCode> referrals = referralCodeService.findAllByCompanyIdAndSource(institutionId, source);
		return referrals.stream()
				.collect(Collectors.toMap(ReferralCode::getUserId, MapStructConverter.MAPPER::toReferralCodeResponse));
	}

	@Override
	public ReferralCodeResponse updateReferralStatusForInstitutionId(@NonNull String id,
			@NonNull UpdateCustomerReferralRequest request) {
		return MapStructConverter.MAPPER.toReferralCodeResponse(referralCodeService
				.updateReferralStatusForInstitutionId(id, request.getStatus(), request.getCancellationReason()));
	}

	@Override
	public ReferralCodeResponse updateReferralCodeByCompanyId(@NonNull String companyId,
			@NonNull UpdateReferralCodeRequest request) {
		return MapStructConverter.MAPPER
				.toReferralCodeResponse(referralCodeService.updateReferralCodeByCompanyId(companyId, request));
	}

	private Referre createNewFriend(UserRefereeUpdateRequest request) {
		ReferralCode refereeSource = referralCodeService.findByReferralCodeAndSource(request.getReferrerCode());
		Referre referrerTree = referreService.findByReferredId(refereeSource.getUserId());
		String friendTree;

		// Only allow to extend friend tree for B2B2C scenario
		if (featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.RSM_B2B2C_LINKAGE_ENABLED)
				&& SourceSystemEnum.MXBIZ.equals(refereeSource.getSource())
				&& SourceSystemEnum.MXAPP.equals(request.getSource())) {
			String initialFriendTree = Optional.ofNullable(referrerTree).map(Referre::getFriendTree)
					.orElse(refereeSource.getUserId());
			friendTree = populateFriendTree(initialFriendTree, request.getReferredId());
		}
		else {
			friendTree = populateFriendTree(refereeSource.getUserId(), request.getReferredId());
		}
		Referre newFriendReferer = Referre.builder().referrerId(refereeSource.getUserId())
				.referredId(request.getReferredId()).confirmationDate(request.getSignUpDate()).friendTree(friendTree)
				.referredSource(request.getSource()).referrerSource(refereeSource.getSource()).build();
		return referreService.create(newFriendReferer);
	}

	private void sendNewRefereeFriendNotification(Referre newReferee) {
		PushNotificationDataRequest notificationRequest = PushNotificationDataRequest.builder()
				.userId(newReferee.getReferrerId()).code(MessageTemplateCodeEnum.NEW_REFERRAL_FRIEND_ADDED)
				.model(new HashMap<>()).channel(ChannelCodeEnum.APPLICATION).saveToInbox(true)
				.isRedirectToModuleFromInbox(true).build();
		kafkaTemplate.send(KafkaTopic.SEND_NOTIFICATION, jsonUtil.convertToString(notificationRequest));
	}

	private String populateFriendTree(String friendTree, String newFriend) {
		return friendTree + ";" + newFriend;
	}

	@Override
	public RSMRelationType findRelationType(FindRelationTypeRequest request) {
		Referre referer = referreService.findByReferredId(request.getReferredId());
		return RSMRelationUtil.findRelationType(referer);
	}

	@Override
	public Set<String> getBizCompanyIdsWithReferralApplicationStatus(@NonNull Sort sort, RSMReferralCodeStatus status,
			Instant dateFrom, Instant dateTo) {
		return referralCodeService.findAllCompanyIdsWhereStatusIn(sort, SourceSystemEnum.MXBIZ, status, dateFrom,
				dateTo);
	}

	@Override
	public void updateReferralToExpiredByUserIds(List<String> userIds) {
		referralCodeService.updateExpiredByUserIds(userIds);
	}

	@Override
	public ReferralCodeResponse getReferralCodeOfReferrerByReferredId(String userId) {
		Referre referre = referreService.findByReferredId(userId);
		if (referre == null) {
			return null;
		}
		ReferralCode referralCode = referralCodeService.findByUserId(referre.getReferrerId());
		if (referralCode == null) {
			return null;
		}
		return MapStructConverter.MAPPER.toReferralCodeResponse(referralCode);
	}

	@Override
	public String getReferralCodeByUserId(String userId) {
		Optional<ReferralCode> referralCode = referralCodeService.getReferralCodeByUserId(userId);
		return referralCode.map(ReferralCode::getCode).orElse(null);
	}

	@Override
	public boolean existReferralCode(String referralCode) {
		if (PhoneNumberUtil.isValidPhoneNumber(referralCode)) {
			return accountFeignClient.checkPhoneNumberExists(referralCode);
		}
		return referralCodeService.existsReferralCode(referralCode);
	}

}
