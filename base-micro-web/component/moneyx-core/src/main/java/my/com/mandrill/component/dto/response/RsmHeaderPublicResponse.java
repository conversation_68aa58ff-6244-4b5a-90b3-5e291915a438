package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RsmHeaderPublicResponse {

	private String externalRewardName;

	private RSMFrequencyEnum frequency;

	private Instant startDate;

	private String productName;

	private String productCategoryName;

	private String productProviderName;

	private BigDecimal externalRevenue;

	private String referrerRewards;

	private String refereeRewards;

}
