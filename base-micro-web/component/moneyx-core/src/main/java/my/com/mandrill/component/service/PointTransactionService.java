package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.PointTransaction;
import my.com.mandrill.component.dto.projection.ClosingBalanceProjection;
import my.com.mandrill.component.dto.response.BizPointHistoryResponse;
import my.com.mandrill.component.dto.response.RsmPointTransactionHistoryResponse;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

public interface PointTransactionService {

	void save(PointTransaction pointTransaction);

	List<PointTransaction> saveAll(List<PointTransaction> pointTransactionList);

	ClosingBalanceProjection calculatePointSpentByApprovedDate(LocalDate date, SourceSystemEnum sourceSystemEnum);

	List<RsmPointTransactionHistoryResponse> findTransactionHistory(String userId, String cursor, Integer limit);

	BigDecimal getAvailableBalance(Collection<String> userIds);

	BigDecimal getAvailableBalance(String userId);

	Page<BizPointHistoryResponse> findBizPointHistory(@NonNull String institutionId, @NonNull Pageable pageable);

}
