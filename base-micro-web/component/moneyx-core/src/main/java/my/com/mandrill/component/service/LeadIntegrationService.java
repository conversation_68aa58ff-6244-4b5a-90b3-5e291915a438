package my.com.mandrill.component.service;

import jakarta.validation.Valid;
import my.com.mandrill.component.dto.model.AttachCommissionBizRequest;
import my.com.mandrill.component.dto.model.DisburseCommissionBizRequest;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.BizRSMCommissionListingResponse;
import my.com.mandrill.component.dto.response.LeadCommissionApplicationResponse;
import my.com.mandrill.component.dto.response.LeadCommissionResponse;
import my.com.mandrill.component.dto.response.LeadDetailResponse;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface LeadIntegrationService {

	LeadDetailResponse getLeadDetail(ApplicationType applicationType, String applicationId);

	List<LeadCommissionResponse> getAttachedCommissions(String applicationId);

	Map<String, LeadCommissionApplicationResponse> getLeadCommissionsInApplication(Set<String> applicationIds);

	Page<UserInterestRecordRSMPaginationDTO> getLeads(Pageable pageable, RSMLeadRequest request);

	List<BizRSMCommissionListingResponse> getCommissions(String commissionId, String productId,
			String productProviderId);

	void attachCommission(AttachCommissionRequest request);

	void attachCommissionBiz(AttachCommissionBizRequest request);

	void disburseCommission(DisburseCommissionRequest request);

	void disburseCommissionBiz(DisburseCommissionBizRequest request);

	void deleteCommission(DeleteCommissionRequest request);

	void deleteCommission(BulkDeleteCommissionRequest request);

	void updateStatus(UpdateRSMInfoRequest request);

	void deleteDraftCommission(String applicationId);

	void bulkUpdateStatus(@Valid BulkUpdateRsmInfoRequest request);

}
