package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "point_allocation")
public class PointAllocation extends AuditSectionTimeSeries {

	@Size(max = 36)
	@Column(name = "origin_tx_id", length = 36)
	private String originTxId;

	@Size(max = 36)
	@Column(name = "dest_tx_id", length = 36)
	private String destTxId;

	@Column(name = "points_allocated")
	private BigDecimal pointsAllocated;

}
