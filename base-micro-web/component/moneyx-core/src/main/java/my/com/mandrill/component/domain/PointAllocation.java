package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "point_allocation")
public class PointAllocation extends AuditSectionTimeSeries {

	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "origin_tx_id")
	private PointTransaction originTransaction;

	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "destination_tx_id")
	private PointTransaction destinationTransaction;

	@Column(name = "point_allocated")
	private BigDecimal pointAllocated;

}
