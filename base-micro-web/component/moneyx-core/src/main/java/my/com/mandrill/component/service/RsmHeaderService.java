package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.projection.BizRSMHeaderWithDetailProjection;
import my.com.mandrill.component.dto.request.RSMHeaderSearchRequest;
import my.com.mandrill.component.dto.response.BizRSMCommissionListingResponse;
import my.com.mandrill.component.dto.response.RsmHeaderPublicResponse;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface RsmHeaderService {

	RsmHeader findById(String id);

	@Transactional
	void save(RsmHeader header);

	@Transactional
	void create(RsmHeader header, List<RsmDetail> details);

	Page<RsmHeader> findAll(RSMHeaderSearchRequest request, Pageable pageable);

	// Biz
	Page<BizRSMCommissionListingResponse> getPaginatedCommissionListing(@NonNull Pageable pageable, String globalSearch,
			String productCategoryId, String partnerId, String productId, Boolean status);

	List<BizRSMHeaderWithDetailProjection> getCommissionListWithDetail(String globalSearch, String productCategoryId,
			String partnerId, String productId, Boolean status);

	List<RsmHeader> findAll(RSMHeaderSearchRequest request);

	List<BizRSMCommissionListingResponse> findCommissions(String commissionId, String productId,
			String productProviderId);

	Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source);

	List<String> findProductTypeIdsWithActiveCommission(@NonNull SourceSystemEnum source);

	Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source, String productTypeName);

	Set<String> findAllProviderIdsWithActiveCommission(@NonNull SourceSystemEnum source,
			Collection<String> productTypeNames);

	List<RsmHeaderPublicResponse> getExternalRsmHeaderList();

}
