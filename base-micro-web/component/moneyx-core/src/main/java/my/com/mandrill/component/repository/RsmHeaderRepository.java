package my.com.mandrill.component.repository;

import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.projection.BizRSMHeaderListingProjection;
import my.com.mandrill.component.dto.projection.BizRSMHeaderWithDetailProjection;
import my.com.mandrill.component.dto.response.RsmHeaderPublicResponse;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface RsmHeaderRepository extends JpaRepository<RsmHeader, String>, JpaSpecificationExecutor<RsmHeader> {

	@Query("""
			select
				rh.id as id,
				rh.commissionId as commissionId,
				rh.productProviderId as productProviderId,
				rh.productId as productId,
				rh.rewardName as rewardName,
				rh.commissionType as commissionType,
				rh.createdDate as createdDate,
				rh.isActive as active
			from RsmHeader rh
			where (
				:idOrRewardName is null
				or rh.commissionId = :idOrRewardName
				or lower(rh.rewardName) like lower(concat('%', :idOrRewardName, '%'))
			)
			and rh.source = :source
			and (
				:productCategoryId is null
				or rh.productCategoryId = :productCategoryId
			)
			and (
				:partnerId is null
				or rh.productProviderId = :partnerId
			)
			and (
				:productId is null
				or rh.productId = :productId
			)
			and (
				:status is null
				or rh.isActive = :status
			)
			""")
	Page<BizRSMHeaderListingProjection> findProjectedBySource(@NonNull Pageable pageable,
			@NonNull SourceSystemEnum source, String idOrRewardName, String productCategoryId, String partnerId,
			String productId, Boolean status);

	@Query("""
			select
				rh.id as id,
				rh.commissionId as commissionId,
				rh.productProviderId as productProviderId,
				rh.productId as productId,
				rh.rewardName as rewardName,
				rh.commissionType as commissionType,
				rh.createdDate as createdDate,
				rh.isActive as active
			from RsmHeader rh
			where (
				:idOrRewardName is null
				or rh.commissionId = :idOrRewardName
				or lower(rh.rewardName) like lower(concat('%', :idOrRewardName, '%'))
			)
			and rh.source = :source
			and
			(
				:productId is null
				or rh.productId = :productId
			)
			and
			(
				:productProviderId is null
				or rh.productProviderId = :productProviderId
			)
			and rh.isActive = true
			""")
	List<BizRSMHeaderListingProjection> findProjectedLeadManagement(String idOrRewardName, String productId,
			String productProviderId, @NonNull SourceSystemEnum source);

	@Query("""
			select rh
			from RsmHeader rh
			left join fetch rh.details
			where (
				:idOrRewardName is null
				or rh.commissionId = :idOrRewardName
				or lower(rh.rewardName) like lower(concat('%', :idOrRewardName, '%'))
			)
			and rh.source = :source
			and (
				:productCategoryId is null
				or rh.productCategoryId = :productCategoryId
			)
			and (
				:partnerId is null
				or rh.productProviderId = :partnerId
			)
			and (
				:productId is null
				or rh.productId = :productId
			)
			and (
				:status is null
				or rh.isActive = :status
			)
			""")
	List<BizRSMHeaderWithDetailProjection> findProjectedWithDetail(@NonNull SourceSystemEnum source,
			String idOrRewardName, String productCategoryId, String partnerId, String productId, Boolean status);

	boolean existsByProductIdAndRewardNameIgnoreCase(String productId, String rewardName);

	@Query("SELECT CASE WHEN COUNT(r) > 0 THEN true ELSE false END FROM RsmHeader r "
			+ "WHERE r.commissionType = :commissionType " + "AND r.productCategoryId = :productCategoryId "
			+ "AND r.productTypeId = :productTypeId " + "AND r.productProviderId = :productProviderId "
			+ "AND r.productId = :productId " + "AND LOWER(r.rewardName) = LOWER(:rewardName) "
			+ "AND r.remarks = :remarks")
	boolean existsByCommissionTypeAndProductAndRewardName(RSMCommissionType commissionType, String productCategoryId,
			String productTypeId, String productProviderId, String productId, String rewardName, String remarks);

	@Query("""
			SELECT DISTINCT r.productId
			FROM RsmHeader r
			WHERE r.source = :source and r.isActive = TRUE
			""")
	Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source);

	@Query("""
			SELECT DISTINCT r.productTypeId
			FROM RsmHeader r
			WHERE r.source = :source and r.isActive = TRUE
			""")
	List<String> findProductTypeIdsWithActiveCommission(@NonNull SourceSystemEnum source);

	@Query("""
			SELECT DISTINCT r.productId
			FROM RsmHeader r
			WHERE r.source = :source and r.isActive = TRUE
			AND r.productTypeName = :productTypeName
			""")
	Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source, String productTypeName);

	@Query("""
			SELECT DISTINCT r.productProviderId
			FROM RsmHeader r
			WHERE r.source = :source and r.isActive = TRUE
			AND r.productTypeName in (:productTypeNames)
			""")
	Set<String> findAllProviderIdsWithActiveCommission(@NonNull SourceSystemEnum source,
			Collection<String> productTypeNames);

	@Query("""
			SELECT new my.com.mandrill.component.dto.response.RsmHeaderPublicResponse(
			    h.externalRewardName,
			    h.frequency,
			    h.startDate,
			    h.productName,
			    h.productCategoryName,
			    h.productProviderName,
			    h.externalRevenue,
			    MAX(CASE WHEN d.focalType = my.com.mandrill.component.constant.RSMFocalType.REFERRER_LEVEL_1 THEN d.externalB2bC2cNonReferralRevenueDistribution ELSE null END),
			    MAX(CASE WHEN d.focalType = my.com.mandrill.component.constant.RSMFocalType.REFEREE THEN d.externalB2bC2cNonReferralRevenueDistribution ELSE null END)
			)
			FROM RsmHeader h
			JOIN h.details d
			WHERE h.status = my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum.LIVE
			AND NOT EXISTS (
			    SELECT 1 FROM RsmHeader subh
			    WHERE subh.status = my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum.LIVE
			      AND subh.productId = h.productId
			      AND subh.productProviderId = h.productProviderId
			      AND subh.productCategoryId = h.productCategoryId
			      AND subh.productTypeId = h.productTypeId
			      AND subh.commissionType = h.commissionType
			      AND subh.createdDate > h.createdDate
			)
			GROUP BY h
			""")
	List<RsmHeaderPublicResponse> findAllExternalRsmHeader();

}
