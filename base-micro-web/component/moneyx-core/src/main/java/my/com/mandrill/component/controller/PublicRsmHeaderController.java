package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.response.RsmHeaderPublicResponse;
import my.com.mandrill.component.service.ReferralIntegrationService;
import my.com.mandrill.component.service.RsmHeaderIntgService;
import my.com.mandrill.component.service.RsmHeaderService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@PublicAuth
@RestController
@SecurityRequirements
@RequiredArgsConstructor
@RequestMapping("/v1/public/rsm-commission-header")
public class PublicRsmHeaderController {

	private final RsmHeaderService rsmHeaderService;

	@GetMapping
	public ResponseEntity<List<RsmHeaderPublicResponse>> getExternalRsmRewards() {
		return ResponseEntity.ok(rsmHeaderService.getExternalRsmHeaderList());
	}

}
