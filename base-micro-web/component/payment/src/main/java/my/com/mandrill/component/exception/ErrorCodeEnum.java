package my.com.mandrill.component.exception;

import lombok.Getter;
import my.com.mandrill.utilities.general.exception.ExceptionEnum;

@Getter
public enum ErrorCodeEnum implements ExceptionEnum {

	WRONG_AMOUNT_FORMAT("PYM0001", "Incorrect amount format entered. (Show user the correct format)"),
	TRANSACTION_EXPIRED("PYM0002", "Transaction expired."), INVALID_REFUND_STATUS("PYM0003", "Refund status invalid"),
	TRANSACTION_CANT_BE_REFUNDED("PYM0004", "Refund can't be processed. Contact <EMAIL>"),
	FPX_TRANSACTION_CANT_BE_REFUNDED("PYM0005", "Refund can't be processed. Contact <EMAIL>"),
	MERCHANT_EXISTED("PYM0006", "Duplicate Merchant"), OPERATOR_EXIST("PYM0007", "Duplicate Operator"),
	INCORRECT_PAYMENT_STATUS("PYM0008", "Incorrect payment status"),
	INCORRECT_PAYMENT_AMOUNT("PYM0009", "Payment amount not correct"),
	INVALID_PHONE_NUMBER("PYM0010", "The mobile number does not match the telco provider selected. Please try again"),
	PHONE_NUMBER_VALIDATION_FAILED("PYM0011", "Phone Number Validation Failed. Contact <EMAIL>");

	private final String code;

	private final String description;

	ErrorCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

}