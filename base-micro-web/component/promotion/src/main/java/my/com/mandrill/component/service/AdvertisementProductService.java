package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.domain.AdvertisementPredefinedProduct;
import my.com.mandrill.utilities.general.constant.AdvertisementStatus;

import java.util.List;

public interface AdvertisementProductService {

	List<AdvertisementPredefinedProduct> findByAdvertisementOrderBySequenceAsc(Advertisement advertisement);

	List<AdvertisementPredefinedProduct> findByProductId(String productId);

}
