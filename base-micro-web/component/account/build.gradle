buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("io.spring.javaformat:spring-javaformat-gradle-plugin:0.0.35")
    }
}

plugins {
    id 'org.springframework.boot' version "${spring_boot_version}"
    id 'io.spring.dependency-management' version "${spring_dependency_version}"
    id 'java'
    id 'jacoco'
    id 'org.sonarqube' version '4.3.1.3277' // Plugin SonarQube
}

jacocoTestReport {
    reports {
        xml.required = true
    }

    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                    "my/com/**/config/*",
                    "my/com/**/constant/*",
                    "my/com/**/domain/*",
                    "my/com/**/dto/*",
                    "my/com/mandrill/AccountComponentApplication.java",
                    "my.com.mandrill.component.aspect.AspectLogging.java"
            ])
        }))
    }
}

apply plugin: 'io.spring.javaformat'

group "${project_group}"
version "${project_version}"

sourceCompatibility = "${java_compatibility_version}"
targetCompatibility = "${java_compatibility_version}"

dependencies {
    implementation project(':utilities:general')
    implementation project(':utilities:feign-client')
    implementation project(':utilities:file-storage')
    implementation project(':utilities:ciphers')
    implementation project(':utilities:core')
    testImplementation project(':utilities:core')
    implementation files('../../libs/crypto-0.0.1-plain.jar') // use local lib

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-data-jpa"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "org.springframework.boot:spring-boot-starter-oauth2-client"
    implementation "org.springframework.boot:spring-boot-starter-data-elasticsearch"
    implementation "org.springframework.kafka:spring-kafka"

    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    implementation "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:${spring_cloud_version}"
    implementation "org.springframework.cloud:spring-cloud-starter-openfeign:${spring_cloud_version}"
    implementation "com.google.cloud:spring-cloud-gcp-starter-bigquery:${spring_cloud_version}"

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    compileOnly "org.projectlombok:lombok:${lombok_version}"
    annotationProcessor "org.projectlombok:lombok:${lombok_version}"

    implementation "org.mapstruct:mapstruct:${mapstruct_version}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstruct_version}"

    implementation "org.liquibase:liquibase-core:${liquibase_version}"

    implementation "org.springdoc:springdoc-openapi-starter-webmvc-api:${springdoc_version}"

    implementation "org.apache.commons:commons-lang3:${apache_commons_lang3_version}"
    implementation "org.apache.commons:commons-collections4:${apache_commons_collection_version}"

    implementation "com.fasterxml.jackson.datatype:jackson-datatype-hibernate6:${jackson_datatype_hibernate6}"

    implementation "io.micrometer:micrometer-registry-prometheus:${micrometer_version}"

    implementation "org.apache.commons:commons-csv:${apache_commons_csv_version}"

    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-csv'

    implementation 'org.apache.poi:poi:5.4.0'
    implementation 'org.apache.poi:poi-ooxml:5.4.0'
    implementation 'com.google.cloud:google-cloud-recaptchaenterprise:3.59.0'

    testImplementation "org.springframework.boot:spring-boot-starter-test"


    testImplementation group: 'org.springframework.security', name: 'spring-security-test', version: '6.0.2'
    testImplementation testFixtures(project(':utilities:core'))
    testImplementation "org.mockito:mockito-inline:${mockito_inline_version}"
    testImplementation "org.projectlombok:lombok:${lombok_version}"
    testAnnotationProcessor "org.projectlombok:lombok:${lombok_version}"
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    testImplementation "org.wiremock:wiremock-standalone:3.9.2"
    testImplementation "org.springframework.kafka:spring-kafka-test:3.0.2"
    testImplementation "org.liquibase:liquibase-core:${liquibase_version}"
    testImplementation "net.lbruun.springboot:preliquibase-spring-boot-starter:1.3.0"
    testImplementation "org.awaitility:awaitility:4.2.2"


}

test {
    useJUnitPlatform()
}

processResources {
    filesMatching('**/application.yml') {
        filter {
            it.replace('#project.version#', version)
        }
    }
}

compileJava.dependsOn processResources

tasks.register('wrapper', Wrapper) {
    gradleVersion = '7.4.1'
}

jacoco {
    toolVersion = "0.8.10"
}

tasks.named('test') {
    finalizedBy 'jacocoTestReport'
}

tasks.named('jacocoTestReport') {
    dependsOn 'test'
    reports {
        xml.required = true
        csv.required = false
        html.required = true
    }
}

sonarqube {
    properties {
        property "sonar.projectKey", "moneyx-micro-account"
        property "sonar.projectName", "moneyx-micro-account"
        property "sonar.host.url", "http://localhost:9000"
        property "sonar.login", "squ_3b749821776f3fcfa0e7c009f141f14e7b0f4b2d" // Ganti dengan token kamu
        property "sonar.java.binaries", "$buildDir/classes/java/main"
        property "sonar.tests", "src/test/java"
        property "sonar.test.inclusions", "**/*IT.java, **/unit/*Test.java"
        property "sonar.exclusions", "**/dto/**, **/domain/**, **/config/**, **/constant/**,  **/AccountComponentApplication.java, **/AspectLogging.java"
        property "sonar.jacoco.reportPaths", "$buildDir/jacoco/test.exec"
        property "sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/jacoco/test/jacocoTestReport.xml"
    }
}