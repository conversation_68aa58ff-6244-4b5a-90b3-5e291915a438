package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DashboardActivity;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.DashboardActivityRepository;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.utilities.feign.dto.UpdateUserByFinologyDTO;
import my.com.mandrill.utilities.feign.dto.UpdateUserIdentityNumberByFinologyDTO;
import my.com.mandrill.utilities.general.constant.DashboardCategory;
import my.com.mandrill.utilities.general.constant.DashboardType;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.SchedulerMessaging;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class AccountConsumerIT extends BaseIntegrationTest {

	private static final String OTP_ID = "d145b3bd-b409-42e1-bd30-1a4f695ec19c";

	private static final String USER_ID = "e8c9da6e-3d32-4ce9-9351-e69756c1f3cc";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private UserKeyRequestRepository userKeyRequestRepository;

	@Autowired
	private DashboardActivityRepository dashboardActivityRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@Autowired
	private AppUserService appUserService;

	@BeforeAll
	void setup() {
		kafkaTemplate.setDefaultTopic(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC);
		kafkaTemplate.setDefaultTopic(KafkaTopicConfig.KEY_EXPIRATION);
		kafkaTemplate.setDefaultTopic(KafkaTopicConfig.DELETE_ACCOUNT);
		kafkaTemplate.setDefaultTopic(KafkaTopicConfig.DASHBOARD_ACTIVITY);
	}

	// @AfterEach
	// void tearDown() {
	// userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
	// cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	// }

	@Test
	void keyExpiration_success() throws ExecutionException, InterruptedException {
		SchedulerMessaging message = SchedulerMessaging.builder().key(KafkaTopicConfig.KEY_EXPIRATION)
				.destination(KafkaTopicConfig.KEY_EXPIRATION).timeJobTriggered(Instant.now()).build();
		kafkaTemplate
				.send(KafkaTopicConfig.KEY_EXPIRATION, UUID.randomUUID().toString(), jsonUtil.convertToString(message))
				.get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<UserKeyRequest> userKeyRequestOptional = userKeyRequestRepository.findById(OTP_ID);
			Assertions.assertTrue(userKeyRequestOptional.isPresent());
			Assertions.assertEquals(RequestKeyStatus.EXPIRED, userKeyRequestOptional.get().getStatus());
		});

		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void keyExpiration_notYetExpired_success() {
		UserKeyRequest userKeyRequest = userKeyRequestRepository.findById(OTP_ID).orElse(null);
		Assertions.assertNotNull(userKeyRequest);
		userKeyRequest.setInitialDate(Instant.now().plus(Duration.ofHours(1)));
		userKeyRequestRepository.save(userKeyRequest);

		SchedulerMessaging message = SchedulerMessaging.builder().key(KafkaTopicConfig.KEY_EXPIRATION)
				.destination(KafkaTopicConfig.KEY_EXPIRATION).timeJobTriggered(Instant.now()).build();
		kafkaTemplate.send(KafkaTopicConfig.KEY_EXPIRATION, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message));

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<UserKeyRequest> userKeyRequestOptional = userKeyRequestRepository.findById(OTP_ID);
			Assertions.assertTrue(userKeyRequestOptional.isPresent());
			Assertions.assertEquals(RequestKeyStatus.PENDING, userKeyRequestOptional.get().getStatus());
		});

		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void dashboardActivity_success() throws ExecutionException, InterruptedException {
		DashboardActivity message = DashboardActivity.builder().category(DashboardCategory.MODULE_RECORDS)
				.type(DashboardType.BANKS).value(1L).createdDate(Instant.now()).build();
		kafkaTemplate.send(KafkaTopicConfig.DASHBOARD_ACTIVITY, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();
		;
		kafkaTemplate.flush();
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			List<DashboardActivity> dashboardActivity = dashboardActivityRepository.findAll();
			Assertions.assertFalse(dashboardActivity.isEmpty());
			dashboardActivityRepository.deleteAll();
		});

		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void deleteAccount_success() throws ExecutionException, InterruptedException {
		SchedulerMessaging message = SchedulerMessaging.builder().key(KafkaTopicConfig.DELETE_ACCOUNT)
				.destination(KafkaTopicConfig.DELETE_ACCOUNT).timeJobTriggered(Instant.now()).build();
		kafkaTemplate
				.send(KafkaTopicConfig.DELETE_ACCOUNT, UUID.randomUUID().toString(), jsonUtil.convertToString(message))
				.get();
		final String deletedId = "4d12a9b0-5f29-4d41-9bfe-faa3ebdca7cd";
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(deletedId).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertTrue(user.getDeleted());
			Assertions.assertNull(user.getFullName());
			Assertions.assertNull(user.getEmail());
			Assertions.assertNull(user.getPhoneNumber());
			Assertions.assertNull(user.getPhoneCountry());
			Assertions.assertNull(user.getNric());
			Assertions.assertTrue(user.getIsNationalityEditable());
			Assertions.assertNull(user.getAddress1());
			Assertions.assertNull(user.getAddress2());
			Assertions.assertNull(user.getAddress3());
			dashboardActivityRepository.deleteAll();
		});
		userRepository.findById(deletedId).ifPresent(userRepository::delete);
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_byFinology_success() throws ExecutionException, InterruptedException {
		UpdateUserByFinologyDTO message = UpdateUserByFinologyDTO.builder().id(USER_ID)
				.email("finologyupdate@localhost").address(Arrays.asList("address 1", "address 2", "address 3"))
				.dob("1990-01-01").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertEquals(message.getEmail(), user.getEmail());
			Assertions.assertEquals(message.getAddress().get(0), user.getAddress1());
			Assertions.assertEquals(message.getAddress().get(1), user.getAddress2());
			Assertions.assertEquals(message.getAddress().get(2), user.getAddress3());
			int currentAge = LocalDate.now().getYear() - 1990;
			Assertions.assertEquals(currentAge, user.getAge());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_byFinology_noEmailAndAddress_success() throws ExecutionException, InterruptedException {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setEmail("finologiaccount@localhost");
		user.setAddress1("address 1");
		userRepository.save(user);

		UpdateUserByFinologyDTO message = UpdateUserByFinologyDTO.builder().id(USER_ID).dob("1990-01-01").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User updatedUser = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertEquals(user.getEmail(), updatedUser.getEmail());
			Assertions.assertEquals(user.getAddress1(), updatedUser.getAddress1());
			Assertions.assertNull(updatedUser.getAddress2());
			Assertions.assertNull(updatedUser.getAddress3());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_byFinology_existEmail_success() throws ExecutionException, InterruptedException {
		UpdateUserByFinologyDTO message = UpdateUserByFinologyDTO.builder().id(USER_ID).email("deniar@localhost")
				.address(Arrays.asList("address 1", "address 2", "address 3")).dob("1990-01-01").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertNull(user.getEmail());
			Assertions.assertEquals(message.getAddress().get(0), user.getAddress1());
			Assertions.assertEquals(message.getAddress().get(1), user.getAddress2());
			Assertions.assertEquals(message.getAddress().get(2), user.getAddress3());
			int currentAge = LocalDate.now().getYear() - 1990;
			Assertions.assertEquals(currentAge, user.getAge());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_nric_success() throws ExecutionException, InterruptedException {
		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("IC").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertEquals(message.getValue(), user.getNric());
			Assertions.assertNull(user.getPassport());
			Assertions.assertNull(user.getArmy());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_nricNotNull_success() throws ExecutionException, InterruptedException {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setNric("12345");
		user = userRepository.save(user);

		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("IC").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();
		;
		kafkaTemplate.flush();
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User updatedUser = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(updatedUser);
			Assertions.assertEquals(message.getValue(), updatedUser.getNric());
			Assertions.assertNull(updatedUser.getPassport());
			Assertions.assertNull(updatedUser.getArmy());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_passport_success() throws ExecutionException, InterruptedException {
		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("PASSPORT").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();
		kafkaTemplate.flush();

		Awaitility.await().atMost(30, TimeUnit.SECONDS).pollInterval(2000, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			AppUser appUser = appUserService.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(appUser);
			Assertions.assertEquals(message.getValue(), appUser.getPassport());
			Assertions.assertNull(appUser.getNric());
			Assertions.assertNull(appUser.getArmy());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_passportNotNull_success() throws ExecutionException, InterruptedException {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setPassport("12345");
		user = userRepository.save(user);

		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("PASSPORT").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();
		;

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User updatedUser = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(updatedUser);
			Assertions.assertEquals(message.getValue(), updatedUser.getPassport());
			Assertions.assertNull(updatedUser.getNric());
			Assertions.assertNull(updatedUser.getArmy());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_army_success() throws ExecutionException, InterruptedException {
		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("ARMY").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertEquals(message.getValue(), user.getArmy());
			Assertions.assertNull(user.getNric());
			Assertions.assertNull(user.getPassport());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_armyNotNull_success() throws ExecutionException, InterruptedException {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setArmy("12345");
		user = userRepository.save(user);

		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("ARMY").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User updatedUser = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(updatedUser);
			Assertions.assertEquals(message.getValue(), updatedUser.getArmy());
			Assertions.assertNull(updatedUser.getNric());
			Assertions.assertNull(updatedUser.getPassport());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_noSupportType_success() throws ExecutionException, InterruptedException {
		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.identityType("OTHER").value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertNull(user.getArmy());
			Assertions.assertNull(user.getNric());
			Assertions.assertNull(user.getPassport());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void updateAccount_nullIdentityType_success() throws ExecutionException, InterruptedException {
		UpdateUserIdentityNumberByFinologyDTO message = UpdateUserIdentityNumberByFinologyDTO.builder().id(USER_ID)
				.value("12345").build();
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, UUID.randomUUID().toString(),
				jsonUtil.convertToString(message)).get();
		kafkaTemplate.flush();
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			User user = userRepository.findById(USER_ID).orElse(null);
			Assertions.assertNotNull(user);
			Assertions.assertNull(user.getArmy());
			Assertions.assertNull(user.getNric());
			Assertions.assertNull(user.getPassport());
		});
		userKeyRequestRepository.findById(OTP_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

}
