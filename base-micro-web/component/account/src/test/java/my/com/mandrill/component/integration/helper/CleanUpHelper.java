package my.com.mandrill.component.integration.helper;

import my.com.mandrill.component.repository.jpa.AppUserRepository;
import my.com.mandrill.component.repository.jpa.EpfContributionRepository;
import my.com.mandrill.component.repository.jpa.ExpenseRepository;
import my.com.mandrill.component.repository.jpa.IncomeRepository;
import my.com.mandrill.component.repository.jpa.TokenRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Component
public class CleanUpHelper {

	@Autowired
	private ExpenseRepository expenseRepository;

	@Autowired
	private IncomeRepository incomeRepository;

	@Autowired
	private EpfContributionRepository epfContributionRepository;

	@Autowired
	private AppUserRepository appUserRepository;

	@Autowired
	private TokenRepository tokenRepository;

	@Transactional
	public void cleanUpAccountIT(String userId) {
		incomeRepository.deleteByUserIds(Collections.singletonList(userId));
		expenseRepository.deleteByUserIds(Collections.singletonList(userId));
		epfContributionRepository.deleteByUser(userId);
		appUserRepository.deleteById(userId);
	}

	@Transactional
	public void cleanUpIncomeGroupIT(List<String> userIds) {
		incomeRepository.deleteByUserIds(userIds);
		appUserRepository.deleteAllById(userIds);
	}

	@Transactional
	public void cleanUpUser(List<String> userIds) {
		appUserRepository.deleteAllById(userIds);
	}

	@Transactional
	public void cleanBusinessRuleIT(List<String> userIds) {
		incomeRepository.deleteByUserIds(userIds);
		expenseRepository.deleteByUserIds(userIds);
		appUserRepository.deleteAllById(userIds);
	}

	@Transactional
	public void clearExpenseIT(List<String> userIds) {
		expenseRepository.deleteByUserIds(userIds);
		appUserRepository.deleteAllById(userIds);
	}

	@Transactional
	public void clearIncomeIT(List<String> userIds) {
		incomeRepository.deleteByUserIds(userIds);
		appUserRepository.deleteAllById(userIds);
	}

	@Transactional
	public void cleanUpRefreshTokenIT(List<String> userIds) {
		tokenRepository.deleteByUserIds(userIds);
		appUserRepository.deleteAllById(userIds);
	}

}
