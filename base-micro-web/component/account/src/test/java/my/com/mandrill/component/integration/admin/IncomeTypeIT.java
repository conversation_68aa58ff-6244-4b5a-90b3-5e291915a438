package my.com.mandrill.component.integration.admin;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.IncomeType;
import my.com.mandrill.component.dto.model.IncomeTypeDTO;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.IncomeTypeRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

public class IncomeTypeIT extends BaseIntegrationTest {

	private static final String EXIST_ID = "1885829c-188b-420b-b2b8-b416f2582a32";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private IncomeTypeRepository incomeTypeRepository;

	@AfterEach
	public void tearDown() {
		incomeTypeRepository.findById(EXIST_ID).ifPresent(incomeTypeRepository::delete);
	}

	@Test
	void create_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(post("/admin/income-types").content(getRequest())
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());

		IncomeTypeDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(getClass().getSimpleName(), response.getName());
		Assertions.assertEquals(getClass().getSimpleName(), response.getDescription());
		Assertions.assertEquals(getClass().getSimpleName().toUpperCase(), response.getCode());

		incomeTypeRepository.deleteById(response.getId());
	}

	@Test
	void create_existName_success() throws Exception {
		mockMvc.perform(post("/admin/income-types").content(getRequest())
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.INCOME_TYPE_CODE_EXISTS.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.INCOME_TYPE_CODE_EXISTS.getDescription()));

	}

	@Test
	void pagination_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/income-types").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Page<IncomeTypeDTO> responsePage = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(responsePage);

		String expected = "EXISTINCOMETYPEIT";
		IncomeTypeDTO response = responsePage.getContent().stream()
				.filter(businessNatureDTO -> expected.equals(businessNatureDTO.getCode())).findFirst().orElse(null);
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals("Exist IncomeTypeIT", response.getName());
		Assertions.assertEquals("Exist IncomeTypeIT", response.getDescription());
		Assertions.assertEquals(expected, response.getCode());
	}

	@Test
	void pagination_withFilter_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/income-types").queryParam("name", "Exist IncomeTypeIT")
						.queryParam("code", "INCOMETYPEIT").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Page<IncomeTypeDTO> responsePage = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(responsePage);

		String expected = "EXISTINCOMETYPEIT";
		IncomeTypeDTO response = responsePage.getContent().stream()
				.filter(businessNatureDTO -> expected.equals(businessNatureDTO.getCode())).findFirst().orElse(null);
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals("Exist IncomeTypeIT", response.getName());
		Assertions.assertEquals("Exist IncomeTypeIT", response.getDescription());
		Assertions.assertEquals(expected, response.getCode());
	}

	@Test
	void findById_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/admin/income-types/" + EXIST_ID)
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		IncomeTypeDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});

		String expected = "EXISTINCOMETYPEIT";
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals("Exist IncomeTypeIT", response.getName());
		Assertions.assertEquals("Exist IncomeTypeIT", response.getDescription());
		Assertions.assertEquals(expected, response.getCode());
	}

	@Test
	void delete_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(delete("/admin/income-types/" + EXIST_ID)
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		IncomeType incomeType = incomeTypeRepository.findById(EXIST_ID).orElse(null);
		Assertions.assertNull(incomeType);
	}

	@Test
	void delete_idUsed_error() throws Exception {
		mockMvc.perform(delete("/admin/income-types/241fd082-51b3-444f-8e51-0feebe6e01bb")
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.DELETE_DEPENDENCY_FAIL.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.DELETE_DEPENDENCY_FAIL.getDescription()));
	}

	@Test
	void update_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(put("/admin/income-types/" + EXIST_ID).content(getRequest())
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());

		IncomeTypeDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(getClass().getSimpleName(), response.getName());
		Assertions.assertEquals(getClass().getSimpleName(), response.getDescription());
		Assertions.assertEquals("UPDATEINCOMETYPEIT", response.getCode());

		incomeTypeRepository.deleteById(response.getId());
	}

	@Test
	void update_notUpdateCode_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(put("/admin/income-types/" + EXIST_ID).content(getRequest())
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());

		IncomeTypeDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(getClass().getSimpleName(), response.getName());
		Assertions.assertEquals(getClass().getSimpleName(), response.getDescription());
		Assertions.assertEquals("EXISTINCOMETYPEIT", response.getCode());

		incomeTypeRepository.deleteById(response.getId());
	}

}
