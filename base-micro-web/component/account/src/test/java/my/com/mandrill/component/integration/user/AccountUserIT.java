package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.PermissionDTO;
import my.com.mandrill.component.dto.model.PublicAuthenticationDTO;
import my.com.mandrill.component.dto.model.TermConditionAndPrivacyPolicyDTO;
import my.com.mandrill.component.dto.model.UserDTO;
import my.com.mandrill.component.dto.request.SignUpRequest;
import my.com.mandrill.component.dto.request.SmsOtpRequest;
import my.com.mandrill.component.dto.request.UpdateMobileRequest;
import my.com.mandrill.component.dto.request.UpdatePasswordResetMobileRequest;
import my.com.mandrill.component.dto.request.WhatsAppOtpRequest;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.*;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.feign.dto.request.EmailRequest;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.HashUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.*;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

/**
 * TODO: need to find a way for validate kafka sending message
 */
@Slf4j
public class AccountUserIT extends BaseIntegrationTest {

	private static final String USER_ID = "9ff423b7-a039-4781-a7f8-dc72d0bc8a18";

	private static final String USER_PASSWORD = "arianto@123";

	private static final String USER_PHONE_NUMBER = "+************";

	private static final String KEY_REQUEST = "9ed64441-61e8-45e1-ab47-a104bf0653ad";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserKeyRequestRepository userKeyRequestRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private BaseProperties baseProperties;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private DeleteAccountMessageRepository deleteAccountMessageRepository;

	@Autowired
	private PublicAuthenticationRepository publicAuthenticationRepository;

	@Autowired
	private PasswordTransactionRepository passwordTransactionRepository;

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private TokenRepository tokenRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@Autowired
	private ExpenseRepository expenseRepository;

	@Autowired
	private DeviceBindingRepository deviceBindingRepository;

	@Autowired
	private EpfContributionRepository epfContributionRepository;

	@Autowired
	private RedisService redisService;

	@MockBean
	private KafkaSender kafkaSender;

	@AfterEach
	public void cleanup() {
		userKeyRequestRepository.findById(KEY_REQUEST).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpAccountIT(USER_ID);
	}

	@Test
	void requestOtp_passwordReset_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "***********", "***********", "OTP");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "***********", "***********", "OTP");
	}

	@Test
	void requestOtp_pinReset_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "***********", "***********", "OTP");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "***********", "***********", "OTP");
	}

	@Test
	void requestOtp_verificationSubmitUserInterested_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "accountusercustomit@localhost",
				"accountusercustomit@localhost", "OTP_VERIFICATION");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "accountusercustomit@localhost",
				"accountusercustomit@localhost", "OTP_VERIFICATION");
	}

	@Test
	void requestOtp_verifiedUserInterestRedirectSubmit_success() throws Exception {
		System.out.println("timestampt:" + hashKeyGenerator.getInstant().toString());
		createMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "***********", "***********", "OTP");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "***********", "***********", "OTP");
	}

	@Test
	void requestOtp_preRegister_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "***********", "***********", "OTP");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "***********", "***********", "OTP");
	}

	@Test
	void requestOtp_withoutReferralCode_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "************", "************", "OTP");
		redisService.deleteFromHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION,
				GlobalSystemConfigurationEnum.RECAPTCHA_ENABLED.getCode());
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "************", "************", "OTP");
	}

	@Test
	void requestOtp_usingReferralCode_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "************", "************", "OTP");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "************", "************", "OTP");
	}

	@Test
	void requestOtp_usingReferralCode_email_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "deniar@localhost", "deniar@localhost",
				"OTP_VERIFICATION");
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		this.validateSuccessOtpRequest(mvcResult);
		verifyMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "deniar@localhost", "deniar@localhost",
				"OTP_VERIFICATION");
	}

	@Test
	void requestOtp_existOtp_success() throws Exception {
		System.out.println("timestampt:" + hashKeyGenerator.getInstant().toString());
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
						.header("identifier", hashKeyGenerator.getIdentifier())
						.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		KeyResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(response.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], response.getKey());
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));
		Assertions.assertEquals(1, expected.getAttempt());
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void requestOtp_existOtp_reachLimit_error() throws Exception {
		mockMvc.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.REQUEST_OTP_LIMIT.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.REQUEST_OTP_LIMIT.getDescription()));

		UserKeyRequest expected = userKeyRequestRepository.findById("4801dd35-bb81-4216-816b-162243b96aa6")
				.orElse(null);
		Assertions.assertNotNull(expected);
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void requestOtp_verifyRecaptchaFailed() throws Exception {
		mockMvc.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value("GLB0032"));
	}

	@Test
	void requestOtp_verifyRecaptchaFailed_successFalse_error() throws Exception {
		mockMvc.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value("GLB0032"));
	}

	@Test
	void requestOtp_verifyRecaptchaFailed_actionDifferent_error() throws Exception {
		mockMvc.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value("GLB0032"));
	}

	@SneakyThrows
	private void validateSuccessOtpRequest(MvcResult mvcResult) {
		KeyResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(response.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], response.getKey());
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void requestOtp_invalidReferralCode_error() throws Exception {
		mockMvc.perform(post("/account/request-otp").header("hash", hashKeyGenerator.getHash())
				.header("identifier", hashKeyGenerator.getIdentifier())
				.header("timestamp", hashKeyGenerator.getInstant().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getDescription()));
	}

	@Test
	void register_withoutReferralCode_success() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		SignUpRequest signUpRequest = jsonUtil.convertValueFromJson(request, new TypeReference<>() {
		});
		User result = userRepository.findAll().stream().filter(user -> signUpRequest.getEmail().equals(user.getEmail()))
				.findFirst().get();

		Assertions.assertNotNull(result);
		Assertions.assertEquals(signUpRequest.getEmail(), result.getEmail());
		Assertions.assertEquals(signUpRequest.getPhoneCountry(), result.getPhoneCountry());
		Assertions.assertEquals(signUpRequest.getPhoneNumber(), result.getPhoneNumber());
		Assertions.assertEquals(result.getUsername(), result.getRefNo());
		Assertions.assertEquals(signUpRequest.getAge(), result.getAge());
		Assertions.assertEquals(signUpRequest.getFullName(), result.getFullName());
		Assertions.assertEquals(LoginTypeEnum.USER, result.getLoginType());

		UserKeyRequest userKey = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(signUpRequest.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(userKey);
		Assertions.assertNotNull(userKey.getCompletionDate());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKey.getStatus());
		userKeyRequestRepository.delete(userKey);
	}

	@Test
	void register_emailExist_error() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.EMAIL_EXISTED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.EMAIL_EXISTED.getDescription()));

	}

	@Test
	void register_invalidOtp_error() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));

	}

	@Test
	void register_withDeviceBindingEnabled_success() throws Exception {
		String deviceId = "register_withDeviceBindingEnabled_success";
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		SignUpRequest signUpRequest = jsonUtil.convertValueFromJson(request, new TypeReference<>() {
		});
		User result = userRepository.findAll().stream().filter(user -> signUpRequest.getEmail().equals(user.getEmail()))
				.findFirst().get();

		Assertions.assertNotNull(result);
		Assertions.assertEquals(signUpRequest.getEmail(), result.getEmail());
		Assertions.assertEquals(signUpRequest.getPhoneCountry(), result.getPhoneCountry());
		Assertions.assertEquals(signUpRequest.getPhoneNumber(), result.getPhoneNumber());
		Assertions.assertEquals(result.getUsername(), result.getRefNo());
		Assertions.assertEquals(signUpRequest.getAge(), result.getAge());
		Assertions.assertEquals(signUpRequest.getFullName(), result.getFullName());
		Assertions.assertEquals(LoginTypeEnum.USER, result.getLoginType());

		UserKeyRequest userKey = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(signUpRequest.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(userKey);
		Assertions.assertNotNull(userKey.getCompletionDate());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKey.getStatus());
		userKeyRequestRepository.delete(userKey);

		DeviceBinding db = deviceBindingRepository.findAll().stream()
				.filter(deviceBinding -> deviceBinding.getDeviceId().equals(deviceId)).findFirst().orElse(null);
		Assertions.assertNotNull(db);
		Assertions.assertEquals(signUpRequest.getDeviceId(), db.getDeviceId());
		Assertions.assertNull(db.getLogoutDatetime());
		deviceBindingRepository.delete(db);
	}

	@Test
	void register_withReferralCode_success() throws Exception {
		createMockOtpNotification(KafkaTopic.USER_REFEREE_UPDATE_TOPIC, USER_ID, USER_ID, "cihuy");
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		SignUpRequest signUpRequest = jsonUtil.convertValueFromJson(request, new TypeReference<>() {
		});
		User result = userRepository.findAll().stream().filter(user -> signUpRequest.getEmail().equals(user.getEmail()))
				.findFirst().get();

		Assertions.assertNotNull(result);
		Assertions.assertEquals(signUpRequest.getEmail(), result.getEmail());
		Assertions.assertEquals(signUpRequest.getPhoneCountry(), result.getPhoneCountry());
		Assertions.assertEquals(signUpRequest.getPhoneNumber(), result.getPhoneNumber());
		Assertions.assertEquals(result.getUsername(), result.getRefNo());
		Assertions.assertEquals(signUpRequest.getAge(), result.getAge());
		Assertions.assertEquals(signUpRequest.getFullName(), result.getFullName());
		Assertions.assertEquals(LoginTypeEnum.USER, result.getLoginType());
		Assertions.assertNull(result.getReferralCode());

		verifyMockOtpNotification(KafkaTopic.USER_REFEREE_UPDATE_TOPIC, USER_ID, USER_ID, "cihuy");
	}

	@Test
	void register_usingReferralCodePhoneNumber_success() throws Exception {
		createMockOtpNotification(KafkaTopic.USER_REFEREE_UPDATE_TOPIC, USER_ID, USER_ID, "kji3k");
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		SignUpRequest signUpRequest = jsonUtil.convertValueFromJson(request, new TypeReference<>() {
		});
		User result = userRepository.findAll().stream().filter(user -> signUpRequest.getEmail().equals(user.getEmail()))
				.findFirst().get();

		Assertions.assertNotNull(result);
		Assertions.assertEquals(signUpRequest.getEmail(), result.getEmail());
		Assertions.assertEquals(signUpRequest.getPhoneCountry(), result.getPhoneCountry());
		Assertions.assertEquals(signUpRequest.getPhoneNumber(), result.getPhoneNumber());
		Assertions.assertEquals(result.getUsername(), result.getRefNo());
		Assertions.assertEquals(signUpRequest.getAge(), result.getAge());
		Assertions.assertEquals(signUpRequest.getFullName(), result.getFullName());
		Assertions.assertEquals(LoginTypeEnum.USER, result.getLoginType());
		Assertions.assertEquals("kji3k", result.getReferralCodeUsed());
		Assertions.assertNull(result.getReferralCode());

		userRepository.findById(result.getId()).ifPresent(userRepository::delete);
		verifyMockOtpNotification(KafkaTopic.USER_REFEREE_UPDATE_TOPIC, USER_ID, USER_ID, "kji3k");
	}

	@Test
	void register_usingReferralCodePhoneNumber_invalid_error() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getDescription()));
	}

	@Test
	void register_usingReferralCodePhoneNumber_blankRefTnc_error() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getDescription()));
	}

	@Test
	void register_usingReferralCodePhoneNumber_verifiedFalse_error() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getDescription()));
	}

	@Test
	void register_usingReferralCodePhoneNumber_refNotValid_error() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getDescription()));
	}

	@Test
	void register_invalidReferralCode_success() throws Exception {
		String request = getRequest();
		mockMvc.perform(post("/account/register").contentType(MediaType.APPLICATION_JSON).content(request))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE.getDescription()));
	}

	@Test
	void accountById_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/id").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		CurrentUserIdResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		CurrentUserIdResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
		Assertions.assertEquals(response.getActive(), expectation.getActive());
		Assertions.assertEquals(response.getLoginType(), expectation.getLoginType());
		Assertions.assertEquals(response.getDob(), expectation.getDob());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getPassport(), expectation.getPassport());
		Assertions.assertEquals(response.getArmy(), expectation.getArmy());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getAge(), expectation.getAge());
		Assertions.assertEquals(response.getPaymentInfoStatus(), expectation.getPaymentInfoStatus());
		Assertions.assertEquals(response.getEkycVerificationStatus(), expectation.getEkycVerificationStatus());
		Assertions.assertEquals(response.getSecretKey(), expectation.getSecretKey());
	}

	@Test
	void accountByIdV2_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/v2/id").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		AccountResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		AccountResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void getPermission_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/permission").header("Authorization", "Bearer " + getAccountTestToken())
						.param("currentInstitutionId", Constant.DEFAULT_INSTITUTION_ID)
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		List<PermissionDTO> response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		List<PermissionDTO> expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
	}

	@Test
	void accountByRefNo_admin_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/ref-no").queryParam("accessType", AccessTypeEnum.EMAIL.name())
						.queryParam("loginType", LoginTypeEnum.ADMIN.name()).queryParam("username", "deniar@localhost")
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		String response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		String expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void accountByRefNo_phoneNumber_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/ref-no").queryParam("accessType", AccessTypeEnum.MOBILE.name())
						.queryParam("loginType", LoginTypeEnum.USER.name()).queryParam("username", "+***********")
						.header("Authorization", "Bearer " + getAccountTestToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		String response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertEquals("8888888", response);
	}

	@Test
	void currentAccount_success() throws Exception {
		getRedisService().deleteFromValue(CacheKey.COUNTRY_CACHE);
		getRedisService().deleteFromValue(CacheKey.STATE_CACHE);
		MvcResult mvcResult = mockMvc
				.perform(get("/account").header("Authorization", "Bearer " + getAccountTestToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		UserDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserDTO expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getIsNricEditable(), expectation.getIsNricEditable());
		Assertions.assertEquals(response.getUsername(), expectation.getUsername());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getEmail(), expectation.getEmail());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getPhoneNumber(), expectation.getPhoneNumber());
		Assertions.assertEquals(response.getProvider(), expectation.getProvider());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
		Assertions.assertEquals(response.getAuthorities(), expectation.getAuthorities());
		Assertions.assertEquals(response.getAddress1(), expectation.getAddress1());
		Assertions.assertEquals(response.getAddress2(), expectation.getAddress2());
		Assertions.assertEquals(response.getAddress3(), expectation.getAddress3());
		Assertions.assertEquals(response.getPostcode(), expectation.getPostcode());
		Assertions.assertEquals(response.getCountry(), expectation.getCountry());
		Assertions.assertEquals(response.getState(), expectation.getState());
		Assertions.assertEquals(response.getNationality(), expectation.getNationality());
		Assertions.assertEquals(response.getIsNationalityEditable(), expectation.getIsNationalityEditable());
		Assertions.assertEquals(response.getAge(), expectation.getAge());
		Assertions.assertEquals(response.getGender(), expectation.getGender());
		Assertions.assertEquals(response.getMaritalStatus(), expectation.getMaritalStatus());
		Assertions.assertEquals(response.getEthnicity(), expectation.getEthnicity());
		Assertions.assertEquals(response.getReligion(), expectation.getReligion());
		Assertions.assertEquals(response.getCurrency(), expectation.getCurrency());
		Assertions.assertEquals(response.getEpfContribution(), expectation.getEpfContribution());
		Assertions.assertEquals(response.getSocso(), expectation.getSocso());
		Assertions.assertEquals(response.getEis(), expectation.getEis());
		Assertions.assertEquals(response.getEducationLevel(), expectation.getEducationLevel());
		Assertions.assertEquals(response.getEmploymentType(), expectation.getEmploymentType());
		Assertions.assertEquals(response.getOccupationGroup(), expectation.getOccupationGroup());
		Assertions.assertEquals(response.getBusinessNature(), expectation.getBusinessNature());
		Assertions.assertEquals(response.getSelfEmployedName(), expectation.getSelfEmployedName());
		Assertions.assertEquals(response.getInterests(), expectation.getInterests());
		Assertions.assertEquals(response.getFinancialGoals(), expectation.getFinancialGoals());
		Assertions.assertEquals(response.getIncomes().get(0).getId(), expectation.getIncomes().get(0).getId());
		Assertions.assertEquals(response.getIncomes().get(0).getIncomeType(),
				expectation.getIncomes().get(0).getIncomeType());
		Assertions.assertEquals(response.getIncomes().get(0).getEmploymentType(),
				expectation.getIncomes().get(0).getEmploymentType());
		Assertions.assertEquals(0, response.getIncomes().get(0).getMonthlyIncomeAmount()
				.compareTo(expectation.getIncomes().get(0).getMonthlyIncomeAmount()));
		Assertions.assertEquals(response.getIncomes().get(0).getIsReminder(),
				expectation.getIncomes().get(0).getIsReminder());
		Assertions.assertEquals(response.getIncomes().get(0).getReminder(),
				expectation.getIncomes().get(0).getReminder());
		Assertions.assertEquals(response.getExpenses(), expectation.getExpenses());
		Assertions.assertEquals(response.getSegment(), expectation.getSegment());
		Assertions.assertEquals(response.getInstitutions(), expectation.getInstitutions());
		Assertions.assertEquals(response.getLoginType(), expectation.getLoginType());
		Assertions.assertEquals(response.getTermConditionVersion(), expectation.getTermConditionVersion());
		Assertions.assertEquals(response.getPrivacyPolicyVersion(), expectation.getPrivacyPolicyVersion());
		Assertions.assertEquals(response.getPlatformAgreementVersion(), expectation.getPlatformAgreementVersion());
		Assertions.assertEquals(response.getReferralCodeTermConditionVersion(),
				expectation.getReferralCodeTermConditionVersion());
		Assertions.assertEquals(response.getPassport(), expectation.getPassport());
		Assertions.assertEquals(response.getArmy(), expectation.getArmy());
		Assertions.assertEquals(response.getDob(), expectation.getDob());
		Assertions.assertEquals(response.getReferralCode(), expectation.getReferralCode());
		Assertions.assertEquals(response.getEkycVerificationStatus(), expectation.getEkycVerificationStatus());
		Assertions.assertEquals(response.getPaymentInfoApprovedDate(), expectation.getPaymentInfoApprovedDate());
		Assertions.assertEquals(response.getPaymentInfoStatus(), expectation.getPaymentInfoStatus());
		Assertions.assertEquals(response.isHasMobileReferralCode(), expectation.isHasMobileReferralCode());
		Assertions.assertEquals(response.isPinSet(), expectation.isPinSet());
	}

	@Test
	void currentAccount_withoutCountryAndState_success() throws Exception {
		getRedisService().deleteFromValue(CacheKey.COUNTRY_CACHE);
		getRedisService().deleteFromValue(CacheKey.STATE_CACHE);

		MvcResult mvcResult = mockMvc
				.perform(get("/account").header("Authorization", "Bearer " + getAccountTestToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		UserDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserDTO expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getIsNricEditable(), expectation.getIsNricEditable());
		Assertions.assertEquals(response.getUsername(), expectation.getUsername());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getEmail(), expectation.getEmail());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getPhoneNumber(), expectation.getPhoneNumber());
		Assertions.assertEquals(response.getProvider(), expectation.getProvider());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
		Assertions.assertEquals(response.getAuthorities(), expectation.getAuthorities());
		Assertions.assertEquals(response.getAddress1(), expectation.getAddress1());
		Assertions.assertEquals(response.getAddress2(), expectation.getAddress2());
		Assertions.assertEquals(response.getAddress3(), expectation.getAddress3());
		Assertions.assertEquals(response.getPostcode(), expectation.getPostcode());
		Assertions.assertNull(response.getCountry());
		Assertions.assertNull(response.getState());
		Assertions.assertEquals(response.getNationality(), expectation.getNationality());
		Assertions.assertEquals(response.getIsNationalityEditable(), expectation.getIsNationalityEditable());
		Assertions.assertEquals(response.getAge(), expectation.getAge());
		Assertions.assertEquals(response.getGender(), expectation.getGender());
		Assertions.assertEquals(response.getMaritalStatus(), expectation.getMaritalStatus());
		Assertions.assertEquals(response.getEthnicity(), expectation.getEthnicity());
		Assertions.assertEquals(response.getReligion(), expectation.getReligion());
		Assertions.assertEquals(response.getCurrency(), expectation.getCurrency());
		Assertions.assertEquals(response.getEpfContribution(), expectation.getEpfContribution());
		Assertions.assertEquals(response.getSocso(), expectation.getSocso());
		Assertions.assertEquals(response.getEis(), expectation.getEis());
		Assertions.assertEquals(response.getEducationLevel(), expectation.getEducationLevel());
		Assertions.assertEquals(response.getEmploymentType(), expectation.getEmploymentType());
		Assertions.assertEquals(response.getOccupationGroup(), expectation.getOccupationGroup());
		Assertions.assertEquals(response.getBusinessNature(), expectation.getBusinessNature());
		Assertions.assertEquals(response.getSelfEmployedName(), expectation.getSelfEmployedName());
		Assertions.assertEquals(response.getInterests(), expectation.getInterests());
		Assertions.assertEquals(response.getFinancialGoals(), expectation.getFinancialGoals());
		Assertions.assertEquals(response.getIncomes().get(0).getId(), expectation.getIncomes().get(0).getId());
		Assertions.assertEquals(response.getIncomes().get(0).getIncomeType(),
				expectation.getIncomes().get(0).getIncomeType());
		Assertions.assertEquals(response.getIncomes().get(0).getEmploymentType(),
				expectation.getIncomes().get(0).getEmploymentType());
		Assertions.assertEquals(0, response.getIncomes().get(0).getMonthlyIncomeAmount()
				.compareTo(expectation.getIncomes().get(0).getMonthlyIncomeAmount()));
		Assertions.assertEquals(response.getIncomes().get(0).getIsReminder(),
				expectation.getIncomes().get(0).getIsReminder());
		Assertions.assertEquals(response.getIncomes().get(0).getReminder(),
				expectation.getIncomes().get(0).getReminder());
		Assertions.assertEquals(response.getExpenses(), expectation.getExpenses());
		Assertions.assertEquals(response.getSegment(), expectation.getSegment());
		Assertions.assertEquals(response.getInstitutions(), expectation.getInstitutions());
		Assertions.assertEquals(response.getLoginType(), expectation.getLoginType());
		Assertions.assertEquals(response.getTermConditionVersion(), expectation.getTermConditionVersion());
		Assertions.assertEquals(response.getPrivacyPolicyVersion(), expectation.getPrivacyPolicyVersion());
		Assertions.assertEquals(response.getPlatformAgreementVersion(), expectation.getPlatformAgreementVersion());
		Assertions.assertEquals(response.getReferralCodeTermConditionVersion(),
				expectation.getReferralCodeTermConditionVersion());
		Assertions.assertEquals(response.getPassport(), expectation.getPassport());
		Assertions.assertEquals(response.getArmy(), expectation.getArmy());
		Assertions.assertEquals(response.getDob(), expectation.getDob());
		Assertions.assertEquals(response.getReferralCode(), expectation.getReferralCode());
		Assertions.assertEquals(response.getEkycVerificationStatus(), expectation.getEkycVerificationStatus());
		Assertions.assertEquals(response.getPaymentInfoApprovedDate(), expectation.getPaymentInfoApprovedDate());
		Assertions.assertEquals(response.getPaymentInfoStatus(), expectation.getPaymentInfoStatus());
		Assertions.assertEquals(response.isHasMobileReferralCode(), expectation.isHasMobileReferralCode());
		Assertions.assertEquals(response.isPinSet(), expectation.isPinSet());
	}

	@Test
	void currentAccount_notActive_error() throws Exception {
		mockMvc.perform(get("/account").header("Authorization", "Bearer " + getAccountTestToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getDescription()));
	}

	@Test
	void currentAccount_alreadyDeleted_error() throws Exception {
		mockMvc.perform(get("/account").header("Authorization", "Bearer " + getAccountTestToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getDescription()));
	}

	@Test
	void accountByRefNo_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/refNo/9999999").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		CurrentUserIdResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		CurrentUserIdResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
		Assertions.assertEquals(response.getActive(), expectation.getActive());
		Assertions.assertEquals(response.getLoginType(), expectation.getLoginType());
		Assertions.assertEquals(response.getDob(), expectation.getDob());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getPassport(), expectation.getPassport());
		Assertions.assertEquals(response.getArmy(), expectation.getArmy());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getAge(), expectation.getAge());
		Assertions.assertEquals(response.getPaymentInfoStatus(), expectation.getPaymentInfoStatus());
		Assertions.assertEquals(response.getEkycVerificationStatus(), expectation.getEkycVerificationStatus());
		Assertions.assertEquals(response.getSecretKey(), expectation.getSecretKey());
	}

	@Test
	void updateTncAgreement_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertEquals("v1.0.0", user.getReferralCodeTermConditionVersion());
		Duration cacheDuration = Duration.ofMinutes(15);
		redisService.putToValue(CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_REF_NO, user.getRefNo()), user,
				cacheDuration);
		redisService.putToValue(
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_PHONE_NUMBER, user.getPhoneNumber()), user,
				cacheDuration);
		redisService.putToValue(CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_EMAIL, user.getEmail()), user,
				cacheDuration);

		mockMvc.perform(put("/account/tnc-aggreement").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		String expectedTncRefCode = "v.1-ref-code";
		user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertEquals(expectedTncRefCode, user.getReferralCodeTermConditionVersion());

		Optional<AppUser> appUser = redisService.getFromValue(
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_REF_NO, user.getRefNo()), new TypeReference<>() {
				});
		Assertions.assertTrue(appUser.isPresent());
		Assertions.assertEquals(expectedTncRefCode, appUser.get().getReferralCodeTermConditionVersion());

		appUser = redisService.getFromValue(
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_EMAIL, user.getEmail()), new TypeReference<>() {
				});
		Assertions.assertTrue(appUser.isPresent());
		Assertions.assertEquals(expectedTncRefCode, appUser.get().getReferralCodeTermConditionVersion());

		appUser = redisService.getFromValue(
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_PHONE_NUMBER, user.getPhoneNumber()),
				new TypeReference<>() {
				});
		Assertions.assertTrue(appUser.isPresent());
		Assertions.assertEquals(expectedTncRefCode, appUser.get().getReferralCodeTermConditionVersion());
	}

	@Test
	void currentUser_nationalities_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/account/integrations/nationalities")
				.header("Authorization", "Bearer " + getAccountTestToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		CurrentUserNationalityResponse response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		CurrentUserNationalityResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void currentUser_incomeEpf_success() throws Exception {
		List<EpfContribution> epfs = epfContributionRepository.findByUserId(USER_ID);
		epfContributionRepository.deleteAll(epfs);
		MvcResult mvcResult = mockMvc
				.perform(get("/account/income-epf").header("Authorization", "Bearer " + getAccountTestToken())
						.queryParam("incomeTypeId", "241fd082-51b3-444f-8e51-0feebe6e01bb")
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		IncomeAndEpfResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		IncomeAndEpfResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void currentUser_incomeEpf_hasContribution_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/income-epf").header("Authorization", "Bearer " + getAccountTestToken())
						.queryParam("incomeTypeId", "241fd082-51b3-444f-8e51-0feebe6e01bb")
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		IncomeAndEpfResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		IncomeAndEpfResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void emailMasked_byPhoneNumber_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/account/email-masked").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Assertions.assertEquals("ac***************it@localhost", mvcResult.getResponse().getContentAsString());
	}

	@Test
	void emailMasked_error() throws Exception {
		mockMvc.perform(post("/account/email-masked").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNotFound()).andExpect(MockMvcResultMatchers
						.jsonPath("$.errorCode").value(ErrorCodeGlobalEnum.ENTITY_NOT_FOUND.getCode()));
	}

	@Test
	void existMobile_byPhoneNumber_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/account/exist-mobile").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Assertions.assertEquals(Boolean.TRUE.toString(), mvcResult.getResponse().getContentAsString());
	}

	@Test
	void existMobile_byPhoneNumber_successInvalid() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/account/exist-mobile").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Assertions.assertEquals(Boolean.FALSE.toString(), mvcResult.getResponse().getContentAsString());
	}

	@Test
	void updateAddress_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/account/update-address").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		UpdateAddressResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UpdateAddressResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void updateTnc_success() throws Exception {
		mockMvc.perform(put("/account/update-term-condition-privacy-policy")
				.header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("termAndConditionRequireUpdate", "true").queryParam("privacyPolicyRequireUpdate", "true")
				.queryParam("platformAgreementRequireUpdate", "true").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertEquals("v2-tnc", user.getTermConditionVersion());
		Assertions.assertEquals("v2-priv-pol", user.getPrivacyPolicyVersion());
		Assertions.assertEquals("v2-plat-agree", user.getPlatformAgreementVersion());
	}

	@Test
	void updateTnc_noUpdate_success() throws Exception {
		mockMvc.perform(put("/account/update-term-condition-privacy-policy")
				.header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("termAndConditionRequireUpdate", "false").queryParam("privacyPolicyRequireUpdate", "false")
				.queryParam("platformAgreementRequireUpdate", "false").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();
	}

	@Test
	void getSecretKey_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/secret-key").header("Authorization", "Bearer " + getAccountTestToken())
						.header("api-key", "9ebbc78d-dff5-4876-bb45-13d0a6e9b8e3")
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Assertions.assertEquals(user.getSecretKey(), mvcResult.getResponse().getContentAsString());
	}

	@Test
	void getSecretKey_accessDenied() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/secret-key").header("Authorization", "Bearer " + getAccountTestToken())
						.header("api-key", "9ebbc78d-dff5-4876-bb45-denied").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isForbidden()).andReturn();
	}

	@Test
	void getSecretKey_emptyParameter_notConfigured() throws Exception {
		String currentApiKey = baseProperties.getApiKey();
		baseProperties.setApiKey("");
		MvcResult mvcResult = mockMvc
				.perform(get("/account/secret-key").header("Authorization", "Bearer " + getAccountTestToken())
						.header("api-key", "").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isInternalServerError()).andReturn();
		baseProperties.setApiKey(currentApiKey);
	}

	@Test
	void updateUser_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/account/update-profile").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
		UserResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getIsNricEditable(), expectation.getIsNricEditable());
		Assertions.assertEquals(response.getUsername(), expectation.getUsername());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getEmail(), expectation.getEmail());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getPhoneNumber(), expectation.getPhoneNumber());
		Assertions.assertEquals(response.getProvider(), expectation.getProvider());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
	}

	@Test
	void updateUser_usingKey_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/account/update-profile").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
		UserResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getIsNricEditable(), expectation.getIsNricEditable());
		Assertions.assertEquals(response.getUsername(), expectation.getUsername());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getEmail(), expectation.getEmail());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getPhoneNumber(), expectation.getPhoneNumber());
		Assertions.assertEquals(response.getProvider(), expectation.getProvider());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());

		User user = userRepository.findById(response.getId()).orElse(null);
		Assertions.assertNotNull(user);
		UserKeyRequest keyRequest = userKeyRequestRepository.findByUsernameAndTypeAndStatus(user.getEmailRaw(),
				RequestKeyType.VERIFICATION_CHANGE_EMAIL, RequestKeyStatus.COMPLETED);
		Assertions.assertNotNull(keyRequest);
		userKeyRequestRepository.delete(keyRequest);
	}

	@Test
	void updateUser_usingInvalid_error() throws Exception {
		mockMvc.perform(put("/account/update-profile").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));
	}

	@Test
	void updateUser_usingEmptyKey_error() throws Exception {
		mockMvc.perform(put("/account/update-profile").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));
	}

	@Test
	void updateUser_dobNull_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setDob(null);
		userRepository.save(user);

		MvcResult mvcResult = mockMvc
				.perform(put("/account/update-profile").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
		UserResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getIsNricEditable(), expectation.getIsNricEditable());
		Assertions.assertEquals(response.getUsername(), expectation.getUsername());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getEmail(), expectation.getEmail());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getPhoneNumber(), expectation.getPhoneNumber());
		Assertions.assertEquals(response.getProvider(), expectation.getProvider());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
		Assertions.assertNull(response.getDob());
	}

	@Test
	void updateUser_dobEmpty_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setDob(null);
		userRepository.save(user);

		MvcResult mvcResult = mockMvc
				.perform(put("/account/update-profile").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
		UserResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		UserResponse expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response.getId(), expectation.getId());
		Assertions.assertEquals(response.getNric(), expectation.getNric());
		Assertions.assertEquals(response.getIsNricEditable(), expectation.getIsNricEditable());
		Assertions.assertEquals(response.getUsername(), expectation.getUsername());
		Assertions.assertEquals(response.getFullName(), expectation.getFullName());
		Assertions.assertEquals(response.getEmail(), expectation.getEmail());
		Assertions.assertEquals(response.getPhoneCountry(), expectation.getPhoneCountry());
		Assertions.assertEquals(response.getPhoneNumber(), expectation.getPhoneNumber());
		Assertions.assertEquals(response.getProvider(), expectation.getProvider());
		Assertions.assertEquals(response.getRefNo(), expectation.getRefNo());
		Assertions.assertNull(response.getDob());
	}

	@Test
	void deleteAccountMessage_success() throws Exception {
		String deleteMessage = "Want to delete it.";
		mockMvc.perform(
				post("/account/delete-account-message").header("Authorization", "Bearer " + getAccountTestToken())
						.queryParam("deleteMessage", deleteMessage).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Optional<DeleteAccountMessage> result = deleteAccountMessageRepository.findAll().stream()
				.filter(deleteAccountMessage -> deleteAccountMessage.getUserid().equals(USER_ID)).findFirst();

		Assertions.assertTrue(result.isPresent());
		Assertions.assertEquals(deleteMessage, result.get().getDeleteMessage());
	}

	@Test
	void validateTnc_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setTermConditionVersion("v2-tnc");
		user.setPrivacyPolicyVersion("v2-priv-pol");
		user.setPlatformAgreementVersion("v2-plat-agree");
		userRepository.save(user);

		MvcResult mvcResult = mockMvc.perform(get("/account/validate-term-condition-privacy-policy")
				.header("Authorization", "Bearer " + getAccountTestToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		TermConditionAndPrivacyPolicyDTO response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		TermConditionAndPrivacyPolicyDTO expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void validateTnc_needUpdate_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setTermConditionVersion("v2-tnc");
		user.setPrivacyPolicyVersion("v2-priv-pol");
		user.setPlatformAgreementVersion("v2-plat-agree");
		userRepository.save(user);

		MvcResult mvcResult = mockMvc.perform(get("/account/validate-term-condition-privacy-policy")
				.header("Authorization", "Bearer " + getAccountTestToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		TermConditionAndPrivacyPolicyDTO response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		TermConditionAndPrivacyPolicyDTO expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(response, expectation);
	}

	@Test
	void deleteProcess_noPin_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setPin(null);
		userRepository.save(user);

		mockMvc.perform(put("/account/delete-process").header("Authorization", "Bearer " + getAccountTestToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();

		user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertNotNull(user.getDeletedDatetime());
		Assertions.assertFalse(user.getActive());

	}

	@Test
	void deleteProcess_hasPin_success() throws Exception {
		mockMvc.perform(put("/account/delete-process").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertNotNull(user.getDeletedDatetime());
		Assertions.assertFalse(user.getActive());

		Optional<UserKeyRequest> processedData = userKeyRequestRepository.findByKeyValueAndType(
				"F8PfsuoqZSbO9Tzysi2+YNc+9Hbo0K9hbUIcJJY=", RequestKeyType.VERIFICATION_DELETE_ACCOUNT);
		Assertions.assertTrue(processedData.isPresent());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, processedData.get().getStatus());
		Assertions.assertNotNull(processedData.get().getCompletionDate());
		userKeyRequestRepository.delete(processedData.get());
	}

	@Test
	void deleteProcess_hasBlankPin_success() throws Exception {
		mockMvc.perform(put("/account/delete-process").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));

	}

	@Test
	void deleteProcess_hasInvalidPin_success() throws Exception {
		mockMvc.perform(put("/account/delete-process").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));

		Optional<UserKeyRequest> processedData = userKeyRequestRepository.findByKeyValueAndType(
				"F8PfsuoqZSbO9Tzysi2+YNc+9Hbo0K9hbUIcJJY=", RequestKeyType.VERIFICATION_DELETE_ACCOUNT);
		Assertions.assertTrue(processedData.isPresent());
		Assertions.assertEquals(RequestKeyStatus.PENDING, processedData.get().getStatus());
		userKeyRequestRepository.delete(processedData.get());
	}

	@Test
	void deleteProcess_hasNonPendingPin_success() throws Exception {
		mockMvc.perform(put("/account/delete-process").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));

		Optional<UserKeyRequest> processedData = userKeyRequestRepository.findByKeyValueAndType(
				"F8PfsuoqZSbO9Tzysi2+YNc+9Hbo0K9hbUIcJJY=", RequestKeyType.VERIFICATION_DELETE_ACCOUNT);
		Assertions.assertTrue(processedData.isPresent());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, processedData.get().getStatus());
		userKeyRequestRepository.delete(processedData.get());
	}

	@Test
	void private_getPublicKey_notConfigured() throws Exception {
		Instant timestamp = Instant.now();
		PublicAuthentication publicAuthentication = publicAuthenticationRepository
				.findByIdentifier("com.hextartech.moneyx.dev").get();
		String hash = HashUtil.sha512Hex(publicAuthentication.getApiKey(), publicAuthentication.getIdentifier(),
				timestamp.toString());
		MvcResult mvcResult = mockMvc
				.perform(get("/account/private/public-key").queryParam("identifier", "com.hextartech.moneyx.dev")
						.queryParam("hash", hash).queryParam("timestamp", timestamp.toString())
						.header("identifier", "com.hextartech.moneyx.dev").header("hash", hash)
						.header("timestamp", timestamp.toString())
						.header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ")
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		PublicAuthenticationDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		PublicAuthenticationDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertEquals(expected, response);
	}

	@Test
	void private_getPublicKey_nullSecretKey() throws Exception {
		Instant timestamp = Instant.now();
		PublicAuthentication publicAuthentication = publicAuthenticationRepository.findByIdentifier("account.it").get();
		String hash = HashUtil.sha512Hex(publicAuthentication.getApiKey(), publicAuthentication.getIdentifier(),
				timestamp.toString());
		MvcResult mvcResult = mockMvc.perform(get("/account/private/public-key").queryParam("identifier", "account.it")
				.queryParam("hash", hash).queryParam("timestamp", timestamp.toString())
				.header("identifier", "account.it").header("hash", hash).header("timestamp", timestamp.toString())
				.header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ")
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		PublicAuthenticationDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(response.getSecretKey());
		publicAuthenticationRepository.findById(response.getId()).ifPresent(publicAuthenticationRepository::delete);
	}

	@Test
	void calculateNetWorth_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/extended-net-worth").header("Authorization", "Bearer " + getAccountTestToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		ExtendedNetWorthCalculationResponse response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		ExtendedNetWorthCalculationResponse expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertEquals(expected, response);
	}

	@Test
	void resetMobile_sms_password_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "+************", "+************", "OTP");
		MvcResult mvcResult = mockMvc.perform(post("/account/request-otp/reset-mobile")
				.header("Authorization", "Bearer " + getAccountTestToken()).queryParam("passcodeType", "PASSWORD")
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		String key = mvcResult.getResponse().getContentAsString();
		Assertions.assertNotNull(key);
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], key);
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));
		userKeyRequestRepository.delete(expected);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "+************", "+************", "OTP");
	}

	@Test
	void resetMobile_sms_pin_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "+************", "+************", "OTP");
		String pinVerificationKey = "Lc00HJ1MWv1mKqu";
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp/reset-mobile")
						.header("Authorization", "Bearer " + getAccountTestToken()).queryParam("passcodeType", "PIN")
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		String key = mvcResult.getResponse().getContentAsString();
		Assertions.assertNotNull(key);
		List<UserKeyRequest> keyRequests = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key) || ukr.getKeyValue().contains(pinVerificationKey))
				.toList();
		Assertions.assertNotNull(keyRequests);
		Assertions.assertEquals(2, keyRequests.size());
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], key);
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));

		userKeyRequestRepository.deleteAll(keyRequests);
		verifyMockOtpNotification(KafkaTopic.SEND_SMS_TOPIC, "+************", "+************", "OTP");
	}

	@Test
	void resetMobile_wa_pin_success() throws Exception {
		this.createMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "+***********", "+***********", "OTP");

		String pinVerificationKey = "Lc00HJ1MWv1mKqu";
		MvcResult mvcResult = mockMvc
				.perform(post("/account/request-otp/reset-mobile")
						.header("Authorization", "Bearer " + getAccountTestToken()).queryParam("passcodeType", "PIN")
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		String key = mvcResult.getResponse().getContentAsString();
		Assertions.assertNotNull(key);
		List<UserKeyRequest> keyRequests = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key) || ukr.getKeyValue().contains(pinVerificationKey))
				.toList();
		Assertions.assertNotNull(keyRequests);
		Assertions.assertEquals(2, keyRequests.size());
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], key);
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		userKeyRequestRepository.deleteAll(keyRequests);

		this.verifyMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "+***********", "+***********", "OTP");
	}

	private void createMockOtpNotification(String topic, String key, String to, String templateName) {
		if (KafkaTopic.SEND_SMS_TOPIC.equals(topic)) {
			Mockito.doNothing().when(kafkaSender).safeSend(Mockito.eq(topic), Mockito.eq(key),
					Mockito.argThat(payload -> {
						if (!(payload instanceof SmsOtpRequest req)) {
							return false;
						}
						return req.getTo().equals(to) && req.getTemplateName().equals(templateName)
								&& req.getParams().getOtp().length() == 6
								&& StringUtils.isNumeric(req.getParams().getOtp());
					}));
		}
		else if (KafkaTopic.SEND_WHATSAPP_TOPIC.equals(topic)) {
			Mockito.doNothing().when(kafkaSender).safeSend(Mockito.eq(topic), Mockito.eq(key),
					Mockito.argThat(payload -> {
						if (!(payload instanceof WhatsAppOtpRequest req)) {
							return false;
						}
						return req.getTo().equals(to) && req.getTemplateName().equals(templateName)
								&& req.getParams().getOtp().length() == 6
								&& StringUtils.isNumeric(req.getParams().getOtp());
					}));
		}
		else if (KafkaTopic.USER_REFEREE_UPDATE_TOPIC.equals(topic)) {
			if (templateName.equals("PASSWORD_UPDATED")) {
				Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(topic), Mockito.any(),
						Mockito.argThat(payload -> {
							if (!(payload instanceof EmailRequest req)) {
								return false;
							}
							return req.getTo().contains(to)
									&& Objects.nonNull(req.getTemplateVariable().get("firstName"));
						}));
				return;
			}
			Mockito.doNothing().when(kafkaSender).safeSend(Mockito.eq(topic), Mockito.any(),
					Mockito.argThat(payload -> {
						if (!(payload instanceof UserRefereeUpdateRequest req)) {
							return false;
						}
						return req.getSource().equals(SourceSystemEnum.MXAPP)
								&& req.getReferrerCode().equals(templateName)
								&& StringUtils.isNotBlank(req.getReferredId()) && Objects.nonNull(req.getSignUpDate());
					}));
		}

	}

	private void verifyMockOtpNotification(String topic, String key, String to, String templateName) {
		if (KafkaTopic.SEND_SMS_TOPIC.equals(topic)) {
			Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(topic), Mockito.eq(key),
					Mockito.argThat(payload -> {
						if (!(payload instanceof SmsOtpRequest req)) {
							return false;
						}
						return req.getTo().equals(to) && req.getTemplateName().equals(templateName)
								&& req.getParams().getOtp().length() == 6
								&& StringUtils.isNumeric(req.getParams().getOtp());
					}));
		}
		else if (KafkaTopic.SEND_WHATSAPP_TOPIC.equals(topic)) {
			Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(topic), Mockito.eq(key),
					Mockito.argThat(payload -> {
						if (!(payload instanceof WhatsAppOtpRequest req)) {
							return false;
						}
						return req.getTo().equals(to) && req.getTemplateName().equals(templateName)
								&& req.getParams().getOtp().length() == 6
								&& StringUtils.isNumeric(req.getParams().getOtp());
					}));
		}
		else if (KafkaTopic.USER_REFEREE_UPDATE_TOPIC.equals(topic)) {
			Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(topic), Mockito.any(),
					Mockito.argThat(payload -> {
						if (!(payload instanceof UserRefereeUpdateRequest req)) {
							return false;
						}
						return req.getSource().equals(SourceSystemEnum.MXAPP)
								&& req.getReferrerCode().equals(templateName)
								&& StringUtils.isNotBlank(req.getReferredId()) && Objects.nonNull(req.getSignUpDate());
					}));
		}
		else if (KafkaTopic.SEND_EMAIL_TOPIC.equals(topic)) {
			if (templateName.equals("PASSWORD_UPDATED")) {
				Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(topic), Mockito.any(),
						Mockito.argThat(payload -> {
							if (!(payload instanceof EmailRequest req)) {
								return false;
							}
							return req.getTo().contains(to)
									&& Objects.nonNull(req.getTemplateVariable().get("firstName"));
						}));
				return;
			}
			Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(topic), Mockito.any(),
					Mockito.argThat(payload -> {
						if (!(payload instanceof EmailRequest req)) {
							return false;
						}
						return req.getTo().contains(to) && Objects.nonNull(req.getTemplateVariable().get("otp"))
								&& StringUtils.isNumeric((String) req.getTemplateVariable().get("otp"));
					}));
		}

	}

	@Test
	void resetMobile_wa_password_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "+***********", "+***********", "OTP");
		MvcResult mvcResult = mockMvc.perform(post("/account/request-otp/reset-mobile")
				.header("Authorization", "Bearer " + getAccountTestToken()).queryParam("passcodeType", "PASSWORD")
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		String key = mvcResult.getResponse().getContentAsString();
		Assertions.assertNotNull(key);
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], key);
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));
		userKeyRequestRepository.delete(expected);

		verifyMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "+***********", "+***********", "OTP");
	}

	@Test
	void resetMobile_wa_password_key_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "+************", "+************", "OTP");
		MvcResult mvcResult = mockMvc.perform(post("/account/request-otp/reset-mobile")
				.header("Authorization", "Bearer " + getAccountTestToken()).queryParam("passcodeType", "PASSWORD")
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		String key = mvcResult.getResponse().getContentAsString();
		Assertions.assertNotNull(key);
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		String[] keyRes = expected.getKeyValue().split("_");
		Assertions.assertEquals(keyRes[0], key);
		Assertions.assertEquals(12, keyRes[0].length());
		Assertions.assertEquals(6, keyRes[1].length());
		Assertions.assertTrue(StringUtils.isNumeric(keyRes[1]));
		userKeyRequestRepository.delete(expected);

		verifyMockOtpNotification(KafkaTopic.SEND_WHATSAPP_TOPIC, "+************", "+************", "OTP");
	}

	@Test
	void verifyOtp_phoneNumber_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/verify-otp").header("Authorization", "Bearer " + getAccountTestToken())
						.queryParam("username", "+************").queryParam("key", "iy6go1pCIN65")
						.queryParam("value", "479877").queryParam("requestKeyType", "PHONE_VERIFICATION")
						.queryParam("usernameType", "PHONE_NUMBER").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		String key = "iy6go1pCIN65";
		Assertions.assertNotNull(key);
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void verifyOtp_email_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/verify-otp").header("Authorization", "Bearer " + getAccountTestToken())
						.queryParam("username", "accountusercustomit@localhost").queryParam("key", "iy6go1pCIN65")
						.queryParam("value", "479877").queryParam("requestKeyType", "PHONE_VERIFICATION")
						.queryParam("usernameType", "EMAIL").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		String key = "iy6go1pCIN65";
		Assertions.assertNotNull(key);
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void verifyOtp_phoneNumber_invalidKey_error() throws Exception {
		mockMvc.perform(get("/account/verify-otp").header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("username", "+************").queryParam("key", "invalid").queryParam("value", "479877")
				.queryParam("requestKeyType", "PHONE_VERIFICATION").queryParam("usernameType", "PHONE_NUMBER")
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));
	}

	@Test
	void verifyOtpPost_phoneNumber_success() throws Exception {
		mockMvc.perform(post("/account/verify-otp").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		String key = "iy6go1pCIN65";
		Assertions.assertNotNull(key);
		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(ukr -> ukr.getKeyValue().contains(key)).findFirst().orElse(null);
		Assertions.assertNotNull(expected);
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void changePassword_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "accountusercustomit@localhost",
				"accountusercustomit@localhost", "PASSWORD_UPDATED");
		MvcResult mvcResult = mockMvc
				.perform(put("/account/change-password").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertTrue(passwordEncoder.matches("123@Mandrill", user.getPassword()));
		List<PasswordTransaction> passwordTransactions = passwordTransactionRepository
				.findAllByUserIdOrderByCreatedDateDesc(USER_ID);
		Assertions.assertEquals(1, passwordTransactions.size());
		Assertions.assertTrue(passwordEncoder.matches("arianto@123", passwordTransactions.get(0).getPasswordHash()));
		passwordTransactionRepository.deleteAll(passwordTransactions);
		verifyMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "accountusercustomit@localhost",
				"accountusercustomit@localhost", "PASSWORD_UPDATED");
	}

	@Test
	void changePassword_already3Times_success() throws Exception {
		createMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "accountusercustomit@localhost",
				"accountusercustomit@localhost", "PASSWORD_UPDATED");
		MvcResult mvcResult = mockMvc
				.perform(put("/account/change-password").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertTrue(passwordEncoder.matches("123@Mandrill", user.getPassword()));
		List<PasswordTransaction> passwordTransactions = passwordTransactionRepository
				.findAllByUserIdOrderByCreatedDateDesc(USER_ID);
		Assertions.assertEquals(3, passwordTransactions.size());
		passwordTransactionRepository.deleteAll(passwordTransactions);
		verifyMockOtpNotification(KafkaTopic.SEND_EMAIL_TOPIC, "accountusercustomit@localhost",
				"accountusercustomit@localhost", "PASSWORD_UPDATED");
	}

	@Test
	void changePassword_oldPasswordNotMatch_error() throws Exception {
		mockMvc.perform(put("/account/change-password").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_PASSWORD.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.INVALID_PASSWORD.getDescription()));
	}

	@Test
	void changePassword_sameWithHistory_error() throws Exception {
		mockMvc.perform(put("/account/change-password").header("Authorization", "Bearer " + getAccountTestToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.PASSWORD_TRANSACTION_EXIST.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.PASSWORD_TRANSACTION_EXIST.getDescription()));

		passwordTransactionRepository.deleteById("480214a0-9959-404e-b1f1-e848f641a855");

	}

	@Test
	void logout_success() throws Exception {
		mockMvc.perform(put("/account/logout").header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("deviceId", "71ccc2c94155d62e91c690d1567cffbe6dae72bc32e49df4b16252c3ac413117")
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();

		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();

		Optional<Token> t = tokenRepository.findByUserAndStatus(user, TokenStatus.LOGOUT);
		Assertions.assertTrue(t.isPresent());
		tokenRepository.delete(t.get());

		List<DeviceBinding> deviceBinding = deviceBindingRepository.findByUserId(USER_ID);
		Assertions.assertEquals(1, deviceBinding.size());
		Assertions.assertNotNull(deviceBinding.get(0).getLogoutDatetime());
		deviceBindingRepository.deleteAll(deviceBinding);
	}

	@Test
	void resetMobile_success() throws Exception {
		mockMvc.perform(put("/account/reset-mobile").header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("usernameType", "PHONE_NUMBER").content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();

		UpdateMobileRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertEquals(request.getPhoneCountry(), user.getPhoneCountry());
		Assertions.assertEquals(request.getPhoneNumber(), user.getPhoneNumber());

		UserKeyRequest keyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(request.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(keyRequest);
		Assertions.assertTrue(keyRequest.getKeyValue().contains(request.getKey()));
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, keyRequest.getStatus());
		userKeyRequestRepository.delete(keyRequest);
	}

	@Test
	void resetPasswordMobile_success() throws Exception {
		mockMvc.perform(put("/account/reset-password-mobile").header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("usernameType", "PHONE_NUMBER").content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();

		UpdatePasswordResetMobileRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertEquals(0, user.getLoginFailAttempt());
		Assertions.assertTrue(user.getEmailVerified());
		Assertions.assertTrue(passwordEncoder.matches("Mandrill@123", user.getPassword()));

		UserKeyRequest keyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(request.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(keyRequest);
		Assertions.assertTrue(keyRequest.getKeyValue().contains(request.getKey()));
		Assertions.assertNotNull(keyRequest.getCompletionDate());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, keyRequest.getStatus());
		userKeyRequestRepository.delete(keyRequest);

		PasswordTransaction passwordTransaction = passwordTransactionRepository.findAll().stream()
				.filter(pt -> USER_ID.equals(pt.getUser().getId())).findFirst().orElse(null);
		Assertions.assertNotNull(passwordTransaction);
		Assertions.assertTrue(passwordEncoder.matches(USER_PASSWORD, passwordTransaction.getPasswordHash()));
		passwordTransactionRepository.delete(passwordTransaction);
	}

	@Test
	void resetPasswordMobile_invalidKey_success() throws Exception {
		mockMvc.perform(put("/account/reset-password-mobile").header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("usernameType", "PHONE_NUMBER").content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));

		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertEquals(0, user.getLoginFailAttempt());
		Assertions.assertTrue(user.getEmailVerified());

		UserKeyRequest keyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains("resetpasswordmobile")).findFirst()
				.orElse(null);
		Assertions.assertNotNull(keyRequest);
		Assertions.assertEquals(RequestKeyStatus.PENDING, keyRequest.getStatus());
		userKeyRequestRepository.delete(keyRequest);
	}

	@Test
	void resetPasswordMobile_noPasswordYet_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setPassword(null);
		userRepository.save(user);

		mockMvc.perform(put("/account/reset-password-mobile").header("Authorization", "Bearer " + getAccountTestToken())
				.queryParam("usernameType", "PHONE_NUMBER").content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();

		user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertEquals(0, user.getLoginFailAttempt());
		Assertions.assertTrue(user.getEmailVerified());

		UserKeyRequest keyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains("resetpasswordmobile")).findFirst()
				.orElse(null);
		Assertions.assertNotNull(keyRequest);
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, keyRequest.getStatus());
		userKeyRequestRepository.delete(keyRequest);

		PasswordTransaction passwordTransaction = passwordTransactionRepository.findAll().stream()
				.filter(pt -> USER_ID.equals(pt.getUser().getId())).findFirst().orElse(null);
		Assertions.assertNull(passwordTransaction);
	}

	@Test
	void checkAndRegister_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/account/private/check-and-register")
						.header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ").content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		UserPublicWebResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);

		UserPublicWebRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		User user = userRepository.findAll().stream().filter(user1 -> response.getId().equals(user1.getId()))
				.findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertEquals(0, user.getLoginFailAttempt());
		Assertions.assertFalse(user.getEmailVerified());

		UserKeyRequest keyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains(request.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(keyRequest);
		Assertions.assertTrue(keyRequest.getKeyValue().contains(request.getKey()));
		Assertions.assertNotNull(keyRequest.getCompletionDate());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, keyRequest.getStatus());
		userKeyRequestRepository.delete(keyRequest);

		userRepository.delete(user);
	}

	@Test
	void resetPasswordEmail_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/account/reset-password-email").content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findAll().stream().filter(user1 -> USER_ID.equals(user1.getId())).findFirst().get();
		Assertions.assertNotNull(user);
		Assertions.assertEquals(0, user.getLoginFailAttempt());
		Assertions.assertTrue(user.getEmailVerified());

		UserKeyRequest keyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().contains("lkejl")).findFirst().orElse(null);
		Assertions.assertNotNull(keyRequest);
		Assertions.assertTrue(keyRequest.getKeyValue().contains("lkejl"));
		Assertions.assertNotNull(keyRequest.getCompletionDate());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, keyRequest.getStatus());
		userKeyRequestRepository.delete(keyRequest);
	}

	@Test
	void resetPasswordEmail_notValidKey_success() throws Exception {

		MvcResult mvcResult = mockMvc
				.perform(put("/account/reset-password-email").header("Authorization", "Bearer " + getAccountTestToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();
		Assertions.assertTrue(mvcResult.getResponse().getContentAsString().isBlank());
	}

	@Test
	void getCurrentUserAi_noExpense_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		expenseRepository.deleteAll(user.getExpenses());

		MvcResult mvcResult = mockMvc
				.perform(get("/account/private/for-ai").header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ")
						.header("Authorization", "Bearer " + getAccountTestToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		CurrentUserDataForAIResponse response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		CurrentUserDataForAIResponse expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertEquals(expected, response);
	}

	@Test
	void getCurrentUserAi_completeData_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/private/for-ai").header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ")
						.header("Authorization", "Bearer " + getAccountTestToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		CurrentUserDataForAIResponse response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		CurrentUserDataForAIResponse expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertEquals(expected, response);
	}

	private String getAccountTestToken() {
		Path userAccessSource = Paths.get("src/test/resources/" + getBasePath().getDefaultPath() + "redis/token.json");
		UserToken userToken = jsonUtil.convertValueFromJson(userAccessSource.toFile(), UserToken.class);
		userTokenRepository.save(userToken);
		return tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(), userToken.getId());
	}

}
