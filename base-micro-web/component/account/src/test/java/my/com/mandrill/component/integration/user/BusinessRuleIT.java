package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

public class BusinessRuleIT extends BaseIntegrationTest {

	private static final String USER_ID = "bc84d60d-ee88-4d8f-aacd-484fc734c391";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void cleanup() {
		cleanUpHelper.cleanBusinessRuleIT(Collections.singletonList(USER_ID));
	}

	@Test
	void checkImbalance_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/business-rule/check-imbalance-income-expense")
				.header("Authorization", "Bearer " + generateCustomToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Boolean response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<Boolean>() {
				});
		Assertions.assertTrue(response);
	}

	@Test
	void checkImbalance_balance_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/business-rule/check-imbalance-income-expense")
				.header("Authorization", "Bearer " + generateCustomToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Boolean response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<Boolean>() {
				});
		Assertions.assertFalse(response);
	}

	@Test
	void checkImbalance_segmentCodeBeginner_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/business-rule/check-imbalance-income-expense")
				.header("Authorization", "Bearer " + generateCustomToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Boolean response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<Boolean>() {
				});
		Assertions.assertFalse(response);
	}

	@Test
	void checkImbalance_zeroExpense_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/business-rule/check-imbalance-income-expense")
				.header("Authorization", "Bearer " + generateCustomToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Boolean response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<Boolean>() {
				});
		Assertions.assertFalse(response);
	}

	@Test
	void checkImbalance_segmentNotFound_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/business-rule/check-imbalance-income-expense")
				.header("Authorization", "Bearer " + generateCustomToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Boolean response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertFalse(response);
	}

	private String generateCustomToken() {
		Path userAccessSource = Paths.get("src/test/resources/" + getBasePath().getBaseScenario() + "redis/token.json");
		UserToken userToken = jsonUtil.convertValueFromJson(userAccessSource.toFile(), UserToken.class);
		userTokenRepository.save(userToken);
		return tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(), userToken.getId());
	}

}
