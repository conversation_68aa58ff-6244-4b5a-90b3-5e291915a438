package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.consumer.IncomeConsumer;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.IncomeRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class IncomeConsumerIT extends BaseIntegrationTest {

	private static final String USER_ID = "31111539-affa-4ffa-8836-54d7ebef87ea";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private IncomeRepository incomeRepository;

	@Autowired
	private IncomeConsumer incomeConsumer;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	void tearDown() {
		cleanUpHelper.cleanUpIncomeGroupIT(Collections.singletonList(USER_ID));
	}

	@Test
	void createIncome_success() {
		ProducerRecord<String, String> record = new ProducerRecord<>(KafkaTopic.CREATE_OR_UPDATE_INCOME, USER_ID,
				getRequest());
		record.headers().add(KafkaHeaders.RECEIVED_KEY, USER_ID.getBytes());

		kafkaTemplate.send(record);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Income income = incomeRepository.findTopByUserIdAndIncomeTypeIdOrderByCreatedDateDesc(USER_ID,
					"241fd082-51b3-444f-8e51-0feebe6e01bb");
			Assertions.assertNotNull(income);
			Assertions.assertEquals(BigDecimal.valueOf(6000), income.getMonthlyIncomeAmount());
			incomeRepository.delete(income);
		});
	}

	@Test
	void createIncome_exist_success() {
		ProducerRecord<String, String> record = new ProducerRecord<>(KafkaTopic.CREATE_OR_UPDATE_INCOME, USER_ID,
				getRequest());
		record.headers().add(KafkaHeaders.RECEIVED_KEY, USER_ID.getBytes());

		kafkaTemplate.send(record);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Income income = incomeRepository.findTopByUserIdAndIncomeTypeIdOrderByCreatedDateDesc(USER_ID,
					"241fd082-51b3-444f-8e51-0feebe6e01bb");
			Assertions.assertNotNull(income);
			Assertions.assertEquals(BigDecimal.valueOf(9999), income.getMonthlyIncomeAmount());
			incomeRepository.delete(income);
		});
	}

	@Test
	void createIncome_handlerError_success() {
		incomeConsumer.handleError("error message");
	}

}
