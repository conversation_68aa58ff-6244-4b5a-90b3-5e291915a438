package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.DeviceKeyDTO;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.AppUserRepository;
import my.com.mandrill.component.repository.jpa.DeviceKeyRepository;
import my.com.mandrill.component.repository.jpa.SignatureChallengeRepository;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

public class DeviceKeyIT extends BaseIntegrationTest {

	private static final String USER_ID = "256934b7-0d8f-419b-b174-fc1c41da0054";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private SignatureChallengeRepository signatureChallengeRepository;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserKeyRequestRepository userKeyRequestRepository;

	@Autowired
	private DeviceKeyRepository deviceKeyRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private AppUserRepository appUserRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void tearDown() {
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	public void register_startWithExpiredScheduled_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/device-keys/register/start").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			SignatureChallenge result = signatureChallengeRepository.findAll().stream()
					.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
							.equals("deviceKeyIntegrationTest"))
					.findFirst().orElse(null);
			Assertions.assertNotNull(result);
			Assertions.assertTrue(result.getExpired());
			signatureChallengeRepository.delete(result);

			DeviceKey deviceKey = result.getDeviceKey();
			Assertions.assertNotNull(deviceKey);
			deviceKeyRepository.delete(deviceKey);
		});
	}

	@Test
	public void register_startWithNoExpiredScheduled_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/device-keys/register/start").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());
		signatureChallengeRepository.delete(result);

		DeviceKey deviceKey = result.getDeviceKey();
		Assertions.assertNotNull(deviceKey);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void register_start_exceedLimit_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/device-keys/register/start").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());
		signatureChallengeRepository.delete(result);

		DeviceKey deviceKey = result.getDeviceKey();
		Assertions.assertNotNull(deviceKey);
		AppUser user = deviceKey.getUser();
		deviceKeyRepository.delete(deviceKey);

		// delete remaining old deviceKey
		DeviceKey oldDeviceKey = deviceKeyRepository.findByUserAndDeviceId(user, "AP3A.240617.008").orElse(null);
		Assertions.assertNotNull(oldDeviceKey);
		deviceKeyRepository.delete(oldDeviceKey);
	}

	@Test
	public void register_finished_noPin_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/device-keys/register/finish").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());
		DeviceKey deviceKey = result.getDeviceKey();
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void register_finished_signatureExpired_error() throws Exception {
		mockMvc.perform(put("/device-keys/register/finish").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getDescription()));

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		DeviceKey deviceKey = result.getDeviceKey();
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void register_finished_signatureVerified_error() throws Exception {
		mockMvc.perform(put("/device-keys/register/finish").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getDescription()));

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		DeviceKey deviceKey = result.getDeviceKey();
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void register_finished_invalidSignature_error() throws Exception {
		mockMvc.perform(put("/device-keys/register/finish").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.SIGNATURE_NOT_VERIFIED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.SIGNATURE_NOT_VERIFIED.getDescription()));

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		DeviceKey deviceKey = result.getDeviceKey();
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void register_finished_withPin_success() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setPin(AesCryptoUtil.basicEncrypt("111111"));
		userRepository.save(user);

		MvcResult mvcResult = mockMvc
				.perform(put("/device-keys/register/finish").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());
		DeviceKey deviceKey = result.getDeviceKey();

		UserKeyRequest userKeyRequest = userKeyRequestRepository.findById("f50cb6af-a975-482d-a07e-ab47dfa70822")
				.orElse(null);
		Assertions.assertNotNull(userKeyRequest);
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKeyRequest.getStatus());
		userKeyRequestRepository.delete(userKeyRequest);
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void register_finished_blankKey_error() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setPin(AesCryptoUtil.basicEncrypt("111111"));
		userRepository.save(user);

		mockMvc.perform(put("/device-keys/register/finish").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_KEY.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_KEY.getDescription()));

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());
		DeviceKey deviceKey = result.getDeviceKey();

		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void assertion_startWithExpiredScheduled_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/device-keys/assertion/start").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			SignatureChallenge result = signatureChallengeRepository.findAll().stream()
					.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
							.equals("deviceKeyIntegrationTest"))
					.findFirst().orElse(null);
			Assertions.assertNotNull(result);
			Assertions.assertTrue(result.getExpired());
			signatureChallengeRepository.delete(result);

			DeviceKey deviceKey = result.getDeviceKey();
			Assertions.assertNotNull(deviceKey);
			deviceKeyRepository.delete(deviceKey);
		});
	}

	@Test
	public void assertion_start_publicKeyNull_error() throws Exception {
		mockMvc.perform(post("/device-keys/assertion/start").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.DEVICE_REGISTRATION_NOT_FINISHED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.DEVICE_REGISTRATION_NOT_FINISHED.getDescription()));

		AppUser user = appUserRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		DeviceKey deviceKe = deviceKeyRepository.findByUserAndDeviceId(user, "deviceKeyIntegrationTest").orElse(null);
		Assertions.assertNotNull(deviceKe);
		deviceKeyRepository.delete(deviceKe);
	}

	@Test
	public void assertion_finished_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/device-keys/assertion/finish").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());
		DeviceKey deviceKey = result.getDeviceKey();
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void assertion_verify_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/device-keys/assertion/verify").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		KeyResponse keyResponse = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<KeyResponse>() {
				});
		Assertions.assertNotNull(keyResponse);

		SignatureChallenge result = signatureChallengeRepository.findAll().stream()
				.filter(signatureChallenge -> signatureChallenge.getDeviceKey().getDeviceId()
						.equals("deviceKeyIntegrationTest"))
				.findFirst().orElse(null);
		Assertions.assertNotNull(result);
		Assertions.assertFalse(result.getExpired());

		UserKeyRequest userKeyRequest = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest1 -> userKeyRequest1.getKeyValue().contains(keyResponse.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(userKeyRequest);
		Assertions.assertEquals(RequestKeyStatus.PENDING, userKeyRequest.getStatus());
		userKeyRequestRepository.delete(userKeyRequest);

		DeviceKey deviceKey = result.getDeviceKey();
		signatureChallengeRepository.delete(result);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	public void getAllDeviceKeys_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/device-keys").header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<DeviceKeyDTO> responses = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<List<DeviceKeyDTO>>() {
				});
		Assertions.assertNotNull(responses);

		List<DeviceKeyDTO> expected = getExpectation(new TypeReference<List<DeviceKeyDTO>>() {
		});
		Assertions.assertEquals(expected, responses);

		DeviceKey deviceKey = deviceKeyRepository.findAll().stream()
				.filter(deviceKey1 -> USER_ID.equals(deviceKey1.getUser().getId())).findFirst().orElse(null);
		Assertions.assertNotNull(deviceKey);
		deviceKeyRepository.delete(deviceKey);
	}

	private String getCustomUserToken() {
		Path userAccessSource = Paths.get("src/test/resources/" + getBasePath().getDefaultPath() + "redis/token.json");
		UserToken userToken = jsonUtil.convertValueFromJson(userAccessSource.toFile(), UserToken.class);
		userTokenRepository.save(userToken);
		return tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(), userToken.getId());
	}

}
