package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

class PinIT extends BaseIntegrationTest {

	private static final String PIN_USER_ID = "647f1e1a-92a4-4592-aa9f-447e2b590979";

	private static final String PIN_USER_KEY_REQUEST_ID = "4592cb4f-3af3-44b7-a94e-cfe1d14d011b";

	private static final String PIN_EMAIL = "pinuser@localhost";

	private static final String PIN_PHONE_NUMBER = "+601122334489";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private UserKeyRequestRepository userKeyRequestRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void cleanUp() {
		userKeyRequestRepository.findById(PIN_USER_KEY_REQUEST_ID).ifPresent(userKeyRequestRepository::delete);
		cleanUpHelper.cleanUpUser(Collections.singletonList(PIN_USER_ID));
	}

	@Test
	void validate_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/pin/validate").content(getRequest())
						.header("Authorization", "Bearer " + getUserSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		KeyResponse keyResponse = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<KeyResponse>() {
				});

		Assertions.assertNotNull(keyResponse);

		UserKeyRequest expected = userKeyRequestRepository.findAll().stream()
				.filter(userKeyRequest -> userKeyRequest.getKeyValue().equals(keyResponse.getKey())).findFirst()
				.orElse(null);
		Assertions.assertNotNull(expected);
		userKeyRequestRepository.delete(expected);
	}

	@Test
	void validate_invalidKeyRequest_error() throws Exception {
		mockMvc.perform(post("/pin/validate").content(getRequest())
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())

				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.INVALID_PASSCODE_VERIFICATION_REQUEST_TYPE.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.INVALID_PASSCODE_VERIFICATION_REQUEST_TYPE.getDescription()));
	}

	@Test
	void resetPin_success() throws Exception {
		mockMvc.perform(put("/pin/reset").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		Optional<UserKeyRequest> userKeyRequestOpt = userKeyRequestRepository.findById(PIN_USER_KEY_REQUEST_ID);
		Assertions.assertTrue(userKeyRequestOpt.isPresent());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKeyRequestOpt.get().getStatus());
	}

	@Test
	void resetPin_email_success() throws Exception {
		mockMvc.perform(put("/pin/reset").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		Optional<UserKeyRequest> userKeyRequestOpt = userKeyRequestRepository.findById(PIN_USER_KEY_REQUEST_ID);
		Assertions.assertTrue(userKeyRequestOpt.isPresent());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKeyRequestOpt.get().getStatus());

		userKeyRequestRepository.findById("0f38d1e5-dfd7-426b-b708-530d89f2e7e5")
				.ifPresent(userKeyRequestRepository::delete);
	}

	@Test
	void changePin_success() throws Exception {
		UserKeyRequest userKeyRequest = userKeyRequestRepository.findById(PIN_USER_KEY_REQUEST_ID).orElse(null);
		Assertions.assertNotNull(userKeyRequest);
		userKeyRequest.setType(RequestKeyType.VERIFICATION_CHANGE_PIN);
		userKeyRequest.setStatus(RequestKeyStatus.PENDING);
		userKeyRequestRepository.save(userKeyRequest);
		mockMvc.perform(put("/pin").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();

		Optional<UserKeyRequest> userKeyRequestOpt = userKeyRequestRepository.findById(PIN_USER_KEY_REQUEST_ID);
		Assertions.assertTrue(userKeyRequestOpt.isPresent());
		Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKeyRequestOpt.get().getStatus());
	}

	@Test
	void createPin_success() throws Exception {
		mockMvc.perform(post("/pin").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();
	}

	@Test
	void createPin_alreadySet_error() throws Exception {
		User user = userRepository.findById(PIN_USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setPin(AesCryptoUtil.basicEncrypt("111111"));
		userRepository.save(user);
		mockMvc.perform(post("/pin").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.PIN_ALREADY_CREATED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.PIN_ALREADY_CREATED.getDescription()));
	}

	private String getCustomUserToken() {
		Path userAccessSource = Paths.get("src/test/resources/" + getBasePath().getDefaultPath() + "redis/token.json");
		UserToken userToken = jsonUtil.convertValueFromJson(userAccessSource.toFile(), UserToken.class);
		userTokenRepository.save(userToken);
		return tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(), userToken.getId());
	}

}
