package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.TermConditionAndPrivacyPolicyDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.service.impl.*;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.feign.client.NotificationFeignClient;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.exception.GlobalExceptionHandler;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Disabled
@WebMvcTest(value = AccountController.class,
		excludeAutoConfiguration = { OAuth2ClientAutoConfiguration.class, SecurityAutoConfiguration.class })
@ContextConfiguration(classes = { AccountController.class, GlobalExceptionHandler.class })
@MockBeans(@MockBean(classes = KafkaTemplate.class))
public class AccountControllerTest {

	@Autowired
	MockMvc mockMvc;

	@Autowired
	ObjectMapper objectMapper;

	@MockBean
	AccountServiceImpl accountService;

	@MockBean
	NotificationFeignClient notificationFeignClient;

	@MockBean
	UserServiceImpl userService;

	@MockBean
	AuthServiceImpl authService;

	@MockBean
	UserIntegrationServiceImpl userIntegrationService;

	@MockBean
	ValidationServiceImpl validationService;

	@MockBean
	AesCryptoUtil aesCryptoUtil;

	User mockUserRegistration;

	UserKeyRequest mockUserKeyRequest;

	SignUpRequest mockSignUpRequest;

	UpdateProfileRequest mockUpdateProfileRequest;

	UpdatePasswordResetRequest mockUpdatePasswordResetRequest;

	UpdatePasswordResetMobileRequest mockUpdatePasswordResetMobileRequest;

	UpdateMobileRequest mockUpdateMobileRequest;

	UpdatePhoneVerificationRequest mockUpdatePhoneVerificationRequest;

	ChangePasswordRequest mockChangePasswordRequest;

	@BeforeEach
	public void setup() {
		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		mockUserRegistration = new User();
		mockUserRegistration.setFullName("tester");
		mockUserRegistration.setUsername("+************");
		mockUserRegistration.setLoginType(LoginTypeEnum.USER);
		mockUserRegistration.setPhoneCountry("+62");

		mockUserKeyRequest = new UserKeyRequest();
		mockUserKeyRequest.setKeyValue("testkey_test_key");

		mockSignUpRequest = new SignUpRequest();
		mockSignUpRequest.setAge(19);
		mockSignUpRequest.setPassword("12345678");
		mockSignUpRequest.setPhoneCountry("+62");
		mockSignUpRequest.setPhoneNumber("8000000000");
		mockSignUpRequest.setEmail("<EMAIL>");
		mockSignUpRequest.setKey("key");
		mockSignUpRequest.setFullName("tester");
		mockSignUpRequest.setValue("value");

		mockUpdateProfileRequest = new UpdateProfileRequest();
		mockUpdateProfileRequest.setEmail("<EMAIL>");
		mockUpdateProfileRequest.setFullName("tester");

		mockUpdatePasswordResetRequest = new UpdatePasswordResetRequest();
		mockUpdatePasswordResetRequest.setUsername("+************");
		mockUpdatePasswordResetRequest.setKey("key");
		mockUpdatePasswordResetRequest.setPassword("12345678");

		mockUpdatePasswordResetMobileRequest = new UpdatePasswordResetMobileRequest();
		mockUpdatePasswordResetMobileRequest.setValue("value");
		mockUpdatePasswordResetMobileRequest.setKey("key");
		mockUpdatePasswordResetMobileRequest.setPassword("password");
		mockUpdatePasswordResetMobileRequest.setUsername("<EMAIL>");

		mockUpdateMobileRequest = new UpdateMobileRequest();
		mockUpdateMobileRequest.setValue("value");
		mockUpdateMobileRequest.setKey("key");
		mockUpdateMobileRequest.setPhoneCountry("+62");
		mockUpdateMobileRequest.setPhoneNumber("8000000");

		mockUpdatePhoneVerificationRequest = new UpdatePhoneVerificationRequest();
		mockUpdatePhoneVerificationRequest.setKey("key");
		mockUpdatePhoneVerificationRequest.setValue("value");

		mockChangePasswordRequest = new ChangePasswordRequest();
		mockChangePasswordRequest.setCurrentPassword("current");
		mockChangePasswordRequest.setNewPassword("newpassword");
	}

	@AfterEach
	public void clean() {
		Mockito.reset(accountService);
	}

	@Test
	public void testRequestOTPUsernamePhoneNumberSuccess() throws Exception {
		Mockito.when(
				accountService.requestOTPByPhoneNumber(anyString(), any(RequestKeyType.class), any(DeliveryType.class)))
				.thenReturn("S3CR3TK3Y");

		mockMvc.perform(post("/account/request-otp").contentType(MediaType.APPLICATION_JSON).content("""
				{
					"username": "shellrean",
					"requestKeyType": "REGISTRATION",
					"usernameType": "PHONE_NUMBER"
				}
				""")).andExpect(status().isOk()).andExpect(jsonPath("$.key").value("S3CR3TK3Y"));

		Mockito.verify(accountService, Mockito.times(1)).requestOTPByPhoneNumber(Mockito.anyString(),
				Mockito.any(RequestKeyType.class), DeliveryType.SMS);
	}

	@Test
	public void testRequestOTPUsernameEmailSuccess() throws Exception {
		Mockito.when(accountService.requestOTPByEmail(Mockito.any())).thenReturn("S3CR3TK3Y");

		mockMvc.perform(post("/account/request-otp").contentType(MediaType.APPLICATION_JSON).content("""
				{
					"username": "shellrean",
					"requestKeyType": "PASSWORD_RESET",
					"usernameType": "EMAIL"
				}
				""")).andExpect(status().isOk()).andExpect(jsonPath("$.key").value("S3CR3TK3Y"));

		Mockito.verify(accountService, Mockito.times(1)).requestOTPByEmail(Mockito.any());
	}

	@Test
	public void testRequestOTPUsernameEmailError() throws Exception {
		mockMvc.perform(post("/account/request-otp").contentType(MediaType.APPLICATION_JSON).content("""
				{
					"username": "shellrean",
					"requestKeyType": "REGISTRATION",
					"usernameType": "EMAIL"
				}
				""")).andExpect(status().is(501)).andExpect(jsonPath("$.errorCode").value("GBL0009"));
	}

	@Test
	public void testVerifyMobileOTPPhoneNumberSuccess() throws Exception {
		mockMvc.perform(get("/account/verify-otp").param("username", "+60**********").param("key", "")
				.param("value", "").param("requestKeyType", "REGISTRATION").param("usernameType", "PHONE_NUMBER"))
				.andExpect(status().isNoContent());

		Mockito.verify(accountService, Mockito.times(1)).verifyMobileOTP(anyString(), anyString(), anyString(),
				any(RequestKeyType.class), any(UsernameType.class));
	}

	@Test
	public void testVerifyMobileOTPEmailSuccess() throws Exception {
		mockMvc.perform(get("/account/verify-otp").param("username", "<EMAIL>").param("key", "")
				.param("value", "").param("requestKeyType", "REGISTRATION").param("usernameType", "EMAIL"))
				.andExpect(status().isNoContent());

		Mockito.verify(accountService, Mockito.times(1)).verifyMobileOTP(anyString(), anyString(), anyString(),
				any(RequestKeyType.class), any(UsernameType.class));
	}

	@Test
	public void verifyMobileOTP_Success() throws Exception {
		Mockito.doNothing().when(accountService).verifyMobileOTP(any(), any(), any(), any(), any());

		mockMvc.perform(MockMvcRequestBuilders
				.get("/account/verify-otp?key=key&value=value&username=&requestKeyType=REGISTRATION&usernameType=EMAIL")
				.with(csrf()).with(user("user"))).andDo(print()).andExpect(status().isNoContent());
	}

	@Test
	public void registerAccount_Success() throws Exception {
		Mockito.doNothing().when(validationService).validateEmailBlacklist(any());
		Mockito.doNothing().when(accountService).checkIfUserExists(any());
		Mockito.when(accountService.registerUser(any())).thenReturn(mockUserRegistration);

		mockMvc.perform(post("/account/register").with(csrf()).with(user("user"))
				.content(objectMapper.writeValueAsString(mockSignUpRequest)).contentType(MediaType.APPLICATION_JSON))
				.andDo(print()).andExpect(status().isNoContent());
	}

	// @Test
	// public void updateProfile_Success() throws Exception {
	// Mockito.when(accountService.getCurrentUser()).thenReturn(mockUserRegistration);
	// Mockito.doNothing().when(validationService).validateEmailBlacklist(any());
	// Mockito.doNothing().when(accountService).checkIfEmailExists(any());
	// Mockito.doNothing().when(validationService).validateUpdateUser(any(), any());
	// Mockito.when(userIntegrationService.save(any())).thenReturn(mockUserRegistration);
	//
	// mockMvc.perform(MockMvcRequestBuilders.put("/account/update-profile").with(csrf()).with(user("user"))
	// .contentType(MediaType.APPLICATION_JSON)
	// .content(objectMapper.writeValueAsString(mockUpdateProfileRequest))
	// .header(HttpHeaders.AUTHORIZATION, "Bearer
	// Token")).andDo(print()).andExpect(status().isOk())
	// .andExpect(jsonPath("$.fullName").value("tester"))
	// .andExpect(jsonPath("$.username").value("+************"));
	// }
	//
	// @Test
	// public void testUpdateProfileEmptyDobSuccess() throws Exception {
	// Mockito.when(accountService.getCurrentUser()).thenReturn(mockUserRegistration);
	// Mockito.doNothing().when(validationService).validateEmailBlacklist(any());
	// Mockito.doNothing().when(accountService).checkIfEmailExists(any());
	// Mockito.doNothing().when(validationService).validateUpdateUser(any(), any());
	// Mockito.when(userIntegrationService.save(any())).thenReturn(mockUserRegistration);
	//
	// mockMvc.perform(MockMvcRequestBuilders.put("/account/update-profile").contentType(MediaType.APPLICATION_JSON)
	// .content("""
	// {
	// "dob": ""
	// }
	// """)).andDo(print()).andExpect(status().isOk())
	// .andExpect(jsonPath("$.fullName").value("tester"))
	// .andExpect(jsonPath("$.username").value("+************"));
	// }
	//
	// @Test
	// public void testUpdateProfileFilledDobSuccess() throws Exception {
	// Mockito.when(accountService.getCurrentUser()).thenReturn(mockUserRegistration);
	// Mockito.doNothing().when(validationService).validateEmailBlacklist(any());
	// Mockito.doNothing().when(accountService).checkIfEmailExists(any());
	// Mockito.doNothing().when(validationService).validateUpdateUser(any(), any());
	// Mockito.when(userIntegrationService.save(any())).thenReturn(mockUserRegistration);
	//
	// mockMvc.perform(MockMvcRequestBuilders.put("/account/update-profile").contentType(MediaType.APPLICATION_JSON)
	// .content("""
	// {
	// "dob": "2000-12-17"
	// }
	// """)).andDo(print()).andExpect(status().isOk())
	// .andExpect(jsonPath("$.fullName").value("tester"))
	// .andExpect(jsonPath("$.username").value("+************"));
	// }

	@Test
	@WithMockUser(username = "moneyx")
	public void updateResetPassword_Success() throws Exception {
		Mockito.when(accountService.completePasswordResetEmail(any())).thenReturn(mockUserKeyRequest);
		Mockito.when(accountService.findByUsernameInputIgnoreCaseAndLoginTypeAndActiveTrue(any(), any()))
				.thenReturn(mockUserRegistration);
		Mockito.doNothing().when(accountService).sendEmailPasswordUpdated(any());

		mockMvc.perform(
				MockMvcRequestBuilders.put("/account/reset-password-email").contentType(MediaType.APPLICATION_JSON)
						.content(objectMapper.writeValueAsString(mockUpdatePasswordResetRequest)))
				.andDo(print()).andExpect(status().isNoContent());
	}

	@Test
	public void updateResetMobile_Success() throws Exception {
		Mockito.when(accountService.completeUpdateMobile(any())).thenReturn(mockUserKeyRequest);

		mockMvc.perform(MockMvcRequestBuilders.put("/account/reset-mobile").with(csrf()).with(user("user"))
				.contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(mockUpdateMobileRequest))
				.header(HttpHeaders.AUTHORIZATION, "Bearer token")).andDo(print()).andExpect(status().isNoContent());
	}

	@Test
	public void getAccount_Success() throws Exception {
		Mockito.when(accountService.getCurrentUser()).thenReturn(mockUserRegistration);

		mockMvc.perform(MockMvcRequestBuilders.get("/account").with(csrf()).with(user("user"))
				.header(HttpHeaders.AUTHORIZATION, "Bearer token").contentType(MediaType.APPLICATION_JSON))
				.andDo(print()).andExpect(status().isOk())
				.andExpect(jsonPath("$.username").value(mockUserRegistration.getUsername()));
	}

	@Test
	public void getRefNoByUsernameAndLoginTypeAndAccessType_Success() throws Exception {
		Mockito.when(accountService.getUserRefNo(any(), any(), any())).thenReturn("test");

		mockMvc.perform(MockMvcRequestBuilders.get("/account/ref-no?accessType=ADMIN&loginType=ADMIN&username=name")
				.with(csrf()).with(user("user")).contentType(MediaType.APPLICATION_JSON)
				.header(HttpHeaders.AUTHORIZATION, "Bearer token")).andDo(print()).andExpect(status().isOk())
				.andExpect(jsonPath("$").value("test"));
	}

	// @Test
	// @WithMockUser(username = "moneyx")
	// public void requestPhoneVerification_Success() throws Exception {
	// Mockito.when(accountService.requestPhoneVerification(anyBoolean(),
	// any())).thenReturn(mockUserKeyRequest);
	// Mockito.when(accountService.findByUsernameInputIgnoreCaseAndLoginTypeAndActiveTrue(any(),
	// any()))
	// .thenReturn(mockUserRegistration);
	// Mockito.doNothing().when(accountService).sendSmsVerification(any(), any());
	//
	// mockMvc.perform(MockMvcRequestBuilders.get("/account/verify-phone")).andDo(print()).andExpect(status().isOk());
	// }
	//
	// @Test
	// public void updatePhoneVerification_Success() throws Exception {
	// Mockito.doNothing().when(accountService).completePhoneVerification(any(),
	// anyBoolean());
	//
	// mockMvc.perform(MockMvcRequestBuilders.put("/account/verify-phone").contentType(MediaType.APPLICATION_JSON)
	// .content(objectMapper.writeValueAsString(mockUpdatePhoneVerificationRequest))).andDo(print())
	// .andExpect(status().isNoContent());
	// }

	@Test
	public void changePassword_Success() throws Exception {
		Mockito.when(accountService.getCurrentUser()).thenReturn(mockUserRegistration);
		Mockito.doNothing().when(accountService).changePassword(any(), any());
		Mockito.doNothing().when(accountService).sendEmailPasswordUpdated(any());

		mockMvc.perform(MockMvcRequestBuilders.put("/account/change-password").with(csrf()).with(user("user"))
				.contentType(MediaType.APPLICATION_JSON).header(HttpHeaders.AUTHORIZATION, "Bearer token")
				.content(objectMapper.writeValueAsString(mockChangePasswordRequest))).andDo(print())
				.andExpect(status().isNoContent());

	}

	@Test
	public void getAccountId() throws Exception {
		// CurrentUserIdResponse currentUserIdResponseMock = new
		// CurrentUserIdResponse("id", "refNo", true);
		// Mockito.when(accountService.getCurrentUserIdAndRefNo()).thenReturn(currentUserIdResponseMock);
		//
		// mockMvc.perform(MockMvcRequestBuilders.get("/account/id").with(csrf()).with(user("user"))
		// .contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk())
		// .andExpect(jsonPath("$.id").value("id"));
	}

	@Test
	public void testRequestOtpResetMobileSuccess() throws Exception {
		User user = new User();
		Mockito.when(accountService.getCurrentUser()).thenReturn(user);

		UserKeyRequest userKeyRequest = new UserKeyRequest();
		userKeyRequest.setKeyValue("MGE_qIIqj");
		Mockito.when(accountService.requestUpdateMobileOTP(Mockito.anyString(), DeliveryType.SMS))
				.thenReturn(userKeyRequest);

		mockMvc.perform(post("/account/request-otp/reset-mobile").contentType(MediaType.APPLICATION_JSON).content("""
					{
						"phoneCountry": "+60",
						"phoneNumber": "**********",
						"password": "me-pass"
					}
				""")).andExpect(status().isOk()).andExpect(jsonPath("$").value("MGE"));

		Mockito.verify(accountService, Mockito.times(1)).getCurrentUser();
		Mockito.verify(authService, Mockito.times(1)).checkPassword(Mockito.any(User.class), Mockito.anyString());
		Mockito.verify(accountService, Mockito.times(1)).checkIfUserExists(Mockito.any(User.class));
		Mockito.verify(accountService, Mockito.times(1)).requestUpdateMobileOTP(Mockito.anyString(), Mockito.any());
		Mockito.verify(accountService, Mockito.times(1)).sendSmsVerification(Mockito.any(UserKeyRequest.class),
				Mockito.anyString());
	}

	@Test
	public void testCheckTokenSuccess() throws Exception {
		mockMvc.perform(get("/account/check-jwt-token")).andExpect(status().isNoContent());
	}

	@Test
	@WithMockUser(username = "**********")
	public void testGetSecretKeySuccess() throws Exception {
		Mockito.when(accountService.getSecretKey(anyString(), anyString())).thenReturn("pXWIvYwMFHe");

		mockMvc.perform(get("/account/secret-key").header("api-key", "6VI8073FRVPPO5KALYND"))
				.andExpect(status().isOk());

		Mockito.verify(accountService, Mockito.times(1)).getSecretKey(anyString(), anyString());
	}

	@Test
	public void testDeleteAccountProcessSuccess() throws Exception {
		User user = new User();
		Mockito.when(accountService.getCurrentUser()).thenReturn(user);

		mockMvc.perform(put("/account/delete-process")).andExpect(status().isNoContent());

		Mockito.verify(accountService, Mockito.times(1)).getCurrentUser();
		Mockito.verify(accountService, Mockito.times(1)).deleteProcess(any(AppUser.class));
		Mockito.verify(userIntegrationService, Mockito.times(1)).save(any(User.class));
	}

	@Test
	public void testValidateTermConditionAndPrivacyPolicySuccess() throws Exception {
		User user = new User();
		Mockito.when(accountService.getCurrentUser()).thenReturn(user);

		TermConditionAndPrivacyPolicyDTO termConditionAndPrivacyPolicyDTO = new TermConditionAndPrivacyPolicyDTO();
		termConditionAndPrivacyPolicyDTO.setPrivacyPolicyRequireUpdate(true);
		termConditionAndPrivacyPolicyDTO.setTermAndConditionRequireUpdate(false);

		Mockito.when(accountService.validateTermConditionAndPrivacyPolicy(any(User.class)))
				.thenReturn(termConditionAndPrivacyPolicyDTO);

		mockMvc.perform(get("/account/validate-term-condition-privacy-policy")).andExpect(status().isOk())
				.andExpect(jsonPath("$.privacyPolicyRequireUpdate").value(true))
				.andExpect(jsonPath("$.termAndConditionRequireUpdate").value(false));

		Mockito.verify(accountService, Mockito.times(1)).getCurrentUser();
		Mockito.verify(accountService, Mockito.times(1)).validateTermConditionAndPrivacyPolicy(any(User.class));
	}

	@Test
	public void testUpdateTermConditionAndPrivacyPolicySuccess() throws Exception {
		User user = new User();
		Mockito.when(accountService.getCurrentUser()).thenReturn(user);

		mockMvc.perform(put("/account/update-term-condition-privacy-policy")).andExpect(status().isNoContent());

		Mockito.verify(accountService, Mockito.times(1)).getCurrentUser();
		Mockito.verify(accountService, Mockito.times(1)).updateTermConditionAndPrivacyPolicyVersion(any(User.class),
				any(TermConditionAndPrivacyPolicyDTO.class));
	}

	@Test
	public void testUpdateAddressesSuccess() throws Exception {
		User user = new User();
		Mockito.when(accountService.getCurrentUser()).thenReturn(user);

		User updated = new User();
		updated.setAddress1("Mars");
		Mockito.when(userIntegrationService.save(any(User.class))).thenReturn(updated);

		mockMvc.perform(put("/account/update-address").contentType(MediaType.APPLICATION_JSON).content("""
										{
											"address1": "Mars"
										}
				""")).andExpect(status().isOk()).andExpect(jsonPath("$.address1").value("Mars"));

		Mockito.verify(accountService, Mockito.times(1)).getCurrentUser();
		Mockito.verify(userIntegrationService, Mockito.times(1)).save(any(User.class));
	}

}
