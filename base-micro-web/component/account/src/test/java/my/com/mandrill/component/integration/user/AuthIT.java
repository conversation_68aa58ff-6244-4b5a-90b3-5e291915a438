package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.response.SignInResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.extension.HashKeyGenerator;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.AppUserRepository;
import my.com.mandrill.component.repository.jpa.DeviceKeyRepository;
import my.com.mandrill.component.repository.jpa.SignatureChallengeRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.feign.dto.AuthenticateDTO;
import my.com.mandrill.utilities.general.constant.AccessTypeEnum;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.dto.request.HashRequest;
import my.com.mandrill.utilities.general.util.HashUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

public class AuthIT extends BaseIntegrationTest {

	private static final String CUSTOM_USER_ID = "32792dd4-6df6-4dbf-9ffe-174d011cd058";

	private static final String USER_USERNAME = "+60123213123";

	private static final String USER_ID = "0ab87a42-995f-416d-8fef-e1bd8b3882c2";

	private static final String USER_PIN_PASSWORD = "111111";

	private static final String USER_PASSWORD = "Mandrill@123";

	private static final String USER_REF_NO = "8888888";

	private static final String SIGNATURE_CHALLANGE = "b5848420-14ac-4b58-a767-31e8cba3b168";

	private static final String DEVICE_KEY = "c16929a5-aace-4846-ae38-2a3af0e86830";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private DeviceKeyRepository deviceKeyRepository;

	@Autowired
	private SignatureChallengeRepository signatureChallengeRepository;

	@Autowired
	private AppUserRepository appUserRepository;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void clearEach() {
		cleanUpHelper.cleanUpUser(Collections.singletonList(CUSTOM_USER_ID));
	}

	@Test
	void authenticateUser_userMobilePin_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.MOBILE.name())
						.queryParam("loginType", LoginTypeEnum.USER.name())
						.queryParam("passcodeType", PasscodeType.PIN.name())
						.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignInResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertTrue(StringUtils.isNotBlank(response.getRefreshToken()));
		Assertions.assertEquals("0ab87a42-995f-416d-8fef-e1bd8b3882c2", response.getUserId());
	}

	@Test
	void authenticateUser_nullUsernameAndPassword_success() throws Exception {
		mockMvc.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.MOBILE.name())
				.queryParam("loginType", LoginTypeEnum.USER.name()).queryParam("passcodeType", PasscodeType.PIN.name())
				.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.USERNAME_AND_PASSWORD_MUST_BE_FILLED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.USERNAME_AND_PASSWORD_MUST_BE_FILLED.getDescription()));
	}

	@Test
	void authenticateUser_nullUsername_success() throws Exception {
		mockMvc.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.MOBILE.name())
				.queryParam("loginType", LoginTypeEnum.USER.name()).queryParam("passcodeType", PasscodeType.PIN.name())
				.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.USERNAME_MUST_BE_FILLED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.USERNAME_MUST_BE_FILLED.getDescription()));
	}

	@Test
	void authenticateUser_nullPassword_success() throws Exception {
		mockMvc.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.MOBILE.name())
				.queryParam("loginType", LoginTypeEnum.USER.name()).queryParam("passcodeType", PasscodeType.PIN.name())
				.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.PASSWORD_MUST_BE_FILLED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.PASSWORD_MUST_BE_FILLED.getDescription()));
	}

	@Test
	void authenticateUser_userMobilePassword_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.MOBILE.name())
						.queryParam("loginType", LoginTypeEnum.USER.name())
						.queryParam("passcodeType", PasscodeType.PASSWORD.name())
						.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignInResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertTrue(StringUtils.isNotBlank(response.getRefreshToken()));
		Assertions.assertEquals("0ab87a42-995f-416d-8fef-e1bd8b3882c2", response.getUserId());
	}

	@Test
	void authenticateUser_email_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.EMAIL.name())
						.queryParam("loginType", LoginTypeEnum.USER.name())
						.queryParam("passcodeType", PasscodeType.PASSWORD.name())
						.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignInResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertTrue(StringUtils.isNotBlank(response.getRefreshToken()));
		Assertions.assertEquals("0ab87a42-995f-416d-8fef-e1bd8b3882c2", response.getUserId());
	}

	@Test
	void authenticateUser_email_emptyPermission_error() throws Exception {
		mockMvc.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.EMAIL.name())
				.queryParam("loginType", LoginTypeEnum.USER.name())
				.queryParam("passcodeType", PasscodeType.PASSWORD.name())
				.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.USER_NOT_VERIFIED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.USER_NOT_VERIFIED.getDescription()));
	}

	@Test
	void authenticateUser_email_notExist_error() throws Exception {
		mockMvc.perform(post("/authenticate").queryParam("accessType", AccessTypeEnum.EMAIL.name())
				.queryParam("loginType", LoginTypeEnum.USER.name())
				.queryParam("passcodeType", PasscodeType.PASSWORD.name())
				.queryParam("deviceId", UUID.randomUUID().toString()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getDescription()));
	}

	@Test
	void createSignatureToken_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/signature-challenge").content(getRequest())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		SignInResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertTrue(StringUtils.isNotBlank(response.getRefreshToken()));
		Assertions.assertEquals("0ab87a42-995f-416d-8fef-e1bd8b3882c2", response.getUserId());

		DeviceKey deviceKey = deviceKeyRepository.findById(DEVICE_KEY).orElse(null);
		Assertions.assertNotNull(deviceKey);

		SignatureChallenge signatureChallenge = signatureChallengeRepository.findById(SIGNATURE_CHALLANGE).orElse(null);
		Assertions.assertNotNull(signatureChallenge);
		Assertions.assertTrue(signatureChallenge.getVerified());
		signatureChallengeRepository.delete(signatureChallenge);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	void createSignatureToken_loginAttemptExceed_error() throws Exception {
		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		user.setLoginFailAttempt(3);
		user = userRepository.save(user);

		mockMvc.perform(
				post("/authenticate/signature-challenge").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.LOGIN_ATTEMPT_EXCEED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.LOGIN_ATTEMPT_EXCEED.getDescription()));

		DeviceKey deviceKey = deviceKeyRepository.findById(DEVICE_KEY).orElse(null);
		Assertions.assertNotNull(deviceKey);

		SignatureChallenge signatureChallenge = signatureChallengeRepository.findById(SIGNATURE_CHALLANGE).orElse(null);
		Assertions.assertNotNull(signatureChallenge);
		Assertions.assertFalse(signatureChallenge.getVerified());
		signatureChallengeRepository.delete(signatureChallenge);
		deviceKeyRepository.delete(deviceKey);

		user.setLoginFailAttempt(0);
		userRepository.save(user);
	}

	@Test
	void createSignatureToken_expired_error() throws Exception {
		mockMvc.perform(
				post("/authenticate/signature-challenge").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getDescription()));

		DeviceKey deviceKey = deviceKeyRepository.findById(DEVICE_KEY).orElse(null);
		Assertions.assertNotNull(deviceKey);

		SignatureChallenge signatureChallenge = signatureChallengeRepository.findById(SIGNATURE_CHALLANGE).orElse(null);
		Assertions.assertNotNull(signatureChallenge);
		signatureChallengeRepository.delete(signatureChallenge);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	void createSignatureToken_verified_error() throws Exception {
		mockMvc.perform(
				post("/authenticate/signature-challenge").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED.getDescription()));

		DeviceKey deviceKey = deviceKeyRepository.findById(DEVICE_KEY).orElse(null);
		Assertions.assertNotNull(deviceKey);

		SignatureChallenge signatureChallenge = signatureChallengeRepository.findById(SIGNATURE_CHALLANGE).orElse(null);
		Assertions.assertNotNull(signatureChallenge);
		signatureChallengeRepository.delete(signatureChallenge);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	void createSignatureToken_notVerified_error() throws Exception {
		mockMvc.perform(
				post("/authenticate/signature-challenge").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeEnum.SIGNATURE_NOT_VERIFIED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.SIGNATURE_NOT_VERIFIED.getDescription()));

		DeviceKey deviceKey = deviceKeyRepository.findById(DEVICE_KEY).orElse(null);
		Assertions.assertNotNull(deviceKey);

		SignatureChallenge signatureChallenge = signatureChallengeRepository.findById(SIGNATURE_CHALLANGE).orElse(null);
		Assertions.assertNotNull(signatureChallenge);
		signatureChallengeRepository.delete(signatureChallenge);
		deviceKeyRepository.delete(deviceKey);
	}

	@Test
	void authenticatePassword_email_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		AuthenticateDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertFalse(response.getPermissions().isEmpty());
		Assertions.assertEquals("8888888", response.getRefNo());
	}

	@Test
	void authenticatePassword_phoneNumber_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		AuthenticateDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertFalse(response.getPermissions().isEmpty());
		Assertions.assertEquals("*************", response.getRefNo());

		// cleanUpHelper.cleanUpAccountIT("32792dd4-6df6-4dbf-9ffe-174d011cd058");
	}

	@Test
	void authenticatePassword_phoneNumber_notVerified_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		AuthenticateDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(response.getPermissions().isEmpty());
		Assertions.assertEquals("*************", response.getRefNo());

		// cleanUpHelper.cleanUpAccountIT("32792dd4-6df6-4dbf-9ffe-174d011cd058");
	}

	@Test
	void authenticatePassword_email_notVerified_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		AuthenticateDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(response.getPermissions().isEmpty());
		Assertions.assertEquals("*************", response.getRefNo());

		// cleanUpHelper.cleanUpAccountIT("32792dd4-6df6-4dbf-9ffe-174d011cd058");
	}

	@Test
	void authenticatePassword_deleted_error() throws Exception {
		mockMvc.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getDescription()));

		// cleanUpHelper.cleanUpAccountIT("32792dd4-6df6-4dbf-9ffe-174d011cd058");
	}

	@Test
	void authenticatePassword_notActive_error() throws Exception {
		mockMvc.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isUnauthorized());

		// cleanUpHelper.cleanUpAccountIT("32792dd4-6df6-4dbf-9ffe-174d011cd058");
	}

	@Test
	void authenticatePassword_pin_exceed_error() throws Exception {
		User user = userRepository.findById("0ab87a42-995f-416d-8fef-e1bd8b3882c2").orElse(null);
		Assertions.assertNotNull(user);
		user.setLoginFailAttempt(10);
		user = userRepository.save(user);
		mockMvc.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.PIN_ATTEMPT_EXCEED.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.PIN_ATTEMPT_EXCEED.getDescription()));

		user.setLoginFailAttempt(0);
		userRepository.save(user);
	}

	@Test
	void authenticatePassword_pin_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		AuthenticateDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertFalse(response.getPermissions().isEmpty());
		Assertions.assertEquals("8888888", response.getRefNo());
	}

	@Test
	void authenticatePassword_invalidPin_error() throws Exception {
		mockMvc.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeGlobalEnum.INVALID_PIN.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.INVALID_PIN.getDescription()));

		AppUser appUser = appUserRepository.findById(CUSTOM_USER_ID).orElse(null);
		Assertions.assertNotNull(appUser);
		Assertions.assertEquals(1, appUser.getLoginFailAttempt());
	}

	@Test
	void authenticatePassword_mobile_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		AuthenticateDTO response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertFalse(response.getPermissions().isEmpty());
		Assertions.assertEquals("8888888", response.getRefNo());
	}

	@Test
	void authenticatePassword_notfound_error() throws Exception {
		mockMvc.perform(post("/authenticate/password").content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST.getDescription()));

	}

	@Test
	void checkPassword_success() throws Exception {
		mockMvc.perform(post("/authenticate/check-password").header("Authorization", "Bearer " + getUserSecretToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
	}

	@Test
	void checkPassword_invalid_error() throws Exception {
		mockMvc.perform(post("/authenticate/check-password").header("Authorization", "Bearer " + getUserSecretToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_PASSWORD.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.INVALID_PASSWORD.getDescription()));
	}

	@Test
	void hash_success() throws Exception {
		HashKeyGenerator hashKeyGenerator = new HashKeyGenerator();
		HashRequest request = HashRequest.builder().timestamp(hashKeyGenerator.getInstant())
				.identifier(hashKeyGenerator.getIdentifier()).hash(hashKeyGenerator.getHash()).build();

		mockMvc.perform(post("/authenticate/hash").content(jsonUtil.convertToString(request))
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isNoContent())
				.andReturn();
	}

	@Test
	void hash_expired_error() throws Exception {
		HashKeyGenerator hashKeyGenerator = new HashKeyGenerator();
		HashRequest request = HashRequest.builder().timestamp(Instant.now().minus(Duration.ofDays(5)))
				.identifier(hashKeyGenerator.getIdentifier()).hash(hashKeyGenerator.getHash()).build();

		mockMvc.perform(post("/authenticate/hash").content(jsonUtil.convertToString(request))
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_HASH.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_HASH.getDescription()));
	}

	@Test
	void hash_invalidHash_error() throws Exception {
		HashKeyGenerator hashKeyGenerator = new HashKeyGenerator();
		HashRequest request = HashRequest.builder().timestamp(hashKeyGenerator.getInstant())
				.identifier(hashKeyGenerator.getIdentifier()).hash(HashUtil.sha512Hex("invalid",
						hashKeyGenerator.getApiKey(), hashKeyGenerator.getInstant().toString()))
				.build();

		mockMvc.perform(post("/authenticate/hash").content(jsonUtil.convertToString(request))
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_HASH.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_HASH.getDescription()));
	}

	@Test
	void hash_notFoundIdentifier_error() throws Exception {
		HashKeyGenerator hashKeyGenerator = new HashKeyGenerator();
		HashRequest request = HashRequest
				.builder().timestamp(hashKeyGenerator.getInstant()).identifier("custom-identifier").hash(HashUtil
						.sha512Hex("invalid", hashKeyGenerator.getApiKey(), hashKeyGenerator.getInstant().toString()))
				.build();

		mockMvc.perform(post("/authenticate/hash").content(jsonUtil.convertToString(request))
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INVALID_HASH.getCode()))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.message").value(ErrorCodeEnum.INVALID_HASH.getDescription()));
	}

	@Test
	void livechat_token_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/authenticate/livechat-token").header("Authorization", "Bearer " + getUserSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		SignInResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertTrue(StringUtils.isBlank(response.getRefreshToken()));
		Assertions.assertEquals("0ab87a42-995f-416d-8fef-e1bd8b3882c2", response.getUserId());
	}

	@Test
	void infobip_token_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(post("/authenticate/infobip-inbox-token")
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		SignInResponse response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertTrue(StringUtils.isBlank(response.getRefreshToken()));
		Assertions.assertEquals("0ab87a42-995f-416d-8fef-e1bd8b3882c2", response.getUserId());
	}

}
