package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.Token;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.model.ExtendAccessTokenDTO;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.TokenRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.general.util.ObjectMapperUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

public class RefreshTokenIT extends BaseIntegrationTest {

	private static final String USER_ID = "ace64cac-7e7a-4f69-9318-2cc697638016";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private TokenRepository tokenRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void tearDown() {
		cleanUpHelper.cleanUpRefreshTokenIT(Collections.singletonList(USER_ID));
	}

	@Test
	void extends_success() throws Exception {
		Token token = tokenRepository.findById("ad8b5ede-6992-462b-8303-5730d2cba470").orElse(null);
		Assertions.assertNotNull(token);

		MvcResult mvcResult = mockMvc
				.perform(post("/token/extends")
						.content(ObjectMapperUtil.MAPPER.writeValueAsString(
								ExtendAccessTokenDTO.builder().refreshToken(token.getRefreshToken()).build()))
						.header("Authorization", "Bearer " + getUserSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		AuthenticateResultDTO response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<AuthenticateResultDTO>() {
				});
		Assertions.assertNotNull(response);

		Assertions.assertTrue(StringUtils.isNotBlank(response.getRefreshToken()));
		Assertions.assertTrue(StringUtils.isNotBlank(response.getAccessToken()));
		Assertions.assertEquals(USER_ID, response.getUserId());
	}

	@Test
	void extends_refreshTokenNotFound_unauthorized() throws Exception {
		Token token = tokenRepository.findById("ad8b5ede-6992-462b-8303-5730d2cba470").orElse(null);
		Assertions.assertNotNull(token);

		mockMvc.perform(post("/token/extends")
				.content(ObjectMapperUtil.MAPPER.writeValueAsString(
						ExtendAccessTokenDTO.builder().refreshToken(UUID.randomUUID().toString()).build()))
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isUnauthorized());
	}

	@Test
	void extends_refreshTokenExpired_unauthorized() throws Exception {
		Token token = tokenRepository.findById("ad8b5ede-6992-462b-8303-5730d2cba470").orElse(null);
		Assertions.assertNotNull(token);
		token.setRefreshTokenValidity(Instant.now().minus(3, ChronoUnit.DAYS));
		tokenRepository.save(token);

		mockMvc.perform(post("/token/extends")
				.content(ObjectMapperUtil.MAPPER.writeValueAsString(
						ExtendAccessTokenDTO.builder().refreshToken(token.getRefreshToken()).build()))
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isUnauthorized());
	}

}
