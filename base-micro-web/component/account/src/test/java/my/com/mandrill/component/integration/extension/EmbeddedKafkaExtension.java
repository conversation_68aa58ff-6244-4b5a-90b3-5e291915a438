package my.com.mandrill.component.integration.extension;

import lombok.extern.slf4j.Slf4j;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.utils.ContainerTestUtils;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class EmbeddedKafkaExtension implements BeforeAllCallback, ExtensionContext.Store.CloseableResource {

	private static final Lock LOCK = new ReentrantLock();

	private static volatile boolean started = false;

	private EmbeddedKafkaBroker embeddedKafka;

	@Override
	public void beforeAll(ExtensionContext context) {
		LOCK.lock();
		try {
			if (!started) {
				started = true;
				context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL).put("embedded-kafka", this);

				log.info("Starting Embedded Kafka...");
				embeddedKafka = new EmbeddedKafkaBroker(1, true, 1)
						.brokerProperty("listeners", "PLAINTEXT://localhost:0").brokerProperty("port", "0");

				embeddedKafka.afterPropertiesSet();
				System.setProperty("spring.embedded.kafka.brokers", embeddedKafka.getBrokersAsString());

				log.info("Started Embedded Kafka on: {}", embeddedKafka.getBrokersAsString());
				ConfigurableApplicationContext appContext = (ConfigurableApplicationContext) SpringExtension
						.getApplicationContext(context);

				KafkaListenerEndpointRegistry registry = appContext.getBean(KafkaListenerEndpointRegistry.class);
				registry.getListenerContainers().forEach(container -> {
					ContainerTestUtils.waitForAssignment(container, container.getAssignedPartitions().size());
					Awaitility.await().atMost(10, TimeUnit.SECONDS)
							.until(() -> container.isRunning() && !container.isContainerPaused());
					log.info("Kafka Listener {} assigned", container.getListenerId());
				});

			}
			else {
				log.info("Embedded Kafka already started, skipping...");
			}
		}
		finally {
			LOCK.unlock();
		}
	}

	@Override
	public void close() {
		if (embeddedKafka != null) {
			embeddedKafka.destroy();
		}
	}

}
