package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.domain.Token;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.TokenRepository;
import my.com.mandrill.utilities.feign.dto.GlobalSystemConfigurationDTO;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.service.RedisService;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class UserActivityConsumerIT extends BaseIntegrationTest {

	private static final String TOKEN_ID = "b4383982-160a-4c57-8d57-e61a3155a0e4";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private TokenRepository tokenRepository;

	@Autowired
	private RedisService redisService;

	@BeforeEach
	public void setUp() {
		redisService.putToHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION,
				GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED.getCode(),
				GlobalSystemConfigurationDTO.builder().id(UUID.randomUUID().toString())
						.code(GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED.getCode()).active(true)
						.value(Boolean.TRUE.toString()).build());
	}

	@AfterEach
	public void cleanUp() {
		redisService.putToHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION,
				GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED.getCode(),
				GlobalSystemConfigurationDTO.builder().id(UUID.randomUUID().toString())
						.code(GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED.getCode()).active(true)
						.value(Boolean.FALSE.toString()).build());
	}

	@Test
	void listenUserActivity_success() throws Exception {
		kafkaTemplate.send(KafkaTopic.USER_ACTIVITY_TRANSACTION, getRequest());
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Token token = tokenRepository.findByIdFetchAll(TOKEN_ID).orElse(null);
			Assertions.assertNotNull(token);
			Assertions.assertEquals(TOKEN_ID, token.getId());
			Assertions.assertFalse(token.getTokenTransactions().isEmpty());
			tokenRepository.delete(token);
		});
	}

	@Test
	void listenUserActivity_invalidTokenId_success() throws Exception {
		String invalidTokenId = "ea141d9b-214f-43a3-b040-57cf45646767";
		kafkaTemplate.send(KafkaTopic.USER_ACTIVITY_TRANSACTION, getRequest());
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Token token = tokenRepository.findByIdFetchAll(invalidTokenId).orElse(null);
			Assertions.assertNull(token);
		});
	}

}
