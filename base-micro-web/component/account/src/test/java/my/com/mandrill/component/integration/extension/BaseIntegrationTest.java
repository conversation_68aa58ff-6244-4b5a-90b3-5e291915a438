package my.com.mandrill.component.integration.extension;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Json;
import com.github.tomakehurst.wiremock.common.Metadata;
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.github.tomakehurst.wiremock.stubbing.StubMapping;
import com.github.tomakehurst.wiremock.stubbing.StubMappingCollection;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.AccountComponentApplication;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.feign.dto.GlobalSystemConfigurationDTO;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.dto.request.CacheRequest;
import my.com.mandrill.utilities.general.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.stream.Stream;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestPropertySource(locations = { "/application.yml"
// ,"/junit-platform.properties" //TODO: To avoid longer execution time, please run this
// too everytime add new test to make sure your test result or data is not conflict with
// another
})
@SpringBootTest(classes = AccountComponentApplication.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ExtendWith(WireMockExtension.class)
@ExtendWith(DatabaseExtension.class)
@ExtendWith(SpringExtension.class)
// @ExtendWith(EmbeddedKafkaExtension.class)
public class BaseIntegrationTest {

	private static final ObjectMapper MAPPER = new ObjectMapper().registerModule(new JavaTimeModule());

	private static final String BASE_DEFAULT_PATH = "/json/%s-%s/%s/default/";

	private static final String DEFAULT_API_PATH = BASE_DEFAULT_PATH + "api-mock/";

	private static final String DEFAULT_DB_PATH = BASE_DEFAULT_PATH + "db/";

	private static final String BASE_SCENARIO_PATH = "/json/%s-%s/%s/%s/";

	private static final String API_PATH = BASE_SCENARIO_PATH + "api-mock/";

	private static final String DB_PATH = BASE_SCENARIO_PATH + "db/";

	private static final String EXPECTATION_PATH = BASE_SCENARIO_PATH + "expectation/";

	private BasePath basePath;

	protected HashKeyGenerator hashKeyGenerator;

	protected Set<StubMapping> stubMappings = new HashSet<>();

	@Autowired
	private ApplicationContext applicationContext;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private RedisService redisService;

	protected String adminSecretToken;

	protected String userSecretToken;

	protected String DEFAULT_USER_ID = "0ab87a42-995f-416d-8fef-e1bd8b3882c2";

	@BeforeEach
	public void setup(TestInfo testInfo) throws Exception {
		basePath = constructScenarioPath(testInfo);
		stubMappings.forEach(WireMockExtension.wireMockServer::removeStub);
		stubMappings.clear();

		// init database data
		insertGlobalDatabaseDataPerTestClass();
		insertSpecificDatabasePerScenario();

		// init default admin and user token
		addDefaultAdminToken();
		addDefaultUserToken();

		// init mock
		stubDefaultMockPerTestClass();
		stubSpeficiMockPerScenario();

		Path sessionManagement = Paths.get("src/test/resources/json/global/redis/session-management-enabled.json");
		try {
			GlobalSystemConfigurationDTO sessionManagementConfig = MAPPER.readValue(sessionManagement.toFile(),
					GlobalSystemConfigurationDTO.class);
			redisService.putToHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION,
					GlobalSystemConfigurationEnum.SESSION_MANAGEMENT_ENABLED.getCode(), sessionManagementConfig);
		}
		catch (IOException e) {
			log.error(e.getMessage(), e);
		}

		hashKeyGenerator = new HashKeyGenerator();
		String cacheKey = CacheKey.PUBLIC_AUTHENTICATION_MODULE_CACHE_KEY.formatted(hashKeyGenerator.getIdentifier());
		my.com.mandrill.utilities.feign.dto.PublicAuthenticationDTO dto = new my.com.mandrill.utilities.feign.dto.PublicAuthenticationDTO();
		dto.setApiKey(hashKeyGenerator.getApiKey());
		redisService.putToValue(cacheKey, dto, Duration.ofMinutes(30));
	}

	private void insertGlobalDatabaseDataPerTestClass() {
		Path dbFolderBasePath = Paths.get("src/test/resources/" + basePath.getDefaultDb());
		try (Stream<Path> files = Files.list(dbFolderBasePath)) {
			boolean isFolderEmpty = files.noneMatch(path -> path.getFileName().toString().endsWith(".sql"));

			if (isFolderEmpty) {
				log.info("Folder is empty or contains no SQL files.");
			}
			else {
				Files.list(dbFolderBasePath).filter(path -> path.getFileName().toString().endsWith(".sql"))
						.sorted(Comparator.comparing(path -> path.getFileName().toString()))
						.forEach(this::processJsonFile);
			}
		}
		catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void insertSpecificDatabasePerScenario() {
		Path dbFolderBasePath = Paths.get("src/test/resources/" + basePath.getDb());
		try (Stream<Path> files = Files.list(dbFolderBasePath)) {
			boolean isFolderEmpty = files.noneMatch(path -> path.getFileName().toString().endsWith(".sql"));

			if (isFolderEmpty) {
				log.info("Folder is empty or contains no SQL files.");
			}
			else {
				Files.list(dbFolderBasePath).filter(path -> path.getFileName().toString().endsWith(".sql"))
						.sorted(Comparator.comparing(path -> path.getFileName().toString()))
						.forEach(this::processJsonFile);
			}
		}
		catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void addDefaultAdminToken() {
		Path token = Paths.get("src/test/resources/json/global/redis/admin-access-token.json");
		try {
			UserToken userToken = MAPPER.readValue(token.toFile(), UserToken.class);
			userTokenRepository.save(userToken);
			adminSecretToken = tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(),
					userToken.getId());
		}
		catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void addDefaultUserToken() {
		Path userAccessSource = Paths.get("src/test/resources/json/global/redis/user-access-token.json");
		try {
			UserToken userToken = MAPPER.readValue(userAccessSource.toFile(), UserToken.class);
			userTokenRepository.save(userToken);
			userSecretToken = tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(),
					userToken.getId());
		}
		catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void stubDefaultMockPerTestClass() {
		Path mockFolderPath = Paths.get("src/test/resources/" + basePath.getDefaultApiMock());
		try (Stream<Path> files = Files.list(mockFolderPath)) {
			boolean isFolderEmpty = files.noneMatch(path -> path.getFileName().toString().endsWith(".json"));

			if (isFolderEmpty) {
				log.info("Folder is empty or contains no mock files.");
			}
			else {
				Files.list(mockFolderPath).filter(path -> path.getFileName().toString().endsWith(".json"))
						.forEach(path -> processMockFile(path, "test-class"));

				stubMappings.forEach(stubMapping -> {
					if (stubMapping.getMetadata().get("identifier").equals("test-class")) {
						WireMockExtension.wireMockServer.addStubMapping(stubMapping);
					}
				});
			}
		}
		catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void stubSpeficiMockPerScenario() {
		Path mockFolderPath = Paths.get("src/test/resources/" + basePath.getApiMock());

		if (!Files.exists(mockFolderPath) || !Files.isDirectory(mockFolderPath)) {
			log.info("Mock folder does not exist or is not a directory: {}", mockFolderPath);
			return;
		}

		try (Stream<Path> files = Files.list(mockFolderPath)) {
			List<Path> jsonFiles = files.filter(path -> path.getFileName().toString().endsWith(".json")).toList();

			if (jsonFiles.isEmpty()) {
				log.info("Folder is empty or contains no mock files.");
				return;
			}

			for (Path path : jsonFiles) {
				processMockFile(path, "test-cases");
			}

			stubMappings.stream()
					.filter(stubMapping -> "test-cases".equals(stubMapping.getMetadata().get("identifier")))
					.forEach(WireMockExtension.wireMockServer::addStubMapping);
		}
		catch (IOException e) {
			log.error("Error while reading mock files from path: {}", mockFolderPath, e);
		}
	}

	@AfterAll
	public void clearAll() {
		stubMappings.forEach(WireMockExtension.wireMockServer::removeStubMapping);
	}

	@AfterEach
	public void clearEach() {
		redisService.deleteCache(CacheRequest.builder().patterns(Collections.singleton("appuser:*")).build());

		stubMappings.forEach(this::verifyStub);
	}

	@BeforeEach
	public void beforeEach() {
		redisService.deleteCache(CacheRequest.builder().patterns(Collections.singleton("appuser:*")).build());
		redisService.putToHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION,
				GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED.getCode(),
				GlobalSystemConfigurationDTO.builder().id(UUID.randomUUID().toString())
						.code(GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED.getCode()).active(true)
						.value(Boolean.FALSE.toString()).build());
	}

	private void verifyStub(StubMapping mapping) {
		List<ServeEvent> serveEvents = WireMockExtension.wireMockServer.removeServeEventsForStubsMatchingMetadata(
				WireMock.matchingJsonPath("$.stubId", WireMock.equalTo(mapping.getMetadata().getString("stubId"))))
				.getServeEvents();
	}

	@SneakyThrows
	private void processMockFile(Path path, String identifier) {
		StubMappingCollection stubMappingCollection = Json.read(Files.readString(path), StubMappingCollection.class);
		for (StubMapping mapping : stubMappingCollection.getMappingOrMappings()) {
			mapping.setDirty(false);
			mapping.setMetadata(Optional.ofNullable(mapping.getMetadata()).orElseGet(Metadata::new));
			mapping.getMetadata().putIfAbsent("stubId", UUID.randomUUID().toString());
			mapping.getMetadata().putIfAbsent("identifier", identifier);
			stubMappings.add(mapping);
		}

	}

	private BasePath constructScenarioPath(TestInfo testInfo) {
		List<String> strings = Arrays.asList(testInfo.getTestClass().get().getCanonicalName().split("\\."));
		String packageName = strings.get(strings.size() - 3);
		String directoryName = String
				.join("-", StringUtils.splitByCharacterTypeCamelCase(strings.get(strings.size() - 2))).toLowerCase();
		String className = Character.toLowerCase(strings.get(strings.size() - 1).charAt(0))
				+ strings.get(strings.size() - 1).substring(1).replace("IT", "");
		String scenarioName = testInfo.getTestMethod().get().getName();
		return BasePath.builder().defaultPath(String.format(BASE_DEFAULT_PATH, directoryName, packageName, className))
				.defaultDb(String.format(DEFAULT_DB_PATH, directoryName, packageName, className))
				.defaultApiMock(String.format(DEFAULT_API_PATH, directoryName, packageName, className))
				.apiMock(String.format(API_PATH, directoryName, packageName, className, scenarioName))
				.baseScenario(String.format(BASE_SCENARIO_PATH, directoryName, packageName, className, scenarioName))
				.db(String.format(DB_PATH, directoryName, packageName, className, scenarioName))
				.expectation(String.format(EXPECTATION_PATH, directoryName, packageName, className, scenarioName))
				.build();
	}

	private void processJsonFile(Path filePath) {
		try {
			jdbcTemplate.execute(Files.readString(filePath));
		}
		catch (Exception e) {
			log.error("Error when execute SQL File" + e.getMessage());
		}
	}

	@SneakyThrows
	public <T> T getExpectation(TypeReference<T> typeReference) {
		Path expectationPath = Paths.get("src/test/resources/" + getBasePath().getExpectation() + "/expected.json");
		return MAPPER.readValue(Files.readString(expectationPath), typeReference);
	}

	@SneakyThrows
	public String getExpectationAsString() {
		Path expectationPath = Paths.get("src/test/resources/" + getBasePath().getExpectation() + "/expected.json");
		return Files.readString(expectationPath);
	}

	@SneakyThrows
	public String getRequest() {
		Path requestPath = Paths.get("src/test/resources/" + getBasePath().getBaseScenario() + "/request.json");
		return Files.readString(requestPath);
	}

}
