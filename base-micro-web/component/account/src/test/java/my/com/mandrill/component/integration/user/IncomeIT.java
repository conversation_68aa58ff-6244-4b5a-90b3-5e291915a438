package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.EmploymentTypeRepository;
import my.com.mandrill.component.repository.jpa.IncomeRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.ObjectMapperUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.annotation.After;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

class IncomeIT extends BaseIntegrationTest {

	private String PHONE_NUMBER = "+601122334486";

	private String EMAIL = "incomeit@localhost";

	private String USER_ID = "3f91dcf5-c6b2-44ed-b703-844417751dc8";

	private String REF_NO = "2023071008499";

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private IncomeRepository incomeRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void tearDown() {
		cleanUpHelper.clearIncomeIT(Collections.singletonList(USER_ID));
	}

	@Test
	void createIncome_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(
						post("/incomes").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
								.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTO = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				IncomeDTO.class);
		Assertions.assertNotNull(incomeDTO);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(incomeDTO.getId());
		Assertions.assertEquals(expected.getIncomeType(), incomeDTO.getIncomeType());
		Assertions.assertEquals(expected.getEmploymentType(), incomeDTO.getEmploymentType());
		Assertions.assertEquals(expected.getMonthlyIncomeAmount(), incomeDTO.getMonthlyIncomeAmount());
		Assertions.assertTrue(expected.getIsReminder());

	}

	@Test
	void createIncome_noReminder_andNotFulltime_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(
						post("/incomes").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
								.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTO = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				IncomeDTO.class);
		Assertions.assertNotNull(incomeDTO);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(incomeDTO.getId());
		Assertions.assertEquals(expected.getIncomeType(), incomeDTO.getIncomeType());
		Assertions.assertEquals(expected.getEmploymentType(), incomeDTO.getEmploymentType());
		Assertions.assertEquals(expected.getMonthlyIncomeAmount(), incomeDTO.getMonthlyIncomeAmount());
		Assertions.assertTrue(expected.getIsReminder());

	}

	@Test
	void createIncome_noFullTimeFixedSalary_and_notification_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(
						post("/incomes").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
								.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTO = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				IncomeDTO.class);
		Assertions.assertNotNull(incomeDTO);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(incomeDTO.getId());
		Assertions.assertEquals(expected.getIncomeType(), incomeDTO.getIncomeType());
		Assertions.assertEquals(expected.getEmploymentType(), incomeDTO.getEmploymentType());
		Assertions.assertEquals(expected.getMonthlyIncomeAmount(), incomeDTO.getMonthlyIncomeAmount());
		Assertions.assertTrue(expected.getIsReminder());

	}

	@Test
	void createIncome_exist_error() throws Exception {
		mockMvc.perform(post("/incomes").content(getRequest()).header("Authorization", "Bearer " + getCustomUserToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INCOME_EXISTS.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.INCOME_EXISTS.getDescription()));

	}

	@Test
	void updateIncome_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTO = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				IncomeDTO.class);
		Assertions.assertNotNull(incomeDTO);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(incomeDTO.getId());
		Assertions.assertEquals(expected.getIncomeType(), incomeDTO.getIncomeType());
		Assertions.assertEquals(expected.getEmploymentType(), incomeDTO.getEmploymentType());
		Assertions.assertEquals(expected.getMonthlyIncomeAmount(), incomeDTO.getMonthlyIncomeAmount());
		Assertions.assertTrue(expected.getIsReminder());
	}

	@Test
	void updateIncome_noEmployeeType_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTO = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				IncomeDTO.class);
		Assertions.assertNotNull(incomeDTO);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(incomeDTO.getId());
		Assertions.assertEquals(expected.getIncomeType(), incomeDTO.getIncomeType());
		Assertions.assertEquals(expected.getEmploymentType(), incomeDTO.getEmploymentType());
		Assertions.assertEquals(expected.getMonthlyIncomeAmount(), incomeDTO.getMonthlyIncomeAmount());
		Assertions.assertTrue(expected.getIsReminder());
	}

	@Test
	void updateIncome_noEmployeeType_anotherSimilarIncomeExist_error() throws Exception {
		mockMvc.perform(put("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7").content(getRequest())
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value(ErrorCodeEnum.INCOME_EXISTS.getCode()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.message")
						.value(ErrorCodeEnum.INCOME_EXISTS.getDescription()));
	}

	@Test
	void updateIncome_notFullTimeEmployee_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTO = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				IncomeDTO.class);
		Assertions.assertNotNull(incomeDTO);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(incomeDTO.getId());
		Assertions.assertEquals(expected.getIncomeType(), incomeDTO.getIncomeType());
		Assertions.assertEquals(expected.getEmploymentType(), incomeDTO.getEmploymentType());
		Assertions.assertEquals(expected.getMonthlyIncomeAmount(), incomeDTO.getMonthlyIncomeAmount());
		Assertions.assertTrue(expected.getIsReminder());
	}

	@Test
	void getAllSalary_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes").header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<IncomeDTO> incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		List<IncomeDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected.size(), incomeDTOs.size());
		Assertions.assertEquals(expected, incomeDTOs);

	}

	@Test
	void getAllSalary_specificIncomeAndEmpType_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes").queryParam("incomeTypeId", "241fd082-51b3-444f-8e51-0feebe6e01bb")
						.queryParam("employmentTypeId", "ea035370-ada2-11ed-a899-0242ac120002")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<IncomeDTO> incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		List<IncomeDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected.size(), incomeDTOs.size());
		Assertions.assertEquals(expected, incomeDTOs);

	}

	@Test
	void findById_populateReminder_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected, incomeDTOs);
	}

	@Test
	void findById_populateReminderNotFound_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected, incomeDTOs);
	}

	@Test
	void findById_notPopulateReminder_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7")
						.queryParam("populateReminder", Boolean.FALSE.toString())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected, incomeDTOs);
	}

	@Test
	void findSalary_populateReminder_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/salary").queryParam("employmentTypeId", "ea035370-ada2-11ed-a899-0242ac120002")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected, incomeDTOs);
	}

	@Test
	void findSalary_notPopulateReminder_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/salary").queryParam("populateReminder", Boolean.FALSE.toString())
						.queryParam("employmentTypeId", "ea035370-ada2-11ed-a899-0242ac120002")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		IncomeDTO incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		IncomeDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected, incomeDTOs);
	}

	@Test
	void deleteSalary_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(delete("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7")
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertTrue(CollectionUtils.isEmpty(user.getIncomes()));
	}

	@Test
	void deleteSalary_noIncome_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(delete("/incomes/8ae68ee1-90fe-4ee7-9818-57e4d92d8ab7")
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertTrue(CollectionUtils.isEmpty(user.getIncomes()));
	}

	@Test
	void getCurrentSalary_integration_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/integrations/current").header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<IncomeDTO> incomeDTOs = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(incomeDTOs);

		List<IncomeDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);

		Assertions.assertEquals(expected.size(), incomeDTOs.size());
		Assertions.assertEquals(expected, incomeDTOs);

	}

	@Test
	void private_findSalaryByUserId_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/incomes/private/find-salary").queryParam("userId", USER_ID)
						.header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ")
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		BigDecimal response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);
		Assertions.assertEquals(0, response.compareTo(new BigDecimal(8000)));

	}

	private String getCustomUserToken() {
		Path userAccessSource = Paths.get("src/test/resources/" + getBasePath().getDefaultPath() + "redis/token.json");
		UserToken userToken = jsonUtil.convertValueFromJson(userAccessSource.toFile(), UserToken.class);
		userTokenRepository.save(userToken);
		return tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(), userToken.getId());
	}

}
