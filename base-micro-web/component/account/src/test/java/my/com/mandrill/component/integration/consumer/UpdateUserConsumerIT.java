package my.com.mandrill.component.integration.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.consumer.UpdateUserConsumer;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class UpdateUserConsumerIT extends BaseIntegrationTest {

	private static final String USER_ID = "43d96176-2cb9-4eee-aafb-ca2daaf0f0bb";

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private UpdateUserConsumer consumer;

	@AfterEach
	void tearDown() {
		userRepository.findById(USER_ID).ifPresent(userRepository::delete);
	}

	@Test
	void updateAllFields_nric() {
		UpdateUserKafkaRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		this.sendToKafka(USER_ID, request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(request.getId(), userOptional.get().getId());
			Assertions.assertEquals(request.getRefNo(), userOptional.get().getRefNo());
			Assertions.assertEquals(request.getNric(), userOptional.get().getNric());
			Assertions.assertEquals(request.getPassport(), userOptional.get().getPassport());
			Assertions.assertEquals(request.getArmy(), userOptional.get().getArmy());
			Assertions.assertEquals(request.getDob(), userOptional.get().getDob());
			Assertions.assertEquals(request.getFullName(), userOptional.get().getFullName());
			Assertions.assertEquals(request.getNationality().getId(), userOptional.get().getNationality().getId());
			Assertions.assertEquals(Boolean.FALSE, userOptional.get().getIsNationalityEditable());
			Assertions.assertEquals(Boolean.FALSE, userOptional.get().getIsNricEditable());
			Assertions.assertEquals(request.getEmail(), userOptional.get().getEmail());
			Assertions.assertEquals(request.getEkycVerificationStatus(),
					userOptional.get().getEkycVerificationStatus());
			Assertions.assertEquals(request.getReferralCode(), userOptional.get().getReferralCode());

		});
	}

	@Test
	void updateAllFields_accountDeletion() {
		UpdateUserKafkaRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		this.sendToKafka(USER_ID, request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(request.getId(), userOptional.get().getId());
			Assertions.assertEquals(request.getRefNo(), userOptional.get().getRefNo());
			Assertions.assertEquals(request.getNric(), userOptional.get().getNric());
			Assertions.assertEquals(request.getPassport(), userOptional.get().getPassport());
			Assertions.assertEquals(request.getArmy(), userOptional.get().getArmy());
			Assertions.assertEquals(request.getDob(), userOptional.get().getDob());
			Assertions.assertEquals(request.getFullName(), userOptional.get().getFullName());
			Assertions.assertEquals(request.getNationality().getId(), userOptional.get().getNationality().getId());
			Assertions.assertEquals(Boolean.TRUE, userOptional.get().getIsNationalityEditable());
			Assertions.assertEquals(Boolean.TRUE, userOptional.get().getIsNricEditable());
			Assertions.assertEquals(request.getEmail(), userOptional.get().getEmail());
			Assertions.assertEquals(request.getEkycVerificationStatus(),
					userOptional.get().getEkycVerificationStatus());
			Assertions.assertEquals(request.getReferralCode(), userOptional.get().getReferralCode());

		});
	}

	@Test
	void updateAllFields_nricOriginNull_skipUpdateNric_accountDeletion() {
		UpdateUserKafkaRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		this.sendToKafka(USER_ID, request);
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(request.getId(), userOptional.get().getId());
			Assertions.assertEquals(request.getRefNo(), userOptional.get().getRefNo());
			Assertions.assertEquals(request.getNric(), userOptional.get().getNric());
			Assertions.assertEquals(request.getPassport(), userOptional.get().getPassport());
			Assertions.assertEquals(request.getArmy(), userOptional.get().getArmy());
			Assertions.assertEquals(request.getDob(), userOptional.get().getDob());
			Assertions.assertEquals(request.getFullName(), userOptional.get().getFullName());
			Assertions.assertEquals(request.getNationality().getId(), userOptional.get().getNationality().getId());
			Assertions.assertEquals(Boolean.TRUE, userOptional.get().getIsNationalityEditable());
			Assertions.assertEquals(Boolean.TRUE, userOptional.get().getIsNricEditable());
			Assertions.assertEquals(request.getEmail(), userOptional.get().getEmail());
			Assertions.assertEquals(request.getEkycVerificationStatus(),
					userOptional.get().getEkycVerificationStatus());
			Assertions.assertEquals(request.getReferralCode(), userOptional.get().getReferralCode());
			Assertions.assertEquals(request.getRsmRelationType(), userOptional.get().getRsmRelationType());

		});
	}

	@Test
	void updateAllFields_blankNric_accountDeletion() {
		UpdateUserKafkaRequest request = jsonUtil.convertValueFromJson(getRequest(), new TypeReference<>() {
		});
		this.sendToKafka(USER_ID, request);
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(request.getId(), userOptional.get().getId());
			Assertions.assertEquals(request.getRefNo(), userOptional.get().getRefNo());
			Assertions.assertNull(userOptional.get().getNric());
			Assertions.assertEquals(request.getPassport(), userOptional.get().getPassport());
			Assertions.assertEquals(request.getArmy(), userOptional.get().getArmy());
			Assertions.assertEquals(request.getDob(), userOptional.get().getDob());
			Assertions.assertEquals(request.getFullName(), userOptional.get().getFullName());
			Assertions.assertEquals(request.getNationality().getId(), userOptional.get().getNationality().getId());
			Assertions.assertEquals(Boolean.TRUE, userOptional.get().getIsNationalityEditable());
			Assertions.assertEquals(Boolean.TRUE, userOptional.get().getIsNricEditable());
			Assertions.assertEquals(request.getEmail(), userOptional.get().getEmail());
			Assertions.assertEquals(request.getEkycVerificationStatus(),
					userOptional.get().getEkycVerificationStatus());
			Assertions.assertEquals(request.getReferralCode(), userOptional.get().getReferralCode());

		});
	}

	@Test
	void noUpdate() {
		UpdateUserKafkaRequest request = new UpdateUserKafkaRequest();
		request.setId(USER_ID);
		this.sendToKafka(USER_ID, request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(USER_ID, userOptional.get().getId());
		});

	}

	@Test
	void test_dlt_handler() {
		consumer.handleError("error");
	}

	private void sendToKafka(String userId, Object payload) {
		try {
			kafkaTemplate.send(KafkaTopic.UPDATE_USER_TOPIC, userId, jsonUtil.convertToString(payload));
		}
		catch (Exception e) {
			System.out.println("hohohoh : " + e);
		}
	}

}
