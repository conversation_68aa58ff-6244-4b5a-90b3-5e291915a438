package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.consumer.ProcessBiWeeklyIncompleteSurveyReminderConsumer;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class ProcessBiWeeklyIncompleteConsumerIT extends BaseIntegrationTest {

	private static final String USER_ID = "b3d9f7d1-d907-4ccf-aa3d-89cc54583d2e";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private ProcessBiWeeklyIncompleteSurveyReminderConsumer processBiWeeklyIncompleteSurveyReminderConsumer;

	@Autowired
	private ConsumerFactory<String, String> testConsumerFactory;

	private org.apache.kafka.clients.consumer.Consumer<String, String> schedulerPushNotificationConsumer;

	@BeforeAll
	void setup() {
		schedulerPushNotificationConsumer = testConsumerFactory.createConsumer();
		schedulerPushNotificationConsumer.subscribe(Collections.singletonList(KafkaTopic.SCHEDULER_PUSH_NOTIFICATION));
	}

	@AfterAll
	void tearDown() {
		Duration duration = Duration.ofSeconds(1);
		if (schedulerPushNotificationConsumer != null) {
			schedulerPushNotificationConsumer.unsubscribe();
			schedulerPushNotificationConsumer.close(duration);
		}
	}

	@AfterEach
	void tearEach() {
		userRepository.findById(USER_ID).ifPresent(userRepository::delete);
	}

	@Test
	void createWelcomeNotification_success() {
		kafkaTemplate.send(KafkaTopic.PROCESS_INCOMPLETE_SURVEY_REMINDER,
				jsonUtil.convertToString(List.of("0ab87a42-995f-416d-8fef-e1bd8b3882c2")));
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			ConsumerRecords<String, String> records = schedulerPushNotificationConsumer.poll(Duration.ofSeconds(5));
			Assertions.assertFalse(records.isEmpty());
			int total = 0;
			for (ConsumerRecord<String, String> record : records) {
				if (record.value().contains(USER_ID)) {
					Assertions.assertTrue(record.value().contains(USER_ID));
					Assertions.assertTrue(record.value().contains("6b77f146-ddfd-4722-a3e6-242c829f329d"));
					total++;
					break;
				}
			}
			Assertions.assertEquals(1, total);
		});

	}

	@Test
	void createWelcomeNotification_noProcessedUser_success() {
		kafkaTemplate.send(KafkaTopic.PROCESS_INCOMPLETE_SURVEY_REMINDER,
				jsonUtil.convertToString(List.of("0ab87a42-995f-416d-8fef-e1bd8b3882c2")));
	}

	@Test
	public void dltHandle() {
		processBiWeeklyIncompleteSurveyReminderConsumer.dltHandler("error");
	}

}
