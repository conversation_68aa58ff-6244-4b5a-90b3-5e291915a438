package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

public class KeyRequestIT extends BaseIntegrationTest {

	private static final String KEY_REQ_ID = "8926a79f-b38b-4f41-a4b3-27aea0b41d44";

	private static final String USER_ID = "23874031-6e82-4ed4-a251-d373e33fc633";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserKeyRequestRepository userKeyRequestRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void cleanup() {
		cleanUpHelper.cleanUpUser(Collections.singletonList(USER_ID));
	}

	@Test
	void validate_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(post("/key-requests/private/validate").content(getRequest())
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			UserKeyRequest userKeyRequest = userKeyRequestRepository.findById(KEY_REQ_ID).orElse(null);
			Assertions.assertNotNull(userKeyRequest);
			Assertions.assertEquals(KEY_REQ_ID, userKeyRequest.getId());
			Assertions.assertEquals("userit_keyrequest", userKeyRequest.getKeyValue());
			Assertions.assertEquals(RequestKeyStatus.COMPLETED, userKeyRequest.getStatus());
			userKeyRequestRepository.delete(userKeyRequest);
		});
	}

}
