package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.consumer.UpdateUserAgeScheduler;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.general.dto.model.SchedulerMessaging;
import my.com.mandrill.utilities.general.util.CalculationUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class UpdateUserAgeConsumerIT extends BaseIntegrationTest {

	private static final String USER_ID = "76ec537f-fb57-41bc-afd1-ff56d8f86c59";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private UpdateUserAgeScheduler updateUserAgeScheduler;

	@AfterEach
	public void tearDown() {
		userRepository.findById(USER_ID).ifPresent(userRepository::delete);
	}

	@Test
	void updateAge_success() {
		SchedulerMessaging message = SchedulerMessaging.builder().key(KafkaTopicConfig.UPDATE_APP_USER_AGE)
				.destination(KafkaTopicConfig.UPDATE_APP_USER_AGE).timeJobTriggered(Instant.now()).build();

		kafkaTemplate.send(KafkaTopicConfig.UPDATE_APP_USER_AGE, jsonUtil.convertToString(message));

		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> user = userRepository.findById(USER_ID);
			Assertions.assertTrue(user.isPresent());
			int age = CalculationUtil.calculateAgeByDob(user.get().getDob());
			Assertions.assertEquals(age, user.get().getAge());
		});
	}

	@Test
	void handleError() {
		updateUserAgeScheduler.handleError("error");
	}

}
