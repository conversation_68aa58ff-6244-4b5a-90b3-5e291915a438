package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.ObjectMapperUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

class ExpenseIT extends BaseIntegrationTest {

	private static final String PHONE_NUMBER = "+601122334479";

	private static final String USER_ID = "e4cc2d38-0c4b-4f40-91a0-2b7498cf37cb";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserTokenRepository userTokenRepository;

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@AfterEach
	public void tearDown() throws Exception {
		cleanUpHelper.clearExpenseIT(Collections.singletonList(USER_ID));
	}

	@Test
	void create_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(post("/expenses").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		ExpenseDTO response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				ExpenseDTO.class);
		Assertions.assertNotNull(response);

		ExpenseDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(expected.getExpenseType(), response.getExpenseType());
		Assertions.assertTrue(expected.getIsReminder());
		Assertions.assertEquals(expected.getAmount(), response.getAmount());
	}

	@Test
	void update_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(put("/expenses/68fcf703-15ff-45be-b4cc-ea8b4e81415f").content(getRequest())
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		ExpenseDTO response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				ExpenseDTO.class);
		Assertions.assertNotNull(response);

		ExpenseDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(expected.getExpenseType(), response.getExpenseType());
		Assertions.assertTrue(expected.getIsReminder());
		Assertions.assertEquals(expected.getAmount(), response.getAmount());
	}

	@Test
	void getById_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/expenses/68fcf703-15ff-45be-b4cc-ea8b4e81415f")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		ExpenseDTO response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				ExpenseDTO.class);
		Assertions.assertNotNull(response);

		ExpenseDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(expected.getExpenseType(), response.getExpenseType());
		Assertions.assertTrue(expected.getIsReminder());
		Assertions.assertEquals(expected.getAmount(), response.getAmount());
	}

	@Test
	void getById_reminderNotFound_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/expenses/68fcf703-15ff-45be-b4cc-ea8b4e81415f")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		ExpenseDTO response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				ExpenseDTO.class);
		Assertions.assertNotNull(response);

		ExpenseDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertNotNull(response.getId());
		Assertions.assertEquals(expected.getExpenseType(), response.getExpenseType());
		Assertions.assertFalse(expected.getIsReminder());
		Assertions.assertEquals(expected.getAmount(), response.getAmount());
	}

	@Test
	void findByExpenseType_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/expenses").header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<ExpenseDTO> response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<List<ExpenseDTO>>() {
				});
		;
		Assertions.assertNotNull(response);

		List<ExpenseDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertEquals(expected, response);
	}

	@Test
	void findByExpenseType_specificExpenseType_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/expenses").queryParam("expenseTypeId", "8ac62381-3eda-469a-b177-d6ddfe5970e3")
						.header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<ExpenseDTO> response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<List<ExpenseDTO>>() {
				});
		;
		Assertions.assertNotNull(response);

		List<ExpenseDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertEquals(expected, response);
	}

	@Test
	void integration_allExpense_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/expenses/integrations/current").header("Authorization", "Bearer " + getCustomUserToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<ExpenseDTO> response = ObjectMapperUtil.MAPPER.readValue(mvcResult.getResponse().getContentAsString(),
				new TypeReference<List<ExpenseDTO>>() {
				});
		;
		Assertions.assertNotNull(response);

		List<ExpenseDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertEquals(expected, response);
	}

	@Test
	void delete_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(delete("/expenses/68fcf703-15ff-45be-b4cc-ea8b4e81415f")
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertTrue(CollectionUtils.isEmpty(user.getExpenses()));
	}

	@Test
	void delete_noExpense_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(delete("/expenses/68fcf703-15ff-45be-b4cc-ea8b4e81415f")
				.header("Authorization", "Bearer " + getCustomUserToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		User user = userRepository.findById(USER_ID).orElse(null);
		Assertions.assertNotNull(user);
		Assertions.assertTrue(CollectionUtils.isEmpty(user.getExpenses()));
	}

	private String getCustomUserToken() {
		Path userAccessSource = Paths.get("src/test/resources/" + getBasePath().getDefaultPath() + "redis/token.json");
		UserToken userToken = jsonUtil.convertValueFromJson(userAccessSource.toFile(), UserToken.class);
		userTokenRepository.save(userToken);
		return tokenProvider.createAccessToken(userToken.getUserRefNo(), userToken.getUserId(), userToken.getId());
	}

}
