package my.com.mandrill.component.integration.admin;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.integration.helper.CleanUpHelper;
import my.com.mandrill.component.repository.jpa.AppUserRepository;
import my.com.mandrill.component.repository.jpa.IncomeRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

public class IncomeGroupIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private AppUserRepository userRepository;

	@Autowired
	private IncomeRepository incomeRepository;

	@Autowired
	private CleanUpHelper cleanUpHelper;

	@Test
	void incomeGroup_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(get("/admin/incomes/group").queryParam("startDate", "2090-01-01")
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		Map<String, Map<String, Long>> response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});

		Map<String, Map<String, Long>> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertEquals(expected, response);

		List<AppUser> users = userRepository.findAll().stream().filter(user -> user.getCreatedDate() != null
				&& user.getCreatedDate().isAfter(LocalDateTime.of(2030, 1, 1, 0, 0, 0, 0).toInstant(ZoneOffset.UTC)))
				.toList();

		List<String> userIds = users.stream().map(AuditSection::getId).collect(Collectors.toList());
		cleanUpHelper.cleanUpIncomeGroupIT(userIds);
	}

}
