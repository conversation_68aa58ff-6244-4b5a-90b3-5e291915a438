info:
  project:
    version: #project.version#

spring:
  application:
    name: account-component
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ******************************************************************
    username: moneyx
    password: moneyx
    hikari:
      max-lifetime: 150000
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
      connection-timeout: 10000
      idle-timeout: 600000
  jpa:
    show-sql: false
    properties:
      hibernate:
        default_schema: account
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true
    open-in-view: false
  liquibase:
    default-schema: account
    change-log: classpath:liquibase/master.xml
    enabled: true
  jackson:
    serialization:
      fail-on-empty-beans: false
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: your-secret-id
            client-secret: your-secret-code
            redirect-uri: 'http://localhost:8762/api/account-component/login/oauth2/code/google' #gateway:login/oauth2/code/client
            authorization-grant-type: authorization_code
            scope:
              - email
              - profile
          facebook:
            client-id: your-secret-id
            client-secret: your-secret-code
            redirect-uri: 'http://localhost:8762/api/account-component/login/oauth2/code/google' #gateway:login/oauth2/code/client
            authorization-grant-type: authorization_code
            scope:
              - email
              - public_profile
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRepositoriesAutoConfiguration
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      auto-offset-reset: earliest
  cache:
    redis:
      time-to-live: 1d
  data:
    redis:
      repositories:
        enabled: false
      timeout: 1000ms
      connect-timeout: 1000ms
      host: localhost
      port: 6379
      database: 2
  cloud:
    discovery:
      client:
        simple:
          instances:
            account-component:
              - uri: http://localhost:8999
            common-component:
              - uri: http://localhost:8999
            moneyx-core-component:
              - uri: http://localhost:8999
            bank-component:
              - uri: http://localhost:8999
            property-component:
              - uri: http://localhost:8999
            investment-component:
              - uri: http://localhost:8999
            insurance-component:
              - uri: http://localhost:8999
            retirement-component:
              - uri: http://localhost:8999
            vehicle-component:
              - uri: http://localhost:8999
            utility-component:
              - uri: http://localhost:8999
            notification-component:
              - uri: http://localhost:8999
            promotion-component:
              - uri: http://localhost:8999
            financial-analysis-component:
              - uri: http://localhost:8999
            analytic-component:
              - uri: http://localhost:8999
    gcp:
      credentials:
        location: classpath:bigquery/key.json
      bigquery:
        project-id: moneyx-sit
        credentials:
          location: classpath:bigquery/key.json
        dataset-name: analytics_359784844
      project-id: moneyx-sit

server:
  port: 8901

management:
  endpoints:
    web:
      exposure:
        include:
          - health
          - info
          - prometheus
  health:
    elasticsearch:
      enabled: false

##https://cloud.spring.io/spring-cloud-netflix/reference/html/appendix.html
eureka:
  client:
    enabled: false
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    preferIpAddress: true

##https://cloud.spring.io/spring-cloud-openfeign/reference/html/appendix.html
feign:
  client:
    account-component:
      name: http://localhost:8999
    common-component:
      name: http://localhost:8999
    moneyx-core-component:
      name: http://localhost:8999
    bank-component:
      name: http://localhost:8999
    property-component:
      name: http://localhost:8999
    investment-component:
      name: http://localhost:8999
    insurance-component:
      name: http://localhost:8999
    retirement-component:
      name: http://localhost:8999
    vehicle-component:
      name: http://localhost:8999
    utility-component:
      name: http://localhost:8999
    notification-component:
      name: http://localhost:8999
    promotion-component:
      name: http://localhost:8999
    financial-analysis-component:
      name: http://localhost:8999
    analytic-component:
      name: http://localhost:8999
    config:
      default:
        loggerLevel: basic # default none | basic | headers | full
        connectTimeout: 10000 # default 10 sec
        readTimeout: 60000 # default 60 sec

security:
  jwt:
    # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 128` on your command line to generate
    base64-secret: NOIUaY1qF6H8wmQJzLWmzk8uJfJZfptuCUW2MBq4bSfobb90cTB9hP49nXiMAVUByeuEF1UhWO1Yj19ABWdZCgPcjzC0Q1Qz9qt9gXU44kLqEDmY7/JoRg5c65j31VnITVvjRSCnxj7eyqv093ETnuGy0QBrUCO624Cx7pk5QAQ=
    token-validity-in-seconds: 86400
    token-validity-in-seconds-for-remember-me: 2592000
    token-expiry-enabled: false

base:
  internal-api-key: xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ
  request-uri:
    email-verification: http://localhost:4200/email-verification
    reset-password: http://localhost:4200/reset-password
    open-api-server: http://localhost:8762/account-component
  otp-email: MONEYX OTP <<EMAIL>>
  support-email: MONEYX Support <<EMAIL>>
  oauth2:
    authorized-redirect-uris:
      - http://localhost:4200
  cache:
    ehcache:
      time-to-live-seconds: 30
      max-entries: 100
  api-key: 9ebbc78d-dff5-4876-bb45-13d0a6e9b8e3
  kafka:
    topic:
      update-user:
        partition: 1
        concurrency: 1
      create-or-update-income:
        partition: 1
        concurrency: 10
      send-sms:
        retention: 300

securities:
  crypto:
    key: NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc
    iv: 57ad289f-3def-4c
zendesk:
  jwt:
    kid: testing
    secret: c2VjcmV0a2V5Zm9ydGVzdGluZ3B1cnBvc2Vz
    token-validity-in-hours: 24

infobip:
  jwt:
    kid: 7e1aca5b-28fb-4027-b3d5-7be2b82af448
    secret: 70eaa69442507ca7d6f4522137ff85b7f873a8765ffa0b6585e3eadb73679945262e0ae7a70e33f39c887365c13678082a6d003860812230105ad39dfbf7f884
    application-code: aa33bff4a9708555cc4c28a5e89c074b-5aaeea64-dbc8-4c31-9ed3-2e52120a9df0
    token-validity-in-hours: 24

google:
  recaptcha:
    base-url: http://localhost:8999
    secret-key:

#logging:
#  level:
#    my.com.mandrill: INFO
#    org.hibernate.sql: INFO