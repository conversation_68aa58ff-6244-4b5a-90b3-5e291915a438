{"request": {"method": "GET", "urlPath": "/state/d1389aef-a836-4c73-b63a-f6820ed17ae2"}, "response": {"body": "{\n    \"id\": \"d1389aef-a836-4c73-b63a-f6820ed17ae2\",\n    \"code\": \"S<PERSON>\",\n    \"name\": \"Selangor\",\n    \"description\": null,\n    \"country\": {\n        \"id\": \"316f0d26-ada2-11ed-a899-0242ac120002\",\n        \"code\": \"MY\",\n        \"name\": \"Malaysia\",\n        \"description\": null\n    }\n}", "headers": {"Content-Type": "application/json"}, "status": 200}}