{"email": "accountusercustomit@localhost", "fullName": "update fullname", "address1": "update-address 1", "address2": "update-address 2", "address3": "update-address 3", "postcode": "111111", "country": {"id": "316f0d26-ada2-11ed-a899-0242ac120002", "code": "MY", "name": "Malaysia", "description": null}, "state": {"id": "1c20f7c0-2a3b-4c79-aaaf-bc3f29ddf466", "code": "KUL", "name": "W.P. Kuala Lumpur", "description": null, "country": {"id": "316f0d26-ada2-11ed-a899-0242ac120002", "code": "MY", "name": "Malaysia", "description": null}}, "age": 30, "gender": "Female", "nric": "********", "maritalStatus": "Married", "ethnicity": "Chinese", "religion": "Buddhism", "currency": {"id": "823612b9-ada2-11ed-a899-0242ac120002"}, "epfContribution": 11, "sosco": true, "eis": true, "educationLevel": {"id": "b2b541ad-ab60-11ed-9f68-0242ac120002"}, "employmentType": {"id": "ea035370-ada2-11ed-a899-0242ac120002"}, "occupationGroup": {"id": "2f7201cb-ada3-11ed-a899-0242ac120002"}, "selfEmployedName": "Testing Management Sdn Bhd", "interests": [{"id": "bb4c66b9-328a-4923-bf04-73c5d5255076"}], "financialGoals": [{"id": "ecc22950-ab89-11ed-9f68-0242ac120002"}], "incomes": [{"incomeType": {"id": "4ded659d-ec15-4969-8c63-259fe70b262b"}, "employmentType": {"id": "bba2854b-ab7c-11ed-9f68-0242ac120002"}, "monthlyIncomeAmount": 20000}], "expenses": [{"expenseType": {"id": "f43q3f75-40b0-4c82-8de5-2708523b683e"}, "amount": 5000, "active": true}], "segment": {"id": "b4aef8b4-fd20-4ead-bf6d-3a4dc4913a47"}, "passport": "********", "army": "********", "key": null, "nationality": {"id": "0a1334c0-ada3-11ed-a899-0242ac120002"}}