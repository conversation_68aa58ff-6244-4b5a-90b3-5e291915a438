{"assets": [{"netWorthType": "UTILITY", "value": 1000, "valueType": "ASSET", "label": "saving", "valueFlag": "ON_GOING"}, {"netWorthType": "PROPERTIES", "value": 50000, "valueType": "ASSET"}, {"netWorthType": "VEHICLES", "value": 20000, "valueType": "ASSET"}, {"netWorthType": "RETIREMENT", "value": 100000, "valueType": "ASSET"}, {"netWorthType": "INVESTMENT", "value": 80000, "valueType": "ASSET"}], "liabilities": [], "incomes": [{"netWorthType": "INCOME", "value": 8000.0, "valueType": "INCOME"}], "recurringRepayments": [{"netWorthType": "INSURANCE_MONTHLY_REPAYMENT", "value": 5000, "valueType": "RECURRING_REPAYMENT", "label": "saving", "valueFlag": "ON_GOING"}, {"netWorthType": "CASH_SAVINGS", "value": 1000, "valueType": "ASSET", "label": "saving", "valueFlag": "ON_GOING"}], "insuranceCoverage": [], "totalAsset": 251000, "totalLiability": 0, "totalIncome": 8000.0, "totalRecurringRepayment": 6000, "totalInsuranceCoverage": 0, "netWorth": 251000}