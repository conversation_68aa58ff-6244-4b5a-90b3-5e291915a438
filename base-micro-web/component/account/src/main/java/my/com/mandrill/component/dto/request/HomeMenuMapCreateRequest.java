package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.HomeSectionEnum;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class HomeMenuMapCreateRequest implements Serializable {

	@NotBlank
	private String homeMenuId;

	@NotNull
	private HomeSectionEnum section;

	@NotBlank
	private String homeMenuGroupId;

}
