package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.PermissionDTO;
import my.com.mandrill.component.dto.model.PublicAuthenticationDTO;
import my.com.mandrill.component.dto.model.TermConditionAndPrivacyPolicyDTO;
import my.com.mandrill.component.dto.model.UserDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Supplier;

import static my.com.mandrill.utilities.general.util.RequestUtil.CLIENT_PLATFORM;

@Tag(name = "02-account")
@Slf4j
@RestController
@RequestMapping("/account")
@RequiredArgsConstructor
public class AccountController {

	private final AccountService accountService;

	private final AuthService authService;

	private final ObjectMapper objectMapper;

	private final UserIntegrationService userIntegrationService;

	private final ValidationService validationService;

	private final Map<String, PasscodeProviderService> passcodeProviderService;

	private final KeyRequestIntegrationService keyRequestIntegrationService;

	private final DeviceBindingService deviceBindingService;

	private final NationalityService nationalityService;

	private final TokenIntgService tokenIntgService;

	private final PublicAuthenticationService publicAuthenticationService;

	private final AccountIntgService accountIntgService;

	private final AppUserService appUserService;

	@PublicAuth
	@SecurityRequirements
	@PostMapping("/request-otp")
	public ResponseEntity<KeyResponse> requestOTP(
			@RequestHeader(required = false, name = CLIENT_PLATFORM) ClientPlatformType clientPlatform,
			@Valid @RequestBody OTPRequest request) {
		request.setClientPlatform(clientPlatform);
		return ResponseEntity.ok(accountIntgService.requestOtp(request));
	}

	@Deprecated(since = "5.2.0", forRemoval = true)
	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@GetMapping("/verify-otp")
	public void verifyMobileOTP(@RequestParam String username, @RequestParam String key, @RequestParam String value,
			@RequestParam RequestKeyType requestKeyType, @RequestParam UsernameType usernameType) {
		if (UsernameType.PHONE_NUMBER.equals(usernameType)) {
			username = PhoneNumberUtil.getPhoneNumberWithNoCountryCode(username).getCombinedPhoneNumber();
		}
		accountService.verifyMobileOTP(username, key, value, requestKeyType, usernameType);
	}

	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("/verify-otp")
	public void verifyOtp(@Valid @RequestBody OTPVerifyRequest request) {
		if (UsernameType.PHONE_NUMBER.equals(request.getUsernameType())) {
			request.setUsername(
					PhoneNumberUtil.getPhoneNumberWithNoCountryCode(request.getUsername()).getCombinedPhoneNumber());
		}
		accountService.verifyMobileOTP(request.getUsername(), request.getKey(), request.getValue(),
				request.getRequestKeyType(), request.getUsernameType());
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@SecurityRequirements
	@PostMapping("/register")
	public void registerAccount(@Valid @RequestBody SignUpRequest signUpRequest) {
		accountIntgService.register(signUpRequest);
	}

	@ServiceToServiceAccess
	@PutMapping("/private/check-and-register")
	public ResponseEntity<UserPublicWebResponse> registerPublicAccount(
			@Valid @RequestBody UserPublicWebRequest request) {
		return ResponseEntity.ok(accountService.checkAndRegisterNewAccount(request));
	}

	@PutMapping(value = "/update-profile", consumes = MediaType.APPLICATION_JSON_VALUE)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<UserResponse> updateProfile(@Valid @RequestBody UpdateProfileRequest request) {
		User existingUser = accountService.getCurrentUser();
		UserResponse resp = null;
		// Transformation
		User user = objectMapper.convertValue(request, User.class);

		// TODO: To standardise this with FE and possibly request FE to pass ALL data.
		// PRJA-959 requirements:
		// https://mandrill.atlassian.net/browse/PRJA-959?focusedCommentId=17680
		// DOB field can be set back to null if request is an empty String. So let's
		// take the following approach:
		//
		// Before validation:
		// 1. If request.dob is an empty String, set current user dob to null.
		// 2. If request.dob is a String, attempt to parse (throw BusinessException in
		// case it fails)
		// 3. If request.dob is null, set current user dob to its existing dob.
		if (request.getDob() != null) {
			if (request.getDob().isBlank()) {
				user.setDob(null);
			}
			else {
				LocalDate dob = LocalDate.parse(request.getDob());
				user.setDob(dob);
			}
		}
		else {
			user.setDob(existingUser.getDob());
		}

		validationService.validateUpdateUser(existingUser, user);
		AppUser appUser = MapStructConverter.MAPPER.toAppUser(existingUser);

		// Implementation
		AppUser response = userIntegrationService.sensitiveFieldUpdateCheck(appUser, request.getKey(),
				appUserService::save);

		// Result
		resp = MapStructConverter.MAPPER.toUserResponse(response);

		return ResponseEntity.ok(resp);
	}

	@Hidden
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/reset-password-email")
	public void updateResetPassword(@Valid @RequestBody UpdatePasswordResetRequest request) {
		accountService.completePasswordResetEmail(request);
	}

	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/reset-password-mobile")
	public void updateResetPasswordMobile(@Valid @RequestBody UpdatePasswordResetMobileRequest request,
			@RequestParam UsernameType usernameType) {
		if (UsernameType.PHONE_NUMBER.equals(usernameType)) {
			request.setUsername(
					PhoneNumberUtil.getPhoneNumberWithNoCountryCode(request.getUsername()).getCombinedPhoneNumber());
		}
		passcodeProviderService.get(PasscodeType.PASSWORD.getProvider()).reset(request, usernameType);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/reset-mobile")
	public void updateResetMobile(@Valid @RequestBody UpdateMobileRequest request) {
		accountService.completeUpdateMobile(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/logout")
	public void logout(@RequestParam String deviceId, HttpServletRequest httpServletRequest) {
		deviceBindingService.logout(accountService.getCurrentUserIdAndRefNo().getId(), deviceId);
		tokenIntgService.logoutToken(httpServletRequest);

	}

	@PostMapping("/request-otp/reset-mobile")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<String> requestOtpResetMobile(
			@RequestParam(required = false, defaultValue = "PASSWORD") PasscodeType passcodeType,
			@Valid @RequestBody UpdateMobileOTPRequest request) {

		if (PasscodeType.PASSWORD.equals(passcodeType)) {
			log.info("passcode password validating...");
			if (StringUtils.isNotBlank(request.getPasswordVerificationKey())) {
				keyRequestIntegrationService.withValidateKey(request.getPasswordVerificationKey(),
						RequestKeyType.VERIFICATION_CHANGE_MOBILE, LambdaVoid.supplierNothing(), false);
			}
			else {
				User user = accountService.getCurrentUser();
				authService.checkPassword(user, request.getPassword());
			}
		}
		else if (PasscodeType.PIN.equals(passcodeType)) {
			log.info("passcode pin validating...");
			keyRequestIntegrationService.withValidateKey(request.getPinVerificationKey(),
					RequestKeyType.VERIFICATION_CHANGE_MOBILE, LambdaVoid.supplierNothing(), false);
		}
		log.info("passcode validation succeed");

		PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getPhoneCountry() + request.getPhoneNumber());

		accountService.checkIfUserExists(
				new User(phoneNumber.getPhoneCountry(), phoneNumber.getPhoneNumber(), LoginTypeEnum.USER));

		UserKeyRequest keyRequest = accountService.requestUpdateMobileOTP(phoneNumber.getCombinedPhoneNumber(),
				request.getDeliveryType());
		String validationKey = keyRequest.getKeyValue().split("_")[0];
		if (DeliveryType.SMS.equals(request.getDeliveryType())) {
			accountService.sendSmsVerification(keyRequest, phoneNumber.getCombinedPhoneNumber());
		}
		else if (DeliveryType.WHATSAPP.equals(request.getDeliveryType())) {
			accountService.sendWhatsAppVerification(keyRequest, phoneNumber.getCombinedPhoneNumber());
		}
		return ResponseEntity.ok(validationKey);
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<UserDTO> getAccount() {
		UserDTO userDTO = accountIntgService.getAccount();
		return ResponseEntity.ok(userDTO);
	}

	@GetMapping("permission")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<PermissionDTO>> getPermission(@RequestParam String currentInstitutionId) {
		User resp = accountService.getCurrentUser();
		Set<Permission> permissions = new HashSet<>();
		resp.getAuthorities().stream()
				.filter(authority -> currentInstitutionId.equals(authority.getInstitution().getId()))
				.forEach(authority -> permissions.addAll(authority.getPermissions()));
		return ResponseEntity.ok(permissions.stream()
				.map(permission -> objectMapper.convertValue(permission, PermissionDTO.class)).toList());
	}

	@GetMapping("/id")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<CurrentUserIdResponse> getAccountId() {
		return ResponseEntity.ok(accountService.getCurrentUserIdAndRefNo());
	}

	@GetMapping("/v2/id")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AccountResponse> getAccountIdV2() {
		return ResponseEntity.ok(accountService.getCurrentUserIdAndRefNoV2());
	}

	@GetMapping("/refNo/{refNo}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<CurrentUserIdResponse> getAccountByRefNo(@PathVariable String refNo) {
		return ResponseEntity.ok(accountService.getCurrentUserIdAndRefNo(refNo));
	}

	@Hidden
	@SecurityRequirements
	@GetMapping("/ref-no")
	public ResponseEntity<String> getRefNoByUsernameAndLoginTypeAndAccessType(@RequestParam String accessType,
			@RequestParam LoginTypeEnum loginType, @RequestParam() String username) {
		String resp = accountService.getUserRefNo(accessType, loginType, username);
		return ResponseEntity.ok(resp);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/change-password")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void changePassword(@Valid @RequestBody ChangePasswordRequest request) {
		User user = accountService.getCurrentUser();
		accountService.changePassword(user, request);
		accountService.sendEmailPasswordUpdated(user);
	}

	@GetMapping("/secret-key")
	public String getSecretKey(@RequestHeader(RequestUtil.API_KEY) String apiKey) {
		return accountService.getSecretKey(SecurityUtil.currentUserLogin(), apiKey);
	}

	@ServiceToServiceAccess
	@GetMapping("/private/public-key")
	public PublicAuthenticationDTO getPublicKey(@RequestParam String identifier) {
		return publicAuthenticationService.findPublicKeyByIdentifier(identifier);
	}

	@GetMapping("/extended-net-worth")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<ExtendedNetWorthCalculationResponse> getExtendedNetWorthBalance(
			@RequestParam(required = false, defaultValue = "FALSE") Boolean isRevamp) {
		ExtendedNetWorthCalculationResponse response = accountService
				.calculateExtendedNetWorth(SecurityUtil.currentUserId(), isRevamp);
		return ResponseEntity.ok(response);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/delete-process")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void deleteAccountProcess(@RequestBody Optional<AccountDeleteRequest> requestOpt) {
		AccountDeleteRequest request = requestOpt.orElse(new AccountDeleteRequest(null));
		AppUser appUser = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		Supplier<Void> deleteProcess = () -> {
			accountService.deleteProcess(appUser);
			deviceBindingService.deleteAllByUserId(appUser.getId());
			return null;
		};

		if (Objects.isNull(appUser.getPin())) {
			log.info("user does not have a PIN, skip the verification step key");
			deleteProcess.get();
			return;
		}

		if (Strings.isBlank(request.key())) {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}

		log.info("user has a PIN, verify the key");
		keyRequestIntegrationService.withValidateKey(request.key(), RequestKeyType.VERIFICATION_DELETE_ACCOUNT,
				deleteProcess);
	}

	@GetMapping("/validate-term-condition-privacy-policy")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<TermConditionAndPrivacyPolicyDTO> validateTermConditionAndPrivacyPolicy() {
		User resp = accountService.getCurrentUser();
		return ResponseEntity.ok(accountService.validateTermConditionAndPrivacyPolicy(resp));
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/update-term-condition-privacy-policy")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public void updateTermConditionAndPrivacyPolicy(TermConditionAndPrivacyPolicyDTO request) {
		User resp = accountService.getCurrentUser();
		accountService.updateTermConditionAndPrivacyPolicyVersion(resp, request);
	}

	@PutMapping("/update-address")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<UpdateAddressResponse> updateAddresses(@Valid @RequestBody UpdateAddressRequest request) {
		User user = accountService.getCurrentUser();

		user.setAddress1(request.getAddress1());
		user.setAddress2(request.getAddress2());
		user.setAddress3(request.getAddress3());
		user = userIntegrationService.save(user);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toUpdateAddressResponse(user));
	}

	@PostMapping("/exist-mobile")
	public Boolean checkMobileExist(@Valid @RequestBody CheckMobileRequest request) {
		PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getPhoneCountry() + request.getPhoneNumber());

		return OptionalUtil.eval(Boolean.TRUE, () -> {
			accountService.checkIfUserExists(
					new User(phoneNumber.getPhoneCountry(), phoneNumber.getPhoneNumber(), LoginTypeEnum.USER));
			return Boolean.FALSE;
		});
	}

	@PostMapping("/email-masked")
	public String checkEmailMasked(@Valid @RequestBody EmailMaskedRequest request) {
		PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getPhoneCountry() + request.getPhoneNumber());

		return accountService.getUserEmail(phoneNumber.getPhoneCountry(), phoneNumber.getPhoneNumber(),
				LoginTypeEnum.USER);
	}

	@PostMapping("/delete-account-message")
	public void deleteAccountMessage(String deleteMessage) {
		User user = accountService.getCurrentUser();
		DeleteAccountMessage accountMessage = new DeleteAccountMessage();
		accountMessage.setUserid(user.getId());
		accountMessage.setDeleteMessage(deleteMessage);
		accountService.saveDeleteAccountMessage(accountMessage);
	}

	@GetMapping("/income-epf")
	public ResponseEntity<IncomeAndEpfResponse> getIncomeAndEpf(@RequestParam String incomeTypeId) {
		IncomeAndEpfResponse data = accountService.findLatestIncomeAndEpf(incomeTypeId);
		return ResponseEntity.ok(data);
	}

	@GetMapping("/integrations/nationalities")
	public ResponseEntity<CurrentUserNationalityResponse> getCurrentUserNationalities() {
		CurrentUserNationalityResponse data = nationalityService.getCurrentUserNationality();
		return ResponseEntity.ok(data);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/tnc-aggreement")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public void tncAgreement(@RequestBody @Valid TncAgreementRequest request) {
		accountIntgService.updateTncAgreement(request);
	}

	@ServiceToServiceAccess
	@GetMapping("private/for-ai")
	public CurrentUserDataForAIResponse getCurrentUserDataForAiIntegration() {

		return accountService.getCurrentUserDataForAI(SecurityUtil.currentUserId());

	}

}
