package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import my.com.mandrill.utilities.general.constant.DeliveryType;

import java.io.Serializable;
import java.util.Optional;

@Data
public class UpdateMobileOTPRequest implements Serializable {

	@NotBlank
	@Size(max = 3)
	private String phoneCountry;

	@NotBlank
	@Pattern(regexp = "^[0-9]*$")
	@Size(max = 17)
	private String phoneNumber;

	private String pinVerificationKey;

	private String passwordVerificationKey;

	private String password;

	private DeliveryType deliveryType;

	public DeliveryType getDeliveryType() {
		return Optional.ofNullable(deliveryType).orElse(DeliveryType.SMS);
	}

}
