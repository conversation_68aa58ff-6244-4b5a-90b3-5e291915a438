package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.KeyRequestIntegrationService;
import my.com.mandrill.component.service.KeyRequestService;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class KeyRequestIntegrationServiceImpl implements KeyRequestIntegrationService {

	private final KeyRequestService keyRequestService;

	@Override
	public <T> T withValidateKey(String key, RequestKeyType type, Supplier<T> postProcess) {
		return withValidateKey(key, type, postProcess, true);
	}

	@Override
	public <T> T withValidateKey(String key, RequestKeyType type, Supplier<T> postProcess, boolean autoRevoke) {
		log.info("starting validate key for type: {}", type);
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(key);

		Optional<UserKeyRequest> keyRequestOpt = keyRequestService.findByKeyValueAndType(keyInput.getKeyValueRaw(),
				type);
		if (keyRequestOpt.isEmpty() || !RequestKeyStatus.PENDING.equals(keyRequestOpt.get().getStatus())) {
			log.error("invalid request checking, data not found or not status pending");
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
		UserKeyRequest keyRequest = keyRequestOpt.get();

		keyRequest.setStatus(RequestKeyStatus.COMPLETED);
		keyRequest.setCompletionDate(Instant.now());

		if (autoRevoke) {
			CompletableFuture.supplyAsync(() -> {
				log.info("updating key request to persistence");
				keyRequestService.save(keyRequest);

				return null;
			}).thenAccept(v -> log.info("success save key-request persisted..."));
		}

		log.info("validate success :)");
		return postProcess.get();
	}

}
