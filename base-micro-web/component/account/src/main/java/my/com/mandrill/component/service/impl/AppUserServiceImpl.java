package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.repository.jpa.AppUserRepository;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.RedisService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppUserServiceImpl implements AppUserService {

	private final AppUserRepository appUserRepository;

	private final RedisService redisService;

	@Override
	public AppUser findByRefNoAndLoginType(String refNo, LoginTypeEnum loginType) {
		return getOrLoadFromCache(CacheKey.APP_USER_BY_REF_NO, refNo,
				() -> appUserRepository.findByRefNoAndDeletedAndLoginType(refNo, false, loginType))
						.orElseThrow(ExceptionPredicate.userNotFoundByRefNo(refNo));
	}

	@Override
	public AppUser findByRefNo(String refNo) {
		return getOrLoadFromCache(CacheKey.APP_USER_BY_REF_NO, refNo,
				() -> appUserRepository.findAuthByRefNoAndDeleted(refNo, false).filter(AppUser::getActive))
						.orElseThrow(() -> new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST));
	}

	private Optional<AppUser> getOrLoadFromCache(String key, String hash, Supplier<Optional<AppUser>> dbSupplier) {
		String cacheKey = CacheKey.CACHE_FORMAT.formatted(key, hash);
		return redisService.getFromValue(cacheKey, new TypeReference<AppUser>() {
		}).or(() -> {
			Optional<AppUser> user = dbSupplier.get();
			cacheUserSearchableKey(user, cacheKey);
			return user;
		});
	}

	private void cacheUserSearchableKey(Optional<AppUser> user, String key) {
		if (user.isEmpty()) {
			return;
		}
		Duration cacheDuration = Duration.ofMinutes(15);
		redisService.putToValue(key, user.get(), cacheDuration);
	}

	private void cacheUserSearchableKey(AppUser user) {
		cacheUserSearchableKey(Optional.of(user),
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_REF_NO, user.getRefNo()));
		if (Objects.nonNull(user.getPhoneNumber())) {
			cacheUserSearchableKey(Optional.of(user),
					CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_PHONE_NUMBER, user.getPhoneNumber()));
		}
		if (Objects.nonNull(user.getEmail())) {
			cacheUserSearchableKey(Optional.of(user),
					CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_EMAIL, user.getEmail()));
		}
	}

	@Override
	public AppUser findByRefNoAndLoginTypeAndDeletedFalse(String refNo, LoginTypeEnum loginTypeEnum) {
		return findByRefNoAndLoginType(refNo, loginTypeEnum);
	}

	@Override
	public AppUser findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(String phoneNumber,
			String phoneCountry, LoginTypeEnum loginType) {
		return findSafeByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(phoneNumber, phoneCountry, loginType)
				.orElseThrow(ExceptionPredicate.userNotFoundByRefNo(phoneNumber));
	}

	@Override
	public Optional<AppUser> findSafeByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(String phoneNumber,
			String phoneCountry, LoginTypeEnum loginType) {
		return getOrLoadFromCache(CacheKey.APP_USER_BY_PHONE_NUMBER, phoneNumber,
				() -> appUserRepository.findUserAuthByPhoneNumberAndPhoneCountryAndDeletedAndLoginType(
						AesCryptoUtil.basicEncrypt(phoneNumber), phoneCountry, false, loginType));
	}

	@Override
	public AppUser findAuthByEmailAndActiveAndLoginType(String email, LoginTypeEnum loginType) {
		return getOrLoadFromCache(CacheKey.APP_USER_BY_EMAIL, email, () -> appUserRepository
				.findUserAuthByEmailAndDeletedAndLoginType(AesCryptoUtil.basicEncrypt(email), false, loginType))
						.orElseThrow(ExceptionPredicate.userNotFoundByRefNo(email));
	}

	@Override
	public Optional<AppUser> findSafeUserByEmailAndActiveAndLoginType(String email, LoginTypeEnum loginType) {
		return getOrLoadFromCache(CacheKey.APP_USER_BY_EMAIL, email,
				() -> appUserRepository.findUserAuthByEmailAndDeletedAndLoginTypeAndActive(
						AesCryptoUtil.basicEncrypt(email), false, loginType, true));
	}

	/**
	 * Explanation: When performing a `save` or `saveAndFlush` operation, Spring Data JPA
	 * may automatically trigger a SELECT query on the entity being saved. This behavior
	 * occurs especially when certain fields or associations (e.g., @ManyToOne
	 * relationships) are already set before the save call. Hibernate does this to verify
	 * that the assigned fields still reference managed and valid entities in the current
	 * persistence context. If you want to avoid triggering SELECT queries for a
	 * particular field, make sure the field is not set manually in the entity before
	 * saving. In other words, avoid setting lazy-loaded relationships if you don't need
	 * them to be initialized at this stage.
	 */
	@Transactional
	@Caching(evict = { @CacheEvict(value = CacheKey.USER_BY_LOGIN_CACHE, key = "#appUser.username"),
			@CacheEvict(value = CacheKey.USER_DETAIL_PERMISSION_LOGIN_CACHE, key = "#appUser.username"),
			@CacheEvict(value = CacheKey.USER_BY_ID_CACHE, key = "#appUser.refNo"), })
	@Override
	public AppUser save(AppUser appUser) {
		appUserRepository.saveAndFlush(appUser);

		AppUser result = appUserRepository.findAuthByRefNoAndDeleted(appUser.getRefNo(), false)
				.orElseThrow(ExceptionPredicate.userNotFoundByRefNo(appUser.getRefNo()));
		cacheUserSearchableKey(result);
		return result;
	}

	@Override
	public AppUser findByEmailOrPhoneNumber(String refOrPhone, LoginTypeEnum loginType) {
		if (LoginTypeEnum.USER.equals(loginType)) {
			return findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(refOrPhone.substring(3),
					refOrPhone.substring(0, 3), loginType);
		}
		return findAuthByEmailAndActiveAndLoginType(refOrPhone, loginType);
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Caching(evict = { @CacheEvict(value = CacheKey.USER_BY_LOGIN_CACHE, key = "#appUser.username"),
			@CacheEvict(value = CacheKey.USER_DETAIL_PERMISSION_LOGIN_CACHE, key = "#appUser.username"),
			@CacheEvict(value = CacheKey.USER_BY_ID_CACHE, key = "#appUser.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_REF_NO, key = "#appUser.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_PHONE_NUMBER, key = "#appUser.phoneNumber"),
			@CacheEvict(value = CacheKey.APP_USER_BY_EMAIL, key = "#appUser.email",
					condition = "#appUser.email != null"), })
	@Override
	public void processFailedLogin(AppUser appUser) {
		appUserRepository.updateFailAttempt(appUser.getLoginFailAttempt(), Instant.now(), appUser.getId());
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public void resetFailedLogin(AppUser appUser) {
		appUserRepository.updateLoginFailAttempt(appUser.getId());
		appUser.setLoginFailAttempt(0);
		appUser.setLoginFailDeviceLockLog(null);
		cacheUserSearchableKey(Optional.of(appUser),
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_PHONE_NUMBER, appUser.getPhoneNumber()));
		cacheUserSearchableKey(Optional.of(appUser),
				CacheKey.CACHE_FORMAT.formatted(CacheKey.APP_USER_BY_EMAIL, appUser.getEmail()));
	}

	@Transactional
	@Override
	public Optional<AppUser> findById(String id) {
		return appUserRepository.findById(id);
	}

	@Transactional
	@Caching(evict = { @CacheEvict(value = CacheKey.USER_BY_LOGIN_CACHE, key = "#appUser.username"),
			@CacheEvict(value = CacheKey.USER_DETAIL_PERMISSION_LOGIN_CACHE, key = "#appUser.username"),
			@CacheEvict(value = CacheKey.USER_BY_ID_CACHE, key = "#appUser.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_REF_NO, key = "#appUser.refNo"),
			@CacheEvict(value = CacheKey.APP_USER_BY_PHONE_NUMBER, key = "#appUser.phoneNumber"),
			@CacheEvict(value = CacheKey.APP_USER_BY_EMAIL, key = "#appUser.email",
					condition = "#appUser.email != null"), })
	@Override
	public void processGracePeriod(AppUser appUser) {
		appUserRepository.updateGracePeriod(Instant.now(), appUser.getId(), appUser.getRefNo());
	}

}
