package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.RequestKeyType;

import java.io.Serializable;
import java.util.Optional;

@Data
public class OTPRequest implements Serializable {

	private String captchaToken;

	@NotBlank
	private String username;

	@NotNull
	private RequestKeyType requestKeyType;

	@NotNull
	private UsernameType usernameType;

	private DeliveryType deliveryType;

	private String referralCode;

	@JsonIgnore
	private ClientPlatformType clientPlatform;

	public DeliveryType getDeliveryType() {
		return Optional.ofNullable(deliveryType).orElse(DeliveryType.SMS);
	}

}
