package my.com.mandrill.component.service.impl;

import com.google.api.gax.core.NoCredentialsProvider;
import com.google.api.gax.rpc.FixedHeaderProvider;
import com.google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseServiceClient;
import com.google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseServiceSettings;
import com.google.recaptchaenterprise.v1.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.service.CaptchaService;
import my.com.mandrill.utilities.feign.client.GoogleClient;
import my.com.mandrill.utilities.feign.dto.request.RecaptchaVerifyRequest;
import my.com.mandrill.utilities.feign.dto.response.RecaptchaVerifyResponse;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.config.GoogleProperties;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleRecaptchaService implements CaptchaService {

	private final GoogleProperties googleProperties;

	private final FeatureFlagOutbound featureFlagOutbound;

	private final GoogleClient googleClient;

	@Override
	public boolean verify(String token, String action, ClientPlatformType platformType) {
		boolean captchaEnabled = featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.RECAPTCHA_ENABLED);
		if (captchaEnabled) {
			int captchaMinimalScore = featureFlagOutbound
					.getIntegerValue(GlobalSystemConfigurationEnum.RECAPTCHA_MINIMUM_SCORE);
			log.info("recaptcha enabled with minimal-score: {}", captchaMinimalScore);

			float minimalScore = captchaMinimalScore / 10.0f;
			if (ClientPlatformType.WEB.equals(platformType) || Objects.isNull(platformType)) {
				return verifyClassic(token, minimalScore, action);
			}
			else {
				return verifyEnterprise(token, minimalScore, action, platformType);
			}
		}
		return true;
	}

	private boolean verifyClassic(String token, float minimalScore, String action) {
		try {
			RecaptchaVerifyResponse verify = googleClient.siteVerify(RecaptchaVerifyRequest.builder()
					.secret(googleProperties.getRecaptcha().getSecretKey()).response(token).build().toMultiValueMap());

			log.info("recaptcha verify response: success: {}, score: {}, action: {}, error: {}", verify.isSuccess(),
					verify.getScore(), verify.getAction(), verify.getErrorCode());
			return verify.isSuccess() && (verify.getScore() >= minimalScore) && action.equals(verify.getAction());
		}
		catch (Exception e) {
			log.error("Error verifying reCAPTCHA: {}", e.getMessage(), e);
			return false;
		}
	}

	private boolean verifyEnterprise(String token, float minimalScore, String action, ClientPlatformType platformType) {
		try {
			String siteKey = switch (platformType) {
				case GOOGLE -> googleProperties.getRecaptcha().getSiteKeyAndroid();
				case APPLE -> googleProperties.getRecaptcha().getSiteKeyIos();
				default -> throw new IllegalArgumentException("Unsupported platform type: " + platformType);
			};

			final RecaptchaEnterpriseServiceSettings settings = RecaptchaEnterpriseServiceSettings.newBuilder()
					.setCredentialsProvider(NoCredentialsProvider.create())
					.setHeaderProvider(
							FixedHeaderProvider.create("x-goog-api-key", googleProperties.getRecaptcha().getApiKey()))
					.build();
			try (RecaptchaEnterpriseServiceClient client = RecaptchaEnterpriseServiceClient.create(settings)) {
				Event event = Event.newBuilder().setSiteKey(siteKey).setToken(token).build();
				CreateAssessmentRequest createAssessmentRequest = CreateAssessmentRequest.newBuilder()
						.setParent(ProjectName.of(googleProperties.getRecaptcha().getProjectId()).toString())
						.setAssessment(Assessment.newBuilder().setEvent(event).build()).build();

				Assessment response = client.createAssessment(createAssessmentRequest);
				log.info("recaptcha-enterprise response: {}", response);
				if (!response.getTokenProperties().getValid()) {
					log.info("recaptcha-enterprise token validation failed");
					return false;
				}
				if (!response.getTokenProperties().getAction().equals(action)) {
					log.info("recaptcha-enterprise action mismatch: expected {}, got {}", action,
							response.getTokenProperties().getAction());
					return false;
				}
				for (RiskAnalysis.ClassificationReason reason : response.getRiskAnalysis().getReasonsList()) {
					log.info("recaptcha-enterprise reason: {}", reason);
				}

				float recaptchaScore = response.getRiskAnalysis().getScore();
				if (recaptchaScore < minimalScore) {
					log.info("recaptcha-enterprise score too low: expected {}, got {}", minimalScore, recaptchaScore);
					return false;
				}
				return true;
			}
		}
		catch (IOException e) {
			log.error("Error verifying reCAPTCHA Enterprise: {}", e.getMessage(), e);
			return false;
		}
	}

}
