package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import my.com.mandrill.utilities.general.constant.RequestKeyType;

import java.io.Serializable;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class KeySignatureChallengeRequest extends AssertionSignatureChallengeRequest implements Serializable {

	@NotNull
	private RequestKeyType requestKeyType;

}