package my.com.mandrill.component.config;

import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.feign.dto.ReminderRequest;
import my.com.mandrill.utilities.feign.dto.ReminderResponse;
import my.com.mandrill.utilities.feign.dto.model.StandardObjectDTO;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.service.LazyLoadingAwareMapper;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter extends LazyLoadingAwareMapper {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	void update(AdminUserDTO adminUserDTO, @MappingTarget User user);

	@Mapping(target = "username", source = "oAuth2User.email")
	User toUser(CustomOAuth2User oAuth2User);

	void update(UserRequest request, @MappingTarget User user);

	PasswordResetEmailRequest toPasswordResetEmailRequest(User user);

	EmailVerificationEmailRequest toEmailVerificationEmailRequest(User user);

	EmailUpdatedEmailRequest toEmailUpdatedEmailRequest(User user);

	Permission toPermission(AdminPermissionDTO adminPermissionDTO);

	AdminPermissionDTO toAdminPermissionDTO(Permission permission);

	Permission toPermission(PermissionDTO permissionDTO);

	PermissionDTO toPermissionDTO(Permission permission);

	Authority toAuthority(AuthorityDTO authorityDTO);

	AuthorityDTO toAuthorityDTO(Authority authority);

	AuthorityNoPaginationResponse toAuthorityNoPaginationResponse(Authority authority);

	ObjectDTO map(Institution value);

	my.com.mandrill.utilities.feign.dto.ExpenseDTO toExpenseDTO(Expense expense);

	ExpenseDTO toExpenseInternalDTO(Expense expense);

	Expense toExpense(ExpenseRequest expenseRequest);

	ReminderRequest toReminderRequest(ReminderIntegrationRequest reminder);

	my.com.mandrill.utilities.feign.dto.IncomeDTO toIncomeDTO(Income income);

	IncomeDTO toIncomeDTOModel(Income income);

	ExpenseType toExpenseType(ExpenseTypeRequest expenseTypeRequest);

	ExpenseTypeDTO toExpenseTypeDTO(ExpenseType expenseType);

	EmploymentType toEmploymentType(EmploymentTypeRequest employmentTypeRequest);

	EmploymentTypeDTO toEmploymentTypeDTO(EmploymentType employmentType);

	Currency toCurrency(CurrencyRequest currencyRequest);

	CurrencyDTO toCurrencyDTO(Currency currency);

	DeviceKey toDeviceKey(DeviceRegisterStartRequest deviceRegisterStartRequest);

	DeviceKey toDeviceKey(ChallengeRequest challengeRequest);

	DeviceKey toDeviceKey(RegisterSignatureChallengeRequest.DeviceKeyRequest deviceKeyRequest);

	SignatureChallengeDTO toSignatureChallengeDTO(SignatureChallenge signatureChallenge);

	SignatureChallenge toSignatureChallenge(RegisterSignatureChallengeRequest registerSignatureChallengeRequest);

	SignatureChallenge toSignatureChallenge(AssertionSignatureChallengeRequest assertionSignatureChallengeRequest);

	DeviceKeyDTO toDeviceKeyDTO(DeviceKey deviceKey);

	@Mapping(target = "pinSet", expression = "java(java.util.Objects.nonNull(resp.getPin()))")
	UserDTO toUserDTO(User resp);

	@Mapping(target = "pinSet", expression = "java(java.util.Objects.nonNull(resp.getPin()))")
	UserDTO toUserDTO(AppUser resp);

	AccountResponse toAccountResponse(User currentUser);

	Income toIncome(IncomeRequest incomeRequest);

	@Mapping(target = "pinSet", expression = "java(java.util.Objects.nonNull(user.getPin()))")
	UserResponse toUserResponse(User user);

	/**
	 * @deprecated since 1.2.0
	 */
	@Deprecated(since = "1.2.0")
	default ReminderResponse toReminderResponse(ReminderIntegrationRequest reminderIntegrationRequest) {
		return null;
	}

	BusinessNatureDTO toBusinessNatureDTO(BusinessNature businessNature);

	BusinessNature toBusinessNature(BusinessNatureRequest businessNatureRequest);

	UpdateAddressResponse toUpdateAddressResponse(User user);

	IdTypeDTO toIdTypeDTO(IdType idType);

	EpfContributionDTO toEpfContributionDTO(EpfContribution epfContribution);

	PublicAuthenticationDTO toPublicAuthenticationDTO(PublicAuthentication publicAuthentication);

	@Mapping(source = "value", target = "downloads")
	WeeklyUserStatisticsResponse toWeeklyUserDownloadsResponse(WeeklyUserStatistics weeklyUserStatistics);

	@Mapping(source = "value", target = "registered")
	WeeklyUserStatisticsResponse toWeeklyUserRegisteredResponse(WeeklyUserStatistics weeklyUserStatistics);

	WeeklyUserTarget toWeeklyUserTarget(WeeklyUserTargetRequest request);

	WeeklyUserTargetDTO toWeeklyUserTargetDTO(WeeklyUserTarget result);

	MonthlyUserTarget toMonthlyUserTarget(MonthlyUserTargetRequest request);

	MonthlyUserTargetDTO toMonthlyUserTargetDTO(MonthlyUserTarget result);

	HomeMenuMapResponse toHomeMenuMapResponse(HomeMenuMap homeMenuMap);

	HomeMenuProduct toHomeMenuProduct(HomeMenuProductRequest homeMenuProductRequest);

	HomeMenuProductResponse toHomeMenuProductResponse(HomeMenuProduct homeMenuProduct);

	List<HomeMenuProductResponse> toHomeMenuProductResponse(List<HomeMenuProduct> homeMenuProduct);

	@Mapping(source = "token.id", target = "accessToken")
	@Mapping(source = "token.refreshToken", target = "refreshToken")
	@Mapping(source = "token.revokedDate", target = "revokedDate")
	@Mapping(source = "token.revokedBy", target = "revokedBy")
	@Mapping(source = "token.status", target = "status")
	@Mapping(source = "token.deviceId", target = "deviceId")
	@Mapping(source = "user.username", target = "username")
	@Mapping(source = "createdDate", target = "accessDate")
	@Mapping(source = "user.id", target = "userId")
	TokenServiceResponse toTokenServiceResponse(TokenTransaction transaction);

	@Mapping(source = "user.refNo", target = "userRefNo")
	UserPublicWebResponse toUserPublicWebResponse(User user, boolean existingUser);

	@Mapping(source = "fullName", target = "name")
	@Mapping(source = "id", target = "userId")
	PublicUserDetailResponse toUserDetailResponse(User user);

	UserDetailResponse toUserDetailResponse(InternalUserMobileResponse user);

	UserDetailResponse toInternalUserMobileResponse(User user);

	@Mapping(target = "referralCode",
			expression = "java(user.getReferralCode() == null || user.getReferralCode().isBlank() ? null : user.getReferralCode())")
	UserPublicWebRequest toUserPublicWebRequest(PreSignUpRequest user);

	@Mapping(target = "parentInstitution", ignore = true)
	my.com.mandrill.utilities.feign.dto.InstitutionDTO toInstitutionFeignDTO(Institution institution);

	@Named("InstitutionToObjectDTO")
	@Mapping(target = "id", source = "id")
	ObjectDTO toObjectDTO(Institution data);

	@Mapping(target = "id", source = "id")
	ObjectDTO toObjectDTO(StandardObjectDTO data);

	CurrentUserDataForAIResponse toCurrentUserDataForAIResponse(UserDataForAiProjection userDataForAiProjection);

	InternalUserMobileResponse mapToInternalUserMobileResponse(User user);

	AppUser toAppUser(User user);

	@Mapping(target = "pinSet", expression = "java(java.util.Objects.nonNull(user.getPin()))")
	UserResponse toUserResponse(AppUser user);

	User toUser(AppUser appUser);

}
