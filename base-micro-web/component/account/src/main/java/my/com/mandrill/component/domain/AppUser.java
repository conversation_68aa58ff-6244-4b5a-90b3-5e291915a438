package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.component.constant.AuthenticationProviderSource;
import my.com.mandrill.crypto.converter.ConfidentialDataConverter;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.converter.ConfidentialDataConverterLocalDate;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.*;
import org.hibernate.Hibernate;
import org.hibernate.annotations.Cascade;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "app_user", uniqueConstraints = { @UniqueConstraint(columnNames = { "username", "login_type" }),
		@UniqueConstraint(columnNames = { "ref_no" }), @UniqueConstraint(columnNames = { "email", "login_type" }) })
public class AppUser extends AuditSection implements Serializable {

	@NotNull
	@Column(name = "ref_no", updatable = false, length = 15)
	private String refNo;

	@NotNull
	@Pattern(regexp = Constant.LOGIN_REGEX)
	@Size(min = 1, max = 100)
	@Column(length = 100, nullable = false, updatable = false)
	private String username;

	@Size(min = 60, max = 60)
	@Column(name = "password_hash", length = 60)
	private String password;

	@Column(name = "pin")
	private String pin;

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 50)
	private AuthenticationProviderSource provider;

	@Size(max = 200)
	@Column(name = "full_name", length = 200)
	private String fullName;

	@Size(min = 5, max = 100)
	@Column(length = 100)
	private String email;

	@NotNull
	@Column(name = "email_verified", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean emailVerified = false;

	@Column(name = "phone_country", length = 3)
	private String phoneCountry;

	@Column(name = "phone_number", length = 100)
	private String phoneNumber;

	@NotNull
	@Column(name = "phone_verified", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean phoneVerified = false;

	@Column(name = "lang_key", length = 6)
	private Language langKey = Language.ENGLISH;

	@NotNull
	@Column(columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean active = false;

	@Column(nullable = false, columnDefinition = "INT(2) DEFAULT 0")
	private Integer loginFailAttempt;

	@Column(name = "login_fail_device_lock_log")
	private String loginFailDeviceLockLog;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "login_type", length = 50, columnDefinition = "VARCHAR(50) DEFAULT 'ADMIN'")
	private LoginTypeEnum loginType;

	// personal info
	@Column(name = "nric", length = 200)
	@Convert(converter = ConfidentialDataConverter.class)
	private String nric;

	@NotNull
	@Column(name = "is_nric_editable", columnDefinition = "BOOLEAN DEFAULT TRUE", nullable = false)
	private Boolean isNricEditable = true;

	@Column(name = "address1")
	@Convert(converter = ConfidentialDataConverter.class)
	private String address1;

	@Column(name = "address2")
	@Convert(converter = ConfidentialDataConverter.class)
	private String address2;

	@Column(name = "address3")
	@Convert(converter = ConfidentialDataConverter.class)
	private String address3;

	@Column(name = "postcode", length = 100)
	private String postcode;

	@Column(name = "country_id", length = 36)
	private String countryId;

	@Column(name = "state_id", length = 36)
	private String stateId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "nationality_id")
	private Nationality nationality;

	@NotNull
	@Column(name = "is_nationality_editable", columnDefinition = "BOOLEAN DEFAULT TRUE", nullable = false)
	private Boolean isNationalityEditable = true;

	@Column(columnDefinition = "INT(3)")
	private Integer age;

	@Column(name = "gender", length = 10)
	private String gender;

	@Column(name = "marital_status", length = 15)
	private String maritalStatus;

	@Column(name = "ethnicity", length = 20)
	private String ethnicity;

	@Column(name = "religion", length = 20)
	private String religion;

	@Column(name = "blood_type", length = 3)
	private String bloodType;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "currency_id")
	private Currency currency;

	@Digits(integer = 5, fraction = 2)
	@Column(name = "epf_contribution", columnDefinition = "DECIMAL(5,2)", precision = 5, scale = 2)
	private BigDecimal epfContribution;

	@Column(name = "socso")
	private Boolean socso;

	@Column(name = "eis")
	private Boolean eis;

	@Column(name = "self_employed_name", length = 100)
	private String selfEmployedName;

	@Column(name = "secret_key")
	@Convert(converter = ConfidentialDataConverter.class)
	private String secretKey;

	// the datetime the user start to initiate the delete process
	@Column(name = "deleted_datetime")
	private Instant deletedDatetime;

	// once the scheduler pickup the record after 30 days we will flag this to be deleted
	// record
	@Column(columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean deleted = false;

	@Column(name = "term_condition_version", length = 20)
	private String termConditionVersion;

	@Column(name = "privacy_policy_version", length = 20)
	private String privacyPolicyVersion;

	@Column(name = "platform_agreement_version", length = 20)
	private String platformAgreementVersion;

	@Column(name = "referral_code_term_condition_version", length = 20)
	private String referralCodeTermConditionVersion;

	@Transient
	private String key;

	@Transient
	private String value;

	@Column(name = "passport", length = 100)
	@Convert(converter = ConfidentialDataConverter.class)
	private String passport;

	@Column(name = "army", length = 100)
	@Convert(converter = ConfidentialDataConverter.class)
	private String army;

	@Column(name = "dob")
	@Convert(converter = ConfidentialDataConverterLocalDate.class)
	private LocalDate dob;

	@Enumerated(EnumType.STRING)
	@Column(name = "payment_info_status")
	private PaymentAccountStatus paymentInfoStatus = PaymentAccountStatus.YET_TO_SUBMIT;

	@Column(name = "payment_info_approved_date")
	private Instant paymentInfoApprovedDate;

	@Enumerated(EnumType.STRING)
	@Column(name = "ekyc_verification_status")
	private EkycStatus ekycVerificationStatus;

	@Column(name = "referral_code")
	private String referralCode;

	@Column(name = "referral_code_used")
	private String referralCodeUsed;

	@Column(name = "rsm_relation_type")
	private String rsmRelationType;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "education_level_id")
	private EducationLevel educationLevel;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "employment_type_id")
	private EmploymentType employmentType;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "occupation_group_id")
	private OccupationGroup occupationGroup;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "business_nature_id")
	private BusinessNature businessNature;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToMany(fetch = FetchType.LAZY, cascade = { CascadeType.REFRESH })
	@JoinTable(name = "app_user_authority",
			joinColumns = { @JoinColumn(name = "user_id", nullable = false, updatable = false) },
			inverseJoinColumns = { @JoinColumn(name = "authority_id", nullable = false, updatable = false) })
	@Cascade({ org.hibernate.annotations.CascadeType.DETACH, org.hibernate.annotations.CascadeType.LOCK,
			org.hibernate.annotations.CascadeType.REFRESH, org.hibernate.annotations.CascadeType.REPLICATE })
	private Set<Authority> authorities = new HashSet<>();

	@EqualsAndHashCode.Exclude
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "app_user_institution", joinColumns = @JoinColumn(name = "user_id"),
			inverseJoinColumns = @JoinColumn(name = "institution_id"))
	@ToString.Exclude
	private Set<Institution> institutions = new HashSet<>();

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "app_user_interest", joinColumns = @JoinColumn(name = "user_id"),
			inverseJoinColumns = @JoinColumn(name = "interest_id"))
	private Set<Interest> interests = new HashSet<>();

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "app_user_financial_goal", joinColumns = @JoinColumn(name = "user_id"),
			inverseJoinColumns = @JoinColumn(name = "financial_goal_id"))
	private Set<FinancialGoal> financialGoals = new HashSet<>();

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
	private Set<Income> incomes = new LinkedHashSet<>();

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
	private Set<EpfContribution> epfContributions = new LinkedHashSet<>();

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
	private Set<Expense> expenses = new LinkedHashSet<>();

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "segment_id")
	private Segment segment;

	public void setEmail(String email) {
		this.email = email == null ? null : AesCryptoUtil.basicEncrypt(email);
	}

	public String getEmail() {
		return this.email == null ? null : AesCryptoUtil.basicDecrypt(this.email);
	}

	public String getPhoneNumber() {
		return this.phoneNumber == null ? null : AesCryptoUtil.basicDecrypt(this.phoneNumber);
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber == null ? null : AesCryptoUtil.basicEncrypt(phoneNumber);
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		AppUser appUser = (AppUser) o;
		return getId() != null && Objects.equals(getId(), appUser.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

	@Override
	public String toString() {
		return getId();
	}

}
