package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.RetirementAccount;
import my.com.mandrill.component.dto.request.RetirementAccountCreateRequest;
import my.com.mandrill.component.dto.request.RetirementAccountUpdateRequest;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.SavingGoalDeletionDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.SavingGoalAccountType;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RetirementAccountIntegrationServiceImpl implements RetirementAccountIntegrationService {

	private static final String EPF_ACCOUNT_TYPE_ID = "dd52a1d8-4e62-4c35-89bc-aed4c85acc80";

	private final RetirementAccountService retirementAccountService;

	private final RetirementAccountTypeService retirementAccountTypeService;

	private final RetirementProviderService retirementProviderService;

	private final ValidationService validationService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	@Override
	public RetirementAccount processRetirementAccount(String userId, RetirementAccountCreateRequest request) {

		validationService.validateRetirementSavingsAmount(request.getSavingsAmount());
		validationService.validateRetirementAccountExists(userId, request.getAccountTypeId(), request.getProviderId());

		RetirementAccount retirementAccount = MapStructConverter.MAPPER.toRetirementAccount(request);
		retirementAccount.setUserId(userId);
		retirementAccount.setAccountType(retirementAccountTypeService.findById(request.getAccountTypeId()));
		if (!EPF_ACCOUNT_TYPE_ID.equals(request.getAccountTypeId())) {
			// there is no provider for EPF
			retirementAccount.setRetirementProvider(retirementProviderService.findById(request.getProviderId()));
		}

		if (retirementAccount.getMonthlyContribution() != null
				&& retirementAccount.getLatestContributionDate() != null) {
			retirementAccount.setLatestSavingCalculationDate(LocalDate.now());
		}

		return retirementAccountService.save(retirementAccount);
	}

	@Override
	public RetirementAccount updateRetirementAccount(RetirementAccountUpdateRequest request, String accountId,
			String userId) {
		validationService.validateRetirementSavingsAmount(request.getSavingsAmount());

		RetirementAccount existingRetirementAccount = retirementAccountService.findByIdAndUserId(accountId, userId);
		existingRetirementAccount.setSavingsAmount(request.getSavingsAmount());
		existingRetirementAccount.setMonthlyIncome(request.getMonthlyIncome());
		existingRetirementAccount.setEmployeeContributionPercentage(request.getEmployeeContributionPercentage());
		existingRetirementAccount.setEmployerContributionPercentage(request.getEmployerContributionPercentage());
		existingRetirementAccount.setMonthlyContribution(request.getMonthlyContribution());
		existingRetirementAccount.setLatestContributionDate(request.getLatestContributionDate());

		if (existingRetirementAccount.getMonthlyIncome() != null
				&& existingRetirementAccount.getLatestContributionDate() != null) {
			existingRetirementAccount.setLatestSavingCalculationDate(LocalDate.now());
		}

		return retirementAccountService.save(existingRetirementAccount);

	}

	@Override
	public NetWorthDTO calculateNetWorth(String userId) {
		List<RetirementAccount> vehicles = retirementAccountService.findAllByUserId(userId);

		return NetWorthDTO.builder().liabilities(BigDecimal.ZERO).assets(
				vehicles.stream().map(RetirementAccount::getSavingsAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
				.build();
	}

	@Override
	public void deleteRetirementAccount(String accountId, String userId) {

		validationService.validateDeleteRetirementAccount(accountId, userId);

		retirementAccountService.deleteById(accountId);
		kafkaTemplate.send(KafkaTopic.SAVING_GOALS_DELETION, jsonUtil
				.convertToString(new SavingGoalDeletionDTO(userId, accountId, SavingGoalAccountType.RETIREMENT)));

	}

}
