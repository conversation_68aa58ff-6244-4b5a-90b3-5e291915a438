package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import lombok.*;
import my.com.mandrill.utilities.converter.ProtectedDataConverterBigDecimal;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "retirement_account", uniqueConstraints = @UniqueConstraint(
		columnNames = { "user_id", "retirement_account_type_id", "retirement_provider_id" }))
public class RetirementAccount extends AuditSection {

	@Column(name = "user_id", length = 36)
	private String userId;

	@Column(name = "savings_amount", length = 100)
	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	private BigDecimal savingsAmount;

	@ManyToOne
	@JoinColumn(name = "retirement_account_type_id")
	private RetirementAccountType accountType;

	@ManyToOne
	@JoinColumn(name = "retirement_provider_id")
	private RetirementProvider retirementProvider;

}
