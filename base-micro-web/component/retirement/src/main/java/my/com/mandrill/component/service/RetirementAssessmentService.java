package my.com.mandrill.component.service;

import my.com.mandrill.component.dto.request.client.RetirementAssessmentClientRequest;
import org.springframework.http.ResponseEntity;

public interface RetirementAssessmentService {

	ResponseEntity<String> findLatestRetirementAssessmentReport(String userId);

	ResponseEntity<String> findLatestRetirementAssessmentAnswer(String userId);

	ResponseEntity<String> processRetirementAssessment(
			RetirementAssessmentClientRequest retirementAssessmentClientRequest);

}
